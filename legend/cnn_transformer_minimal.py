import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader

from typing import Tuple

class BlocksWorldDataset(Dataset):
    """
    读取 legend 目录下由 convert_log_to_samples.py 生成的 CSV，
    并按给定形状 reshape 成 (N, n_layers, n_rows, n_cols)。
    """
    def __init__(self, matrix_file: str, label_file: str, orig_shape: Tuple[int, int, int], new_shape: Tuple[int, int, int]):
        assert os.path.exists(matrix_file), f"Matrix CSV not found: {matrix_file}"
        assert os.path.exists(label_file), f"Label CSV not found: {label_file}"
        
        # 加载矩阵数据并reshape
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = self.prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        
        # 数据验证
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签并转换为CNN层索引
        labels = pd.read_csv(label_file).values
        print(f"Original labels: min={labels.min()}, max={labels.max()}")
        
        # 将动作索引转换为CNN层索引（+2，因为前两层是目标状态和当前状态）
        converted_labels = labels.copy()
        valid_mask = labels != -1
        converted_labels[valid_mask] = labels[valid_mask] + 2
        
        print(f"Converted labels (CNN layer indices): min={converted_labels.min()}, max={converted_labels.max()}")
        self.labels = converted_labels

    def prepare_data(self, data, orig_shape, new_shape):
        """数据预处理函数"""
        batch_size = data.shape[0]
        new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
        new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data
        return new_data

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels

class MinimalCNNClassifier(nn.Module):
    """
    极简版CNN分类器，专注于在小数据集上取得好的训练效果：
    - 去掉Transformer，只使用CNN
    - 使用全局平均池化
    - 大幅减少参数量
    - 强正则化
    """
    def __init__(
        self,
        n_layers=22,
        n_stacks=5,
        max_blocks=15,
        buffer_rows=0,
        buffer_cols=1,
        n_classes=20,
        dropout_rate=0.3,
    ):
        super().__init__()
        self.n_layers = n_layers
        self.n_rows = max_blocks + buffer_rows
        self.n_cols = n_stacks + buffer_cols
        self.n_classes = n_classes

        # 极简CNN架构
        self.cnn = nn.Sequential(
            # 第一层：大幅减少通道数
            nn.Conv2d(n_layers, 32, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Dropout2d(p=dropout_rate),
            
            # 第二层：进一步特征提取
            nn.Conv2d(32, 64, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout2d(p=dropout_rate),
            
            # 全局平均池化，大幅减少参数
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        # 简单的分类头
        self.classifier = nn.Sequential(
            nn.Dropout(p=dropout_rate),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate),
        )
        
        # 双头输出
        self.fc_best = nn.Linear(32, n_classes)
        self.fc_worst = nn.Linear(32, n_classes)
        
        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # x shape: (batch_size, n_layers, n_rows, n_cols)
        batch_size = x.size(0)
        
        # CNN特征提取
        features = self.cnn(x)  # (batch_size, 64, 1, 1)
        features = features.view(batch_size, -1)  # (batch_size, 64)
        
        # 分类器
        hidden = self.classifier(features)  # (batch_size, 32)
        
        # 双头输出
        best_logits = self.fc_best(hidden)
        worst_logits = self.fc_worst(hidden)
        
        return best_logits, worst_logits

class SimpleCNNClassifier(nn.Module):
    """
    简单CNN分类器，比MinimalCNN稍微复杂一点但仍然很轻量
    """
    def __init__(
        self,
        n_layers=22,
        n_stacks=5,
        max_blocks=15,
        buffer_rows=0,
        buffer_cols=1,
        n_classes=20,
        dropout_rate=0.2,
    ):
        super().__init__()
        self.n_layers = n_layers
        self.n_rows = max_blocks + buffer_rows
        self.n_cols = n_stacks + buffer_cols
        self.n_classes = n_classes

        # 简单CNN架构，保留更多空间信息
        self.features = nn.Sequential(
            # 第一层
            nn.Conv2d(n_layers, 64, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate),
            
            # 第二层
            nn.Conv2d(64, 128, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate),
            
            # 轻微池化
            nn.MaxPool2d(kernel_size=(2, 1), stride=(2, 1)),
            
            # 第三层
            nn.Conv2d(128, 256, kernel_size=(2, 1), padding=(0, 0)),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate),
            
            # 全局平均池化
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Dropout(p=dropout_rate),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(p=dropout_rate),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(p=dropout_rate),
        )
        
        # 双头输出
        self.fc_best = nn.Linear(64, n_classes)
        self.fc_worst = nn.Linear(64, n_classes)
        
        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # x shape: (batch_size, n_layers, n_rows, n_cols)
        batch_size = x.size(0)
        
        # 特征提取
        features = self.features(x)  # (batch_size, 256, 1, 1)
        features = features.view(batch_size, -1)  # (batch_size, 256)
        
        # 分类器
        hidden = self.classifier(features)  # (batch_size, 64)
        
        # 双头输出
        best_logits = self.fc_best(hidden)
        worst_logits = self.fc_worst(hidden)
        
        return best_logits, worst_logits