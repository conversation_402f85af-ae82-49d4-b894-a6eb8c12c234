"""
不同编码方案的深度对比
"""
import numpy as np

print("=" * 70)
print("编码方案对比：6栈15块")
print("=" * 70)

# 示例状态
state = {
    'stack1': ['A', 'B', 'C'],
    'stack2': ['D'],
    'stack3': [],
    'stack4': ['E', 'F'],
    'stack5': ['G', 'H', 'I', 'J'],
    'stack6': ['K']
}

print("\n方案1: 位置编码 (15×6)")
print("-" * 40)
print("matrix[height, stack] = 1 表示该位置有块")
print("\n优点：")
print("  - 简单直观")
print("  - 空间紧凑")
print("\n缺点：")
print("  - 丢失块身份信息")
print("  - CNN难以追踪特定块")
print("  - 无法直接表达块的可移动性")

print("\n方案2: 块-栈编码 (15×6)")
print("-" * 40)
print("matrix[block_id, stack] = height 表示块在栈中的高度")
print("\n优点：")
print("  - 保留块身份")
print("  - 直接表达块在哪个栈")
print("  - 高度信息隐含可移动性")
print("\n缺点：")
print("  - 需要预定义块的顺序")
print("  - 高度信息需要额外处理")

print("\n方案3: 多通道编码 (3×15×6)")
print("-" * 40)
print("通道0: 块-栈隶属 (0/1)")
print("通道1: 块在栈中的高度")
print("通道2: 块是否可移动")
print("\n优点：")
print("  - 信息最完整")
print("  - 显式表达所有关键信息")
print("  - CNN可以选择性关注不同通道")
print("\n缺点：")
print("  - 数据量较大")
print("  - 需要更复杂的预处理")

print("\n方案4: 栈独立编码 (6×15×1)")
print("-" * 40)
print("每个栈作为独立通道，值为块ID")
print("\n优点：")
print("  - 栈之间完全独立")
print("  - 保留块身份和顺序")
print("  - 自然匹配问题结构")
print("\n缺点：")
print("  - 稀疏（大部分为0）")
print("  - 需要特殊的CNN设计")

print("\n" + "=" * 70)
print("推荐方案：混合编码")
print("=" * 70)
print("\n使用 (2, 15, 6) 的编码：")
print("- 通道0: 块-栈隶属矩阵（块ID × 栈ID）")
print("- 通道1: 高度/可移动性信息")
print("\n这样CNN可以：")
print("1. 从通道0学习块的分布")
print("2. 从通道1学习动作可行性")
print("3. 结合两者做出决策")
