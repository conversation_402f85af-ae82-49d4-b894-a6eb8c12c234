"""
比较不同的状态编码方式
"""
import numpy as np

def demo_encodings():
    # 示例状态
    state = {
        'stack1': ['A', 'B', 'C'],  # A在底，C在顶
        'stack2': ['D'],
        'stack3': [],
        'stack4': ['E', 'F'],  # E在底，F在顶
        'stack5': []
    }
    
    all_blocks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O']
    n_blocks = 15
    n_stacks = 5
    
    print("=" * 60)
    print("示例状态:")
    for stack_name, blocks in state.items():
        if blocks:
            print(f"  {stack_name}: {' -> '.join(blocks)} (底 -> 顶)")
    print()
    
    # ========== 当前编码方式 ==========
    print("=" * 60)
    print("【当前编码】位置编码 (Position Encoding)")
    print("-" * 60)
    matrix_current = np.zeros((n_blocks, n_stacks + 1))
    
    for stack_idx, (stack_name, blocks) in enumerate(state.items()):
        if stack_idx < n_stacks:
            for pos, block in enumerate(blocks):
                if pos < n_blocks:
                    matrix_current[pos, stack_idx] = 1
    
    print("矩阵 (行=高度位置, 列=栈):")
    print("     s1 s2 s3 s4 s5 buf")
    for i in range(6):
        row = matrix_current[i].astype(int)
        print(f"pos{i}: {row}")
    
    print("\n特点:")
    print("- 每列表示一个栈的占用情况")
    print("- 行索引表示在栈中的高度（0=底部）")
    print("- 值1表示该位置有块")
    print("\n问题:")
    print("❌ 无法直接知道具体是哪个块")
    print("❌ CNN需要通过位置关系推断块的身份")
    print("❌ 对于判断'块A在哪个栈'这种查询，需要复杂的推理")
    
    # ========== 改进编码方式 ==========
    print("\n" + "=" * 60)
    print("【改进编码】块-栈隶属编码 (Block-Stack Membership)")
    print("-" * 60)
    matrix_improved = np.zeros((n_blocks, n_stacks + 1))
    
    for stack_idx, (stack_name, blocks) in enumerate(state.items()):
        if stack_idx < n_stacks:
            for block in blocks:
                if block in all_blocks:
                    block_idx = all_blocks.index(block)
                    matrix_improved[block_idx, stack_idx] = 1
    
    print("矩阵 (行=块ID, 列=栈):")
    print("      s1 s2 s3 s4 s5 buf")
    for i in range(6):
        block_name = all_blocks[i]
        row = matrix_improved[i].astype(int)
        print(f"块{block_name}: {row}")
    
    print("\n特点:")
    print("- 每行表示一个特定的块")
    print("- 每列表示一个栈")
    print("- 值1表示该块在该栈中")
    print("\n优势:")
    print("✓ 直接编码'块X在栈Y中'的信息")
    print("✓ CNN可以直接学习块与栈的关系")
    print("✓ 更适合学习动作（从栈X移动块Y到栈Z）")
    
    # ========== 混合编码方式 ==========
    print("\n" + "=" * 60)
    print("【混合编码】块-栈隶属 + 高度信息")
    print("-" * 60)
    print("可以使用两个通道:")
    print("- 通道1: 块-栈隶属关系（如上）")
    print("- 通道2: 块在栈中的高度")
    
    matrix_height = np.zeros((n_blocks, n_stacks + 1))
    for stack_idx, (stack_name, blocks) in enumerate(state.items()):
        if stack_idx < n_stacks:
            for height, block in enumerate(blocks):
                if block in all_blocks:
                    block_idx = all_blocks.index(block)
                    matrix_height[block_idx, stack_idx] = height + 1  # 1-based高度
    
    print("\n高度矩阵 (行=块ID, 列=栈):")
    print("      s1 s2 s3 s4 s5 buf")
    for i in range(6):
        block_name = all_blocks[i]
        row = matrix_height[i].astype(int)
        print(f"块{block_name}: {row}")
    
    print("\n解读:")
    print("- 块A: 在stack1的高度1（底部）")
    print("- 块B: 在stack1的高度2")
    print("- 块C: 在stack1的高度3（顶部）")
    print("- 块E: 在stack4的高度1（底部）")
    print("\n这样CNN可以同时获得:")
    print("✓ 块在哪个栈（隶属关系）")
    print("✓ 块在栈中的位置（高度信息）")
    print("✓ 更容易学习哪些块可以移动（顶部的块）")

if __name__ == "__main__":
    demo_encodings()
