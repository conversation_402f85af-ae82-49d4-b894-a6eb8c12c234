#!/usr/bin/env python3
"""
CNN-Transformer模型指导A*搜索
使用训练好的CNN-Transformer模型来指导A*算法的搜索过程
"""

import os
import json
import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
import re

from cnn_transformer_legend import CNNTransformerClassifier

class CNNTransformerGuidance:
    """
    使用训练好的CNN-Transformer模型指导A*搜索的类
    """
    
    def __init__(self, model_path: str, metadata_path: str = None):
        """
        初始化CNN-Transformer指导系统
        
        Args:
            model_path: 训练好的模型文件路径
            metadata_path: 模型元数据文件路径，如果为None则自动推断
        """
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 自动推断metadata路径（优先使用 metadata_fixed.json，其次 metadata.json）
        if metadata_path is None:
            model_dir = os.path.dirname(model_path)
            candidates = [
                os.path.join(model_dir, 'metadata_fixed.json'),
                os.path.join(model_dir, 'metadata.json')
            ]
            metadata_path = None
            for cand in candidates:
                if os.path.exists(cand):
                    metadata_path = cand
                    break
            if metadata_path is None:
                raise FileNotFoundError('Cannot find metadata_fixed.json or metadata.json next to model file')

        # 加载模型元数据
        with open(metadata_path, 'r') as f:
            self.metadata = json.load(f)

        # 初始化模型（与训练时参数保持一致）
        self.model = CNNTransformerClassifier(
            n_layers=self.metadata['n_layers'],
            n_stacks=5,
            max_blocks=15,
            buffer_rows=0,
            buffer_cols=1,
            embed_dim=self.metadata['embed_dim'],
            n_heads=self.metadata['n_heads'],
            n_hidden=self.metadata['n_hidden'],
            n_classes=self.metadata['n_classes'],
            num_transformer_layers=self.metadata['num_transformer_layers'],
            classifier_dropout_rate=self.metadata['classifier_dropout']
        )
        
        # 加载训练好的权重
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint)
        self.model.to(self.device)
        self.model.eval()
        
        print(f"✅ 成功加载CNN-Transformer模型: {model_path}")
        print(f"   - 设备: {self.device}")
        print(f"   - 模型参数: {self.metadata['n_layers']}层, {self.metadata['n_rows']}x{self.metadata['n_cols']}")
        
    def _build_input_and_actions(self, current_state: Dict, goal_state: Dict, planner) -> Tuple[np.ndarray, List[Tuple[int, int]]]:
        """
        按训练数据的语义构造输入：
        - 第0层：goal 状态占用矩阵
        - 第1层：current 状态占用矩阵
        - 第2..21层：20个规范顺序动作执行后的后继状态占用矩阵
        返回：(input_matrix, action_list)，其中 action_list 为[(from_stack,to_stack)]*20，1-based。
        使用 planner.state.stacks 的键顺序来定义栈编号，确保与 A* 中 successors 的动作编号一致。
        """
        n_layers = self.metadata['n_layers']
        n_rows = self.metadata['n_rows']
        n_cols = self.metadata['n_cols']

        matrix = np.zeros((n_layers, n_rows, n_cols), dtype=np.float32)

        # 使用 planner 的栈顺序（与 GraphPlanningBlocksWorld.get_successors 保持一致）
        if planner is not None and hasattr(planner, 'state') and hasattr(planner.state, 'stacks'):
            active_stacks = list(planner.state.stacks.keys())[:5]
        else:
            # 退化：按字典顺序
            active_stacks = list(sorted(current_state.keys()))[:5]

        def encode_occupancy(state_dict: Dict) -> np.ndarray:
            mat = np.zeros((n_rows, n_cols), dtype=np.float32)
            for s_idx, stack_name in enumerate(active_stacks):
                blocks = state_dict.get(stack_name, [])
                for pos, _ in enumerate(blocks):
                    if pos < n_rows and s_idx < n_cols:
                        mat[pos, s_idx] = 1.0
            return mat

        def apply_action(state_dict: Dict, action: Tuple[int, int]) -> Dict:
            fs, ts = action
            new_state = {k: v.copy() for k, v in state_dict.items()}
            if 1 <= fs <= len(active_stacks) and 1 <= ts <= len(active_stacks):
                src = active_stacks[fs - 1]
                dst = active_stacks[ts - 1]
                if new_state.get(src):
                    block = new_state[src].pop()
                    new_state.setdefault(dst, []).append(block)
            return new_state

        # 层0/1
        matrix[0] = encode_occupancy(goal_state)
        matrix[1] = encode_occupancy(current_state)

        # 生成20个规范动作 (from!=to，1..5)
        action_list: List[Tuple[int, int]] = []
        for fs in range(1, 6):
            for ts in range(1, 6):
                if fs == ts:
                    continue
                action_list.append((fs, ts))

        # 填充后继层
        for k, act in enumerate(action_list):
            layer_idx = 2 + k
            if layer_idx >= n_layers:
                break
            succ = apply_action(current_state, act)
            matrix[layer_idx] = encode_occupancy(succ)

        return matrix, action_list
    
    def evaluate_actions(self, current_state, goal_state, planner, successors: List[Tuple]) -> Dict[str, Any]:
        """
        评估所有可能的动作，返回最佳和最差动作
        
        Args:
            current_state: 当前状态（可以是字典或State对象）
            goal_state: 目标状态（可以是字典或State对象）
            planner: 规划器实例（未使用）
            successors: [(next_state, action), ...] 后继状态和动作列表
            
        Returns:
            包含最佳和最差动作信息的字典
        """
        if not successors:
            return {"best_action": 0, "worst_action": 0, "confidence": 0.0}
        
        # 处理State对象，提取stacks字典
        if hasattr(current_state, 'stacks'):
            current_state_dict = current_state.stacks
        else:
            current_state_dict = current_state
            
        if hasattr(goal_state, 'stacks'):
            goal_state_dict = goal_state.stacks
        else:
            goal_state_dict = goal_state
        
        # 构造单次前向所需的输入（与训练一致）
        input_matrix, canonical_actions = self._build_input_and_actions(current_state_dict, goal_state_dict)
        input_tensor = torch.tensor(input_matrix, dtype=torch.float32).unsqueeze(0).to(self.device)

        with torch.no_grad():
            best_logits, worst_logits = self.model(input_tensor)
            best_probs = torch.softmax(best_logits, dim=1)
            worst_probs = torch.softmax(worst_logits, dim=1)
            best_pred_idx = torch.argmax(best_probs, dim=1).item()
            worst_pred_idx = torch.argmax(worst_probs, dim=1).item()
            best_confidence = torch.max(best_probs, dim=1)[0].item()
            worst_confidence = torch.max(worst_probs, dim=1)[0].item()

        # 将预测类别索引映射到规范动作元组
        best_action_tuple = canonical_actions[best_pred_idx] if 0 <= best_pred_idx < len(canonical_actions) else None
        worst_action_tuple = canonical_actions[worst_pred_idx] if 0 <= worst_pred_idx < len(canonical_actions) else None

        # successors 的动作格式可能是 (from_stack, to_stack) 或包含块信息的三元组；
        # 这里以 (from_stack, to_stack) 进行对齐，如果不同则仅用于日志显示
        # 在 A* 集成时（limited_astar_guidance.py）采用 i 索引匹配，因此这里返回 1-based 类别索引即可。

        # 汇总可视化信息（非必要，用于调试/展示）
        action_scores = []
        for idx, act in enumerate(canonical_actions):
            action_scores.append({
                'action_idx': idx,
                'action': act,
                'best_pred': int(best_pred_idx),
                'worst_pred': int(worst_pred_idx),
                'best_confidence': float(best_confidence) if idx == best_pred_idx else 0.0,
                'worst_confidence': float(worst_confidence) if idx == worst_pred_idx else 0.0,
                'combined_score': float(best_confidence - worst_confidence) if idx in (best_pred_idx, worst_pred_idx) else 0.0,
            })

        def format_action(action):
            return f"move from Stack{action[0]} to Stack{action[1]}" if isinstance(action, tuple) else str(action)

        result = {
            "best_action": int(best_pred_idx),  # 0-based index to match limited_astar_guidance.py branch
            "best_reason": f"模型预测最优动作 (置信度: {best_confidence:.3f}): {format_action(best_action_tuple)}",
            "worst_action": int(worst_pred_idx),
            "worst_reason": f"模型预测最差动作 (置信度: {worst_confidence:.3f}): {format_action(worst_action_tuple)}",
            "confidence": float(best_confidence),
            "all_scores": action_scores
        }
        
        # 为了兼容原始A*搜索接口，返回4个值：llm_result, actions, current_issue, priority
        actions = [action for _, action in successors]
        current_issue = "CNN-Transformer模型分析"
        priority = f"最佳动作: {format_action(best_action_info['action'])}"
        
        return result, actions, current_issue, priority

# 为了兼容性，提供别名
CNNTransformerGuidance = CNNTransformerGuidance