import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader

from typing import Tuple

class BlocksWorldDataset(Dataset):
    """
    读取 legend 目录下由 convert_log_to_samples.py 生成的 CSV，
    并按给定形状 reshape 成 (N, n_layers, n_rows, n_cols)。
    与原版cnn_transformer_modify.py的BlocksWorldDataset保持一致。
    """
    def __init__(self, matrix_file: str, label_file: str, orig_shape: Tuple[int, int, int], new_shape: Tuple[int, int, int]):
        assert os.path.exists(matrix_file), f"Matrix CSV not found: {matrix_file}"
        assert os.path.exists(label_file), f"Label CSV not found: {label_file}"
        
        # 加载矩阵数据并reshape
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = self.prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        
        # 数据验证
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签并转换为CNN层索引
        labels = pd.read_csv(label_file).values
        print(f"Original labels: min={labels.min()}, max={labels.max()}")
        
        # 将动作索引转换为CNN层索引（+2，因为前两层是目标状态和当前状态）
        # 保持-1标签不变（表示无效标签）
        converted_labels = labels.copy()
        valid_mask = labels != -1
        converted_labels[valid_mask] = labels[valid_mask] + 2
        
        print(f"Converted labels (CNN layer indices): min={converted_labels.min()}, max={converted_labels.max()}")
        self.labels = converted_labels

    def prepare_data(self, data, orig_shape, new_shape):
        """数据预处理函数，与原版保持一致"""
        batch_size = data.shape[0]
        new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
        new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data
        return new_data

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels

class SimplifiedCNNTransformerClassifier(nn.Module):
    """
    简化版CNN-Transformer架构，专注于在小数据集上取得好的训练效果：
    - 保守的卷积：使用更小的卷积核，减少池化
    - 增大嵌入维度：提供更丰富的表示能力
    - 简化Transformer：只使用1-2层，减少过拟合
    - 保留空间信息：最小化信息丢失
    """
    def __init__(
        self,
        n_layers=22,
        n_stacks=5,           # 实际栈数
        max_blocks=15,        # 最大块数
        buffer_rows=0,        # 缓冲行数（可选）
        buffer_cols=1,        # 缓冲列数（可选，默认1）
        embed_dim=128,        # 增大嵌入维度
        n_heads=8,
        n_hidden=512,         # 增大隐藏层
        n_classes=20,
        num_transformer_layers=2,  # 简化transformer层数
        classifier_dropout_rate=0.1,
    ):
        super().__init__()
        # 动态计算实际尺寸
        self.n_layers = n_layers
        self.n_rows = max_blocks + buffer_rows
        self.n_cols = n_stacks + buffer_cols
        self.n_stacks = n_stacks
        self.max_blocks = max_blocks
        self.embed_dim = embed_dim
        self.classifier_dropout_rate = classifier_dropout_rate
        self.max_actions_layers = n_layers - 2

        # 保守的CNN架构：最小化信息丢失
        self.cnn = nn.Sequential(
            # 第一层：使用更小的卷积核，保留更多空间信息
            nn.Conv2d(n_layers, 64, kernel_size=(2, 1), padding=(0, 0)),  # 减小卷积核
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout(p=0.1),  # 减少dropout
            
            # 第二层：继续保守的卷积
            nn.Conv2d(64, 128, kernel_size=(2, 1), padding=(0, 0)),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            nn.Dropout(p=0.1),
            
            # 非常保守的池化：只在必要时池化
            # 不使用池化，保留所有空间信息
        )

        # Calculate sequence length after CNN
        with torch.no_grad():
            dummy_input = torch.zeros(1, n_layers, self.n_rows, self.n_cols)
            cnn_output_shape = self.cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]  # 128
            self.cnn_out_h = cnn_output_shape[2]  # n_rows - 2
            self.cnn_out_w = cnn_output_shape[3]  # n_cols (保持不变)
            self.sequence_length = self.cnn_out_h * self.cnn_out_w
            
            print(f"CNN output shape: {cnn_output_shape}")
            print(f"Sequence length: {self.sequence_length}")

        # Linear projection from CNN channel dim to Transformer embed_dim
        self.input_proj = nn.Linear(self.cnn_out_channels, embed_dim)

        # Positional embedding for the sequence
        self.pos_embed = nn.Parameter(torch.zeros(1, self.sequence_length, embed_dim))

        # 简化的Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim, 
            nhead=n_heads, 
            dim_feedforward=n_hidden, 
            batch_first=True,
            dropout=0.1,  # 减少dropout
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # 分类头部之前的Dropout层
        self.classifier_dropout = nn.Dropout(p=self.classifier_dropout_rate)

        # Classification heads (using the aggregated output)
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)

        self._initialize_weights()

    def _initialize_weights(self):
        # Initialize positional embedding
        nn.init.normal_(self.pos_embed, std=.02)

        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # x shape: (batch_size, n_layers, n_rows, n_cols)
        batch_size = x.size(0)
        
        # CNN feature extraction
        cnn_out = self.cnn(x)  # (batch_size, cnn_out_channels, cnn_out_h, cnn_out_w)
        
        # Reshape for transformer: (batch_size, sequence_length, cnn_out_channels)
        cnn_out = cnn_out.view(batch_size, self.cnn_out_channels, -1).transpose(1, 2)
        
        # Project to embedding dimension
        transformer_input = self.input_proj(cnn_out)  # (batch_size, sequence_length, embed_dim)
        
        # Add positional embedding
        transformer_input = transformer_input + self.pos_embed
        
        # Transformer encoding
        transformer_out = self.transformer(transformer_input)  # (batch_size, sequence_length, embed_dim)
        
        # Global average pooling over sequence dimension
        pooled_output = transformer_out.mean(dim=1)  # (batch_size, embed_dim)
        
        # Apply dropout before classification
        pooled_output = self.classifier_dropout(pooled_output)
        
        # Classification
        best_logits = self.fc_best(pooled_output)
        worst_logits = self.fc_worst(pooled_output)
        
        return best_logits, worst_logits