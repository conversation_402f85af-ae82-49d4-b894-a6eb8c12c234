import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader

from typing import Tuple

class BlocksWorldDataset(Dataset):
    """
    读取 legend 目录下由 convert_log_to_samples.py 生成的 CSV，
    并按给定形状 reshape 成 (N, n_layers, n_rows, n_cols)。
    支持完整的22,21,25 one-hot编码格式。
    """
    def __init__(self, matrix_file: str, label_file: str, orig_shape: Tuple[int, int, int], new_shape: Tuple[int, int, int]):
        assert os.path.exists(matrix_file), f"Matrix CSV not found: {matrix_file}"
        assert os.path.exists(label_file), f"Label CSV not found: {label_file}"
        
        # 加载矩阵数据并reshape
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = self.prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        
        # 数据验证
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签（保持为动作类别索引 0..19，不做偏移转换）
        labels = pd.read_csv(label_file).values
        print(f"Labels loaded: min={labels.min()}, max={labels.max()}")
        self.labels = labels

    def prepare_data(self, data, orig_shape, new_shape):
        """数据预处理函数，支持形状自适应"""
        batch_size = data.shape[0]
        new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
        
        # 复制原始数据到新形状的对应位置
        min_layers = min(orig_shape[0], new_shape[0])
        min_rows = min(orig_shape[1], new_shape[1])
        min_cols = min(orig_shape[2], new_shape[2])
        
        new_data[:, :min_layers, :min_rows, :min_cols] = data[:, :min_layers, :min_rows, :min_cols]
        return new_data

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels

class CNNTransformerClassifier(nn.Module):
    """
    恢复完整感知的CNN-Transformer架构：
    - 使用标准(3,3)卷积核，支持完整的空间感知
    - 支持22,21,25的完整one-hot编码格式
    - 保留块间关系、栈间关系的完整信息
    - 双头分类（best, worst）+ Transformer编码器
    
    优势：
    - 完整表达能力：保留所有逻辑和空间关系
    - 跨栈感知：能够理解栈间的空间布局
    - 关系建模：完整的on-block和clear关系表示
    """
    def __init__(
        self,
        n_layers=22,
        n_rows=21,            # 标准：20个块 + 1个clear行
        n_cols=25,            # 标准：20个块 + 5个栈
        embed_dim=64,
        n_heads=8,
        n_hidden=256,
        n_classes=20,
        num_transformer_layers=6,
        classifier_dropout_rate=0.1,
    ):
        super().__init__()
        # 保存配置参数
        self.n_layers = n_layers
        self.n_rows = n_rows
        self.n_cols = n_cols
        self.embed_dim = embed_dim
        self.classifier_dropout_rate = classifier_dropout_rate
        self.max_actions_layers = n_layers - 2

        # 恢复完整感知的CNN架构：使用标准卷积核
        self.cnn = nn.Sequential(
            # 第一层：标准卷积，允许完整的空间感知
            nn.Conv2d(n_layers, 32, kernel_size=(3, 3), padding=(1, 1)),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            
            # 第二层：继续标准卷积
            nn.Conv2d(32, 64, kernel_size=(3, 3), padding=(1, 1)),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            
            # 标准池化，在所有方向上池化
            nn.MaxPool2d(kernel_size=2, stride=2)
            # Output shape: (batch_size, 64, (n_rows)//2, (n_cols)//2)
        )

        # 动态计算CNN输出形状
        with torch.no_grad():
            dummy_input = torch.zeros(1, n_layers, self.n_rows, self.n_cols)
            cnn_output_shape = self.cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]  # 64
            self.cnn_out_h = cnn_output_shape[2]  # (n_rows) // 2
            self.cnn_out_w = cnn_output_shape[3]  # (n_cols) // 2
            self.sequence_length = self.cnn_out_h * self.cnn_out_w

        print(f"CNN output shape: {cnn_output_shape}")
        print(f"Sequence length for Transformer: {self.sequence_length}")

        # 线性投影：从CNN通道维度到Transformer嵌入维度
        self.input_proj = nn.Linear(self.cnn_out_channels, embed_dim)

        # 位置编码
        self.pos_embed = nn.Parameter(torch.zeros(1, self.sequence_length, embed_dim))

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim, nhead=n_heads, dim_feedforward=n_hidden, batch_first=True,
            dropout=0.3,  # 提升泛化性
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # 分类头前的Dropout
        self.classifier_dropout = nn.Dropout(p=self.classifier_dropout_rate)

        # 双分类头
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)

        self._initialize_weights()

    def _initialize_weights(self):
        """权重初始化"""
        # 位置编码初始化
        nn.init.normal_(self.pos_embed, std=.02)

        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        batch_size = x.size(0)

        # 1. CNN特征提取（完整空间感知）
        x = self.cnn(x)  # Shape: (batch_size, 64, H//2, W//2)

        # 2. 为Transformer重塑数据
        x = x.flatten(2)  # Shape: (batch_size, 64, H*W)
        x = x.transpose(1, 2)  # Shape: (batch_size, H*W, 64)

        # 3. 投影到嵌入维度
        x = self.input_proj(x)  # Shape: (batch_size, H*W, embed_dim)

        # 4. 添加位置编码
        x = x + self.pos_embed  # Shape: (batch_size, H*W, embed_dim)

        # 5. Transformer编码
        x = self.transformer(x)  # Shape: (batch_size, H*W, embed_dim)

        # 6. 全局平均池化
        x = x.mean(dim=1)  # Shape: (batch_size, embed_dim)

        # 7. 分类前Dropout
        x = self.classifier_dropout(x)

        # 8. 双头分类
        best_logits = self.fc_best(x)  # Shape: (batch_size, n_classes)
        worst_logits = self.fc_worst(x)  # Shape: (batch_size, n_classes)

        return best_logits, worst_logits

    def freeze_cnn_layers(self, freeze_first=True, freeze_second=False):
        """冻结CNN层"""
        for name, param in self.cnn.named_parameters():
            # 冻结第一个卷积层
            if freeze_first and (name.startswith("0.")):
                param.requires_grad = False
                print(f"Frozen CNN layer: {name}")
            # 冻结第二个卷积层
            if freeze_second and (name.startswith("4.")):
                param.requires_grad = False
                print(f"Frozen CNN layer: {name}")

    def load_state_dict(self, state_dict, strict=False, orig_n_rows=21, orig_n_cols=25):
        """
        自定义的state_dict加载器，支持位置编码和分类头的自适应处理
        """
        # --- 1. 处理位置编码 ---
        ckpt_pos_embed = state_dict.get('pos_embed', None)
        if ckpt_pos_embed is not None and self.pos_embed.shape != ckpt_pos_embed.shape:
            print(f"位置编码尺寸不匹配。Checkpoint: {ckpt_pos_embed.shape}, Model: {self.pos_embed.shape}")
            print("正在进行位置编码插值...")
            
            with torch.no_grad():
                # 计算原始CNN输出形状
                device = next(self.parameters()).device
                dummy_cnn = self.cnn
                dummy_input = torch.zeros(1, self.n_layers, orig_n_rows, orig_n_cols).to(device)
                orig_cnn_out_shape = dummy_cnn(dummy_input).shape
                orig_h, orig_w = orig_cnn_out_shape[2], orig_cnn_out_shape[3]
            
            # 进行2D插值
            pos_embed_ckpt_2d = ckpt_pos_embed.reshape(1, orig_h, orig_w, self.embed_dim).permute(0, 3, 1, 2)
            new_h, new_w = self.cnn_out_h, self.cnn_out_w
            print(f"从 {orig_h}x{orig_w} 插值到 {new_h}x{new_w}")
            resized_pos_embed = F.interpolate(pos_embed_ckpt_2d, size=(new_h, new_w), mode='bicubic', align_corners=False)
            resized_pos_embed = resized_pos_embed.permute(0, 2, 3, 1).flatten(1, 2)
            state_dict['pos_embed'] = resized_pos_embed
        
        # --- 2. 处理分类头 ---
        model_head_shape = self.fc_best.weight.shape
        ckpt_head_shape = state_dict.get('fc_best.weight', None).shape if 'fc_best.weight' in state_dict else None
        
        if ckpt_head_shape is not None and model_head_shape != ckpt_head_shape:
            print(f"分类头尺寸不匹配。Checkpoint: {ckpt_head_shape[0]} classes, Model: {model_head_shape[0]} classes.")
            print("重新初始化分类头权重")
            state_dict.pop('fc_best.weight', None)
            state_dict.pop('fc_best.bias', None)
            state_dict.pop('fc_worst.weight', None)
            state_dict.pop('fc_worst.bias', None)
        
        # --- 3. 最终加载 ---
        super().load_state_dict(state_dict, strict=False)
        print("✅ 模型权重加载完成（恢复完整感知版本）")

