# Guidance Model Experiments Report

Date: 2025-08-18
Repo: legend/

## 1. Objective

Train a guidance model for Blocks World to predict:
- Best next action (primary objective)
- Worst next action (auxiliary, down-weighted)

Target: Achieve stable and measurable gains on Best head (Top-1 and Top-k), under small data and class imbalance.

---

## 2. Data Pipeline

Source logs:
- legend/llm_guidance_logs/instance_001_llm_log.txt
- legend/llm_guided_test_log.txt

Converter: legend/convert_log_to_samples_optimized.py
- Added multi-log support (args: --log_dir, --extra_logs, --output_prefix)
- Maintains 22-layer interface: layer 0=goal, 1=current, 2..21=action-based successor occupancy
- Position enhancement on layer 1 only (does not alter occupancy semantics):
  - FIX_STACK_STABLE_PART, OTHER_PROBLEMATIC_BLOCKS, PRIORITY_TASK_BLOCK
- Action space: 5 stacks → 5*4=20 actions (0..19)

Run (done):
```
python legend/convert_log_to_samples_optimized.py \
  --log_dir legend/llm_guidance_logs \
  --extra_logs legend/llm_guided_test_log.txt \
  --output_prefix legend/optimized_llm_guided_samples
```
Output (726 samples):
- legend/optimized_llm_guided_samples_matrix.csv
- legend/optimized_llm_guided_samples_labels.csv

Label coverage:
- best: min=-1, max=16 (missing classes 17..19)
- worst: many -1 (missing), heavy skew

---

## 3. Diagnostics & Visualization

Tool: legend/diagnostics_guidance.py
- Generates: label_stats_overall.json, label_stats_splits.json
- Evaluates model on val/test (Top-k for best/worst), outputs model_metrics.json
- Saves best-head confusion matrix (PNG+NumPy) if matplotlib available
- Enhancements: --metadata, --model_path to evaluate a specific model

Typical usage:
```
python legend/diagnostics_guidance.py \
  --metadata legend/models/metadata_t3_d03_wcoef010.json \
  --model_path legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth
```

Reports created in this session:
- legend/reports/20250818_194312 (default baseline check)
- legend/reports/20250818_194507 (wcls)
- legend/reports/20250818_205302 (wcoef010)

Splits (sequential): train=509, val=145, test=72

---

## 4. Training Script Changes

Script: legend/train_guidance_model_fixed.py
- Separate loss heads + configurable weights:
  - criterion_best and criterion_worst
  - worst_loss_coef (default 0.5)
- Fixed class weights for Best head:
  - Only weight classes present in training set; unseen classes weight=1
  - Weights normalized to mean=1; label smoothing preserved
- Early stopping & scheduler:
  - Scheduler stays on val_loss
  - Early-stop metric selectable: --early_stop_metric [val_loss|best_acc]
- Optional Weighted Sampler (per-sample weights from Best labels): --use_weighted_sampler
- CLI params added: --use_class_weights_best, --worst_loss_coef, --early_stop_metric, --use_weighted_sampler

Shape infer kept from CSV header (22, 15, 6) for these runs.

---

## 5. Experiments & Results

Common config (unless stated):
- num_transformer_layers=3, classifier_dropout=0.3, embed_dim=64, n_heads=4, n_hidden=128
- batch_size=64, patience=40, lr=5e-5, weight_decay=3e-4
- Sequential split; label smoothing=0.1

Note on metrics: Best/Worst Top-1, Top-3, Top-5 on test set; totals: best_total=38, worst_total=34 for this data.

### 5.1 WCLS (initial, buggy class weights)
Command:
```
python legend/train_guidance_model_fixed.py \
  --epochs 200 --batch_size 64 --patience 40 \
  --num_transformer_layers 3 --classifier_dropout 0.3 \
  --embed_dim 64 --n_heads 4 --n_hidden 128 \
  --use_class_weights_best --worst_loss_coef 0.25 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcls.pth \
  --metadata_out legend/models/metadata_t3_d03_wcls.json
```
Outcome:
- Loss values extremely large (due to unseen-class weights exploding)
- Test: Best Top-1=0.0526, Worst Top-1=0.1176
- Diagnostics: legend/reports/20250818_194507/model_metrics.json
Root cause: Unseen classes were given extreme weights; fixed later.

### 5.2 WCLS_FIX (class weights fixed), worst_loss_coef=0.25
Command:
```
python legend/train_guidance_model_fixed.py \
  --epochs 200 --batch_size 64 --patience 40 \
  --num_transformer_layers 3 --classifier_dropout 0.3 \
  --embed_dim 64 --n_heads 4 --n_hidden 128 \
  --use_class_weights_best --worst_loss_coef 0.25 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcls_fix.pth \
  --metadata_out legend/models/metadata_t3_d03_wcls_fix.json
```
Outcome:
- Early stop at epoch ~171
- Test: Best Top-1=0.2105, Worst Top-1=0.7353
Interpretation: Best improves to majority baseline level; Worst head highly biased (expected), may over-regularize Best.

### 5.3 BEST-ONLY (worst_loss_coef=0.0)
```
python legend/train_guidance_model_fixed.py ... \
  --use_class_weights_best --worst_loss_coef 0.0 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcls_bestonly.pth \
  --metadata_out legend/models/metadata_t3_d03_wcls_bestonly.json
```
Outcome:
- Early stop at epoch ~118
- Test: Best Top-1=0.1842, Worst Top-1=0.0882
Interpretation: Removing Worst supervision slightly hurts Best vs 0.25; some auxiliary benefit exists.

### 5.4 ROUTE-C (early_stop_metric=best_acc, worst_loss_coef=0.10)
```
python legend/train_guidance_model_fixed.py ... \
  --use_class_weights_best --worst_loss_coef 0.10 \
  --early_stop_metric best_acc \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_routeC.pth \
  --metadata_out legend/models/metadata_t3_d03_routeC.json
```
Outcome:
- Early stop at epoch ~77
- Test: Best Top-1=0.0789
Interpretation: best_acc as early-stop metric is noisy here → regression; keep val_loss.

### 5.5 B3 (RECOMMENDED): val_loss early stop + worst_loss_coef=0.10
```
python legend/train_guidance_model_fixed.py ... \
  --use_class_weights_best --worst_loss_coef 0.10 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth \
  --metadata_out legend/models/metadata_t3_d03_wcoef010.json
```
Outcome:
- Early stop at epoch ~71
- Test: Best Top-1=0.2105, Top-3=0.3684, Top-5=0.4737; Worst Top-1=0.0588
- Diagnostics: legend/reports/20250818_205302/model_metrics.json
Interpretation: Matches best WCLS_FIX Best score, while worst head impact is reduced.

### 5.6 B4: B3 + Weighted Sampler
```
python legend/train_guidance_model_fixed.py ... \
  --use_class_weights_best --worst_loss_coef 0.10 --use_weighted_sampler \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcoef010_ws.pth \
  --metadata_out legend/models/metadata_t3_d03_wcoef010_ws.json
```
Outcome:
- Early stop at epoch ~53
- Test: Best Top-1=0.2105 (no change), Worst Top-1=0.0, Test Loss high (5.42)
Interpretation: Weighted sampling unhelpful here; skip for now.

---

## 6. Side-by-Side Highlights

- WCLS (buggy) vs B3:
  - Best Top-1: 0.0526 → 0.2105
  - Validates class-weight fix + proper loss balancing
- WCLS_FIX vs B3:
  - Best Top-1: 0.2105 → 0.2105 (tie)
  - B3 reduces Worst influence (Top-1 from 0.7353 down to 0.0588)
- BEST-ONLY vs B3:
  - Best Top-1: 0.1842 → 0.2105
- ROUTE-C vs B3:
  - Best Top-1: 0.0789 → 0.2105

### 6.1 Split label stats (B3 example)
- Overall (best): valid=476/726 (65.6%), unique classes=14, majority class=0 (270), majority baseline≈0.567
- Train (best): valid=330/509 (64.8%), unique=10, top classes: 0(199), 4(86), 3(24), 5(11)
- Val (best): valid=108/145 (74.5%), unique=9, top classes: 0(67), 4(21), 3(8)
- Test (best): valid=38/72 (52.8%), unique=8, top classes: 2(9), 1(8), 4(7), 3(6), 0(4)
- Overall (worst): valid=250/726 (34.4%), unique=8, top classes: 4(112), 0(105), 8(20)

### 6.2 Best head confusion matrix (test)
- B1 accuracy: 0.0526 (2/38); B3 accuracy: 0.2105 (8/38)
- Row totals (top-5 true classes by support): [(2,9), (1,8), (4,7), (3,6), (0,4)]
- Top confusions (count, true→pred):
  - B1: (9, 2→10), (8, 1→10), (7, 4→10), (6, 3→10), (4, 0→10), ...
  - B3: (9, 2→1), (7, 4→1), (6, 3→1), (4, 0→1), (2, 10→1), ...
Interpretation:
- B1 强烈偏向预测类 10；B3 主要偏向预测类 1；类别偏置由 worst 头影响与数据分布共同导致。

---

## 7. Key Findings

- Stable early stopping on val_loss is superior to best_acc for this small, imbalanced dataset.
- A small but non-zero Worst loss weight helps overall (0.10–0.25); 0.10 recommended to reduce Worst over-regularization.
- Class weights for Best head must ignore unseen classes and be normalized; fix resolved loss explosion.
- Weighted sampling did not help Best under current coverage; defer unless data changes significantly.
- Data coverage is the primary bottleneck: best classes only up to 0..16; 17..19 missing.

---

## 8. Recommendations & Next Steps

1) Data Expansion (highest ROI)
- Aggregate more llm_guidance logs; target coverage for classes 17–19
- Re-run B3 training and diagnostics; expect Best Top-1 > 0.21 with fuller coverage

2) Keep B3 as the baseline recipe
- val_loss early stopping; --use_class_weights_best; --worst_loss_coef 0.10
- No weighted sampler by default

3) Optional gentle regularization tweaks (after data improves)
- Increase dropout to 0.4
- num_transformer_layers: try 4
- Compare against simple CNN head as ablation

4) Evaluate end-to-end later
- After model stabilizes, instrument limited_astar_guidance to compare expansions/time/success with vs without guidance

---

## 9. Reproducibility Cheatsheet

Data generation:
```
python legend/convert_log_to_samples_optimized.py \
  --log_dir legend/llm_guidance_logs \
  --extra_logs legend/llm_guided_test_log.txt \
  --output_prefix legend/optimized_llm_guided_samples
```

Train (B3 baseline):
```
python legend/train_guidance_model_fixed.py \
  --matrix_csv legend/optimized_llm_guided_samples_matrix.csv \
  --labels_csv legend/optimized_llm_guided_samples_labels.csv \
  --epochs 200 --batch_size 64 --patience 40 \
  --num_transformer_layers 3 --classifier_dropout 0.3 \
  --embed_dim 64 --n_heads 4 --n_hidden 128 \
  --use_class_weights_best --worst_loss_coef 0.10 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth \
  --metadata_out legend/models/metadata_t3_d03_wcoef010.json
```

Diagnostics:
```
python legend/diagnostics_guidance.py \
  --metadata legend/models/metadata_t3_d03_wcoef010.json \
  --model_path legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth
```

Artifacts of interest:
- Models: legend/models/*.pth
- Metadata: legend/models/*.json
- Reports: legend/reports/<timestamp>/*

---

## 10. Appendix: Environment & Shapes

- Inferred shape from CSVs: (n_layers=22, n_rows=15, n_cols=6)
- Splits: train=509, val=145, test=72 (sequential)
- Best totals: val=108, test=38; Worst totals: val=37, test=34
- Device: CUDA available for all runs

