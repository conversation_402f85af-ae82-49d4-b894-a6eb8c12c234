# 指导模型（Guidance）实验报告（中文）

日期：2025-08-18  
目录：legend/

## 1. 目标

在 Blocks World 任务上训练一个指导模型，输出：
- Best（最佳下一步动作，主目标）
- Worst（最差下一步动作，辅助目标，降低权重）

在小样本且类别不平衡的条件下，让 Best 头的 Top-1/Top-k 指标稳定提升，并可复现实验。

---

## 2. 数据与预处理

日志来源：
- legend/llm_guidance_logs/instance_001_llm_log.txt
- legend/llm_guided_test_log.txt

转换脚本：legend/convert_log_to_samples_optimized.py（本次新增多日志支持）
- 参数：--log_dir、--extra_logs、--output_prefix
- 22 层接口：
  - 层 0=目标（goal）占用
  - 层 1=当前状态（current）占用（含位置增强，不改变占用语义）
  - 层 2..21=基于候选动作生成的后继占用
- 位置增强（仅在层 1）：
  - FIX_STACK_STABLE_PART、OTHER_PROBLEMATIC_BLOCKS、PRIORITY_TASK_BLOCK（以多通道标注）
- 动作空间：5 个栈，源-目的不相等 → 5×4=20 类（0..19）

运行命令（已执行）：
```
python legend/convert_log_to_samples_optimized.py \
  --log_dir legend/llm_guidance_logs \
  --extra_logs legend/llm_guided_test_log.txt \
  --output_prefix legend/optimized_llm_guided_samples
```
输出：
- legend/optimized_llm_guided_samples_matrix.csv（样本 726）
- legend/optimized_llm_guided_samples_labels.csv

标签覆盖摘要（总体）：
- best：valid=476/726（65.6%），unique=14，min=0，max=13（缺少 14..19），多数类基线≈0.567（类 0=270）
- worst：valid=250/726（34.4%），unique=8，偏置重（类 4=112，类 0=105）

---

## 3. 统计与可视化（diagnostics）

诊断脚本：legend/diagnostics_guidance.py（新增支持 --metadata、--model_path 选择模型）
- 生成：label_stats_overall.json、label_stats_splits.json
- 评估：val/test 上 best/worst 的 Top-k，输出 model_metrics.json
- 若环境支持 matplotlib：保存 best 头测试集混淆矩阵 PNG 和 NPY

本次生成的报告目录：
- B1（wcoef=0.25）：legend/reports/20250818_194507
- B3（wcoef=0.10）：legend/reports/20250818_205302

数据切分（顺序切分）：train=509, val=145, test=72

按 split 的标签统计（B3 与 B1 一致）：
- Train（best）：valid=330/509（64.8%），unique=10，多数类基线≈0.603；Top 类：0(199), 4(86), 3(24), 5(11)
- Val（best）：valid=108/145（74.5%），unique=9；Top 类：0(67), 4(21), 3(8)
- Test（best）：valid=38/72（52.8%），unique=8；Top 类：2(9), 1(8), 4(7), 3(6), 0(4)
- Train（worst）：valid=179/509（35.2%），unique=5；Top 类：0(80), 4(76), 8(18)
- Val（worst）：valid=37/145（25.5%），unique=5；Top 类：0(21), 4(11), 8(2)
- Test（worst）：valid=34/72（47.2%），unique=5；Top 类：4(25), 0(4), 2(3)

关键图表（若存在）：
- B1 混淆矩阵（best, test）：legend/reports/20250818_194507/confusion_best_test.png
- B3 混淆矩阵（best, test）：legend/reports/20250818_205302/confusion_best_test.png

---

## 4. 训练脚本修改（train_guidance_model_fixed.py）

- 分头损失与可调权重：
  - criterion_best、criterion_worst 分离
  - worst_loss_coef（默认 0.5，可降至 0.10 以减弱 worst 牵制）
- Best 头类权重修复：
  - 仅对训练集中出现过的类别按“反频率”加权；未出现类别权重=1
  - 整体归一化至均值=1；保持 label smoothing
- 早停与调度：
  - 学习率调度固定以 val_loss 为度量
  - 早停指标可选：--early_stop_metric [val_loss|best_acc]（建议 val_loss 更稳定）
- 采样器（可选）：--use_weighted_sampler（按 Best 标签的类权重生成样本权重）
- 新增 CLI：--use_class_weights_best、--worst_loss_coef、--early_stop_metric、--use_weighted_sampler

---

## 5. 实验设置（通用）

- 模型：3 层 Transformer，classifier_dropout=0.3，embed_dim=64，n_heads=4，n_hidden=128
- 训练：epochs=200，batch=64，patience=40，lr=5e-5，weight_decay=3e-4，label_smoothing=0.1
- 切分：顺序切分（509/145/72），CSV 头推断形状=(22,15,6)

注：测试集 best_total=38，worst_total=34。

---

## 6. 实验与结果

### 6.1 WCLS（初始，类权重有缺陷）
- 命令：使用 --use_class_weights_best，worst_loss_coef=0.25
- 结果：loss 异常巨大（未出现类别被赋予极端权重）
- 测试：Best Top-1=0.0526，Worst Top-1=0.1176
- 报告：legend/reports/20250818_194507

### 6.2 WCLS_FIX（修复类权重），worst_loss_coef=0.25（B1）
- 早停：≈第 171 个 epoch
- 测试：Best Top-1=0.2105，Worst Top-1=0.7353
- 解读：Best 达到“多数类基线”附近；Worst 偏置很强，可能牵制表示

### 6.3 BEST-ONLY（worst_loss_coef=0.0）
- 早停：≈第 118 个 epoch
- 测试：Best Top-1=0.1842，Worst Top-1=0.0882
- 解读：完全移除 Worst 监督会使 Best 略降，说明少量 Worst 监督有辅助作用

### 6.4 ROUTE-C（早停指标=best_acc，worst_loss_coef=0.10）
- 早停：≈第 77 个 epoch
- 测试：Best Top-1=0.0789
- 解读：小数据+不平衡下 best_acc 作为早停指标较噪声，效果不佳；建议固定用 val_loss

### 6.5 B3（推荐）：val_loss 早停 + worst_loss_coef=0.10
- 早停：≈第 71 个 epoch
- 测试：Best Top-1=0.2105，Top-3=0.3684，Top-5=0.4737；Worst Top-1=0.0588
- 报告：legend/reports/20250818_205302
- 解读：与 B1 的 Best 持平，但进一步降低了 Worst 的干扰

### 6.6 B4：在 B3 基础上启用加权采样（use_weighted_sampler）
- 早停：≈第 53 个 epoch
- 测试：Best Top-1=0.2105（无提升），Worst Top-1=0.0000，Test Loss 较高（5.42）
- 解读：当前覆盖度下加权采样对 Best 无益，暂不推荐

---

## 7. B1 vs B3 详细对比

验证集：
- B1（wcoef=0.25）：
  - Best：top1=0.000，top3=0.000，top5=0.000（best_total=108）
  - Worst：top1=0.568，top3=0.919，top5=0.919（worst_total=37）
- B3（wcoef=0.10）：
  - Best：top1=0.028，top3=0.120，top5=0.500（best_total=108）
  - Worst：top1=0.568，top3=0.919，top5=0.919（worst_total=37）

测试集：
- B1（wcoef=0.25）：
  - Best：top1=0.0526，top3=0.0526，top5=0.0526（best_total=38）
  - Worst：top1=0.1176，top3=0.8529，top5=0.8529（worst_total=34）
- B3（wcoef=0.10）：
  - Best：top1=0.2105，top3=0.3684，top5=0.4737（best_total=38）
  - Worst：top1=0.0588，top3=0.7941，top5=0.8529（worst_total=34）

---

## 8. 混淆矩阵分析（best，测试集）

- 整体准确率：
  - B1：0.0526（2/38）
  - B3：0.2105（8/38）
- 支持度前 5 的真实类别与其样本数（行总数）：[(2,9), (1,8), (4,7), (3,6), (0,4)]
- Top 混淆对（计数，真实→预测）：
  - B1： (9, 2→10), (8, 1→10), (7, 4→10), (6, 3→10), (4, 0→10), ...
  - B3： (9, 2→1), (7, 4→1), (6, 3→1), (4, 0→1), (2, 10→1), ...
- 图像（如存在）：
  - B1：legend/reports/20250818_194507/confusion_best_test.png
  - B3：legend/reports/20250818_205302/confusion_best_test.png

解读：
- B1 明显偏向预测“类 10”；B3 主要偏向“类 1”。
- 偏置来源于数据分布（多数类）与 worst 头约束的共同影响；降权 worst（B3）后，Best 的偏置相对收敛。

---

## 9. 关键结论

- 早停与调度固定以 val_loss 为度量最稳健；best_acc 作为早停指标不适合当前数据规模与不平衡。
- 少量（非零）Worst 损失权重有帮助（0.10–0.25）；推荐 0.10 以减少对 Best 的牵制。
- Best 头类权重需忽略未出现类别并归一化；修复后训练损失恢复正常。
- 当前加权采样对 Best 无显著收益；暂不启用。
- 数据覆盖是主要瓶颈：Best 仅覆盖 0..13（总体），测试集也只到 10；亟需补充 14..19 类。

---

## 10. 建议与下一步

1) 数据扩充（最高优先级）
- 继续聚合 llm_guidance 日志，优先补足 17–19 类；尽量提升少数类样本量
- 扩充后以 B3 配置重训并生成对照报告

2) 训练基线（B3）
- val_loss 早停、--use_class_weights_best、--worst_loss_coef 0.10
- 默认不启用 --use_weighted_sampler

3) 温和正则（在数据改善后再试）
- dropout 从 0.3 提到 0.4；或将 num_transformer_layers 从 3 提到 4 做对照
- 可与简化 CNN 头做消融对比

4) 端到端评测（模型稳定后）
- 在 limited_astar_guidance 中对比引导/不引导的节点展开、耗时、成功率

---

## 11. 复现清单

数据生成：
```
python legend/convert_log_to_samples_optimized.py \
  --log_dir legend/llm_guidance_logs \
  --extra_logs legend/llm_guided_test_log.txt \
  --output_prefix legend/optimized_llm_guided_samples
```

训练（B3 基线）：
```
python legend/train_guidance_model_fixed.py \
  --matrix_csv legend/optimized_llm_guided_samples_matrix.csv \
  --labels_csv legend/optimized_llm_guided_samples_labels.csv \
  --epochs 200 --batch_size 64 --patience 40 \
  --num_transformer_layers 3 --classifier_dropout 0.3 \
  --embed_dim 64 --n_heads 4 --n_hidden 128 \
  --use_class_weights_best --worst_loss_coef 0.10 \
  --model_out legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth \
  --metadata_out legend/models/metadata_t3_d03_wcoef010.json
```

诊断：
```
python legend/diagnostics_guidance.py \
  --metadata legend/models/metadata_t3_d03_wcoef010.json \
  --model_path legend/models/cnn_transformer_guidance_t3_d03_wcoef010.pth
```

产出物：
- 模型：legend/models/*.pth
- 元数据：legend/models/*.json
- 报告：legend/reports/<timestamp>/*

---

## 12. 附录：环境与形状

- CSV 推断形状：n_layers=22，n_rows=15，n_cols=6
- 切分：train=509，val=145，test=72（顺序切分）
- 计数：val（best_total=108, worst_total=37），test（best_total=38, worst_total=34）
- 设备：GPU 可用

