{"train": {"best": {"count": 85, "valid": 55, "valid_ratio": 0.6470588235294118, "unique": 9, "min": 0, "max": 13, "majority_baseline": 0.3090909090909091, "top5_classes": [[0, 17], [2, 11], [3, 10], [4, 8], [1, 4]]}, "worst": {"count": 85, "valid": 30, "valid_ratio": 0.35294117647058826, "unique": 5, "min": 0, "max": 12, "majority_baseline": 0.7333333333333333, "top5_classes": [[4, 22], [0, 3], [2, 2], [12, 2], [8, 1]]}}, "val": {"best": {"count": 24, "valid": 10, "valid_ratio": 0.4166666666666667, "unique": 5, "min": 0, "max": 10, "majority_baseline": 0.4, "top5_classes": [[1, 4], [0, 2], [4, 2], [2, 1], [10, 1]]}, "worst": {"count": 24, "valid": 14, "valid_ratio": 0.5833333333333334, "unique": 4, "min": 0, "max": 4, "majority_baseline": 0.7142857142857143, "top5_classes": [[4, 10], [0, 2], [1, 1], [2, 1]]}}, "test": {"best": {"count": 12, "valid": 7, "valid_ratio": 0.5833333333333334, "unique": 5, "min": 1, "max": 6, "majority_baseline": 0.42857142857142855, "top5_classes": [[1, 3], [2, 1], [4, 1], [5, 1], [6, 1]]}, "worst": {"count": 12, "valid": 5, "valid_ratio": 0.4166666666666667, "unique": 4, "min": 0, "max": 4, "majority_baseline": 0.4, "top5_classes": [[4, 2], [0, 1], [2, 1], [3, 1]]}}}