"""
编码方案的深入分析与改进
"""
import numpy as np
import torch
import torch.nn as nn

print("=" * 70)
print("问题1：是否需要单独的块-栈隶属通道？")
print("=" * 70)

# 示例状态
state = {
    'stack1': ['A', 'B', 'C'],      # A在底，C在顶
    'stack2': ['D'],
    'stack3': [],
    'stack4': ['E', 'F'],            # E在底，F在顶
    'stack5': [],
    'stack6': []
}

all_blocks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O']

print("\n方案A：双通道（隶属 + 高度）")
print("-" * 40)
channel0_belong = np.zeros((15, 6))
channel1_height = np.zeros((15, 6))

for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    for height, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            channel0_belong[block_idx, stack_idx] = 1
            channel1_height[block_idx, stack_idx] = height + 1

print("通道0（隶属）:")
print("A在stack1:", channel0_belong[0, 0], "| 高度信息:", channel1_height[0, 0])
print("A在stack2:", channel0_belong[0, 1], "| 高度信息:", channel1_height[0, 1])

print("\n方案B：单通道（仅高度）")
print("-" * 40)
single_channel = np.zeros((15, 6))
for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    for height, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            single_channel[block_idx, stack_idx] = height + 1

print("仅高度编码:")
print("A在stack1:", single_channel[0, 0], "(>0表示在栈中)")
print("A在stack2:", single_channel[0, 1], "(=0表示不在)")

print("\n【CNN角度的分析】")
print("-" * 40)
print("为什么可能需要单独的隶属通道：")
print()
print("1. 卷积核的激活模式不同：")
print("   - 隶属通道：二值激活，容易学习'是否存在'的模式")
print("   - 高度通道：连续值，需要学习数值大小关系")
print()
print("2. 不同的归一化需求：")
print("   - 隶属：0/1二值，不需要归一化")
print("   - 高度：1-15的范围，可能需要归一化")
print()
print("3. 梯度传播特性：")
print("   - 二值特征的梯度更稳定")
print("   - 连续值可能有梯度消失/爆炸问题")

print("\n【实验验证】")
class TestCNN(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 32, kernel_size=3, padding=1)
        
    def forward(self, x):
        return self.conv(x)

# 测试两种编码
two_channel = torch.randn(1, 2, 15, 6)  # 双通道
one_channel = torch.randn(1, 1, 15, 6)  # 单通道

model_2ch = TestCNN(2)
model_1ch = TestCNN(1)

out_2ch = model_2ch(two_channel)
out_1ch = model_1ch(one_channel)

print(f"\n双通道CNN参数量: {sum(p.numel() for p in model_2ch.parameters())}")
print(f"单通道CNN参数量: {sum(p.numel() for p in model_1ch.parameters())}")
print("参数差异：双通道仅增加 3×3×1×32 = 288 个参数")

print("\n【结论】")
print("单通道（仅高度）可能就足够了，因为：")
print("✓ 高度>0 等价于隶属信息")
print("✓ 减少输入维度，模型更简洁")
print("✓ CNN可以自己学习提取隶属特征")

print("\n" + "=" * 70)
print("问题2：高度编码方向 - 从底部还是从顶部？")
print("=" * 70)

print("\n方案A：从底部编码（当前方案）")
print("-" * 40)
height_from_bottom = np.zeros((15, 6))
for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    for height, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            height_from_bottom[block_idx, stack_idx] = height + 1

print("示例: stack1 = [A, B, C]")
print("A的高度:", height_from_bottom[0, 0], "(底部)")
print("B的高度:", height_from_bottom[1, 0], "(中间)")
print("C的高度:", height_from_bottom[2, 0], "(顶部)")

print("\n方案B：从顶部编码（改进方案）")
print("-" * 40)
height_from_top = np.zeros((15, 6))
for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    stack_height = len(blocks)
    for pos, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            # 从顶部计数：顶部=1，次顶=2，...
            height_from_top[block_idx, stack_idx] = stack_height - pos

print("示例: stack1 = [A, B, C]")
print("A的高度:", height_from_top[0, 0], "(距顶部3)")
print("B的高度:", height_from_top[1, 0], "(距顶部2)")
print("C的高度:", height_from_top[2, 0], "(距顶部1，可移动！)")

print("\n【从顶部编码的优势】")
print("-" * 40)
print("1. 直接表达可移动性：")
print("   - height=1 → 可以移动")
print("   - height>1 → 被压住，不能移动")
print()
print("2. 动作决策更直观：")
print("   - CNN学习'找height=1的块'比'找最大height'简单")
print("   - 阈值固定（=1），不依赖栈的总高度")
print()
print("3. 更符合问题本质：")
print("   - Blocks World的核心是'移动栈顶块'")
print("   - 从顶部编码直接反映这一点")

print("\n方案C：双重编码（可移动性 + 深度）")
print("-" * 40)
movability = np.zeros((15, 6))
depth = np.zeros((15, 6))

for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    stack_height = len(blocks)
    for pos, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            # 通道0：可移动性（二值）
            movability[block_idx, stack_idx] = 1 if pos == len(blocks)-1 else 0
            # 通道1：深度信息
            depth[block_idx, stack_idx] = pos + 1

print("通道0（可移动性）- 只有栈顶块为1:")
print("C可移动:", movability[2, 0])
print("B可移动:", movability[1, 0])
print("\n通道1（深度）- 保留完整位置信息:")
print("A深度:", depth[0, 0])
print("B深度:", depth[1, 0])
print("C深度:", depth[2, 0])

print("\n" + "=" * 70)
print("最终推荐方案")
print("=" * 70)

print("\n【推荐：单通道，从顶部编码】")
print("-" * 40)
print("编码维度: (1, 15, 6) per state")
print()
print("编码规则:")
print("- matrix[block_id, stack_id] = 0: 块不在该栈")
print("- matrix[block_id, stack_id] = 1: 块在栈顶（可移动）")
print("- matrix[block_id, stack_id] = n: 块在栈中，距顶部n层")
print()
print("优势:")
print("✓ 信息完整且紧凑")
print("✓ 直接编码可移动性（=1）")
print("✓ 保留堆叠深度信息")
print("✓ CNN只需学习简单规则：找值=1的位置")

print("\n对于22个状态的完整输入:")
print("Shape: (22, 15, 6)")
print("- 不需要额外的隶属通道")
print("- 每层直接表示一个状态")
print("- 模型更简洁，训练更高效")
