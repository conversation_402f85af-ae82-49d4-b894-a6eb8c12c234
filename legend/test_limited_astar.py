#!/usr/bin/env python3
"""
测试带节点限制的A*搜索与CNN-Transformer指导
"""

import os
import sys
import time
import re
import ast

# 添加路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

try:
    from limited_astar_guidance import LimitedGraphPlanningBlocksWorld
    from cnn_transformer_guidance import CNNTransformerGuidance
    print("✅ 成功导入所需模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def parse_instance_file(file_path):
    """解析实例文件"""
    data = {}
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            start_state_match = re.search(r"Start State:\s*(\{.*\})", content)
            if start_state_match: 
                data['start_state'] = ast.literal_eval(start_state_match.group(1))
            g_canonical_match = re.search(r"G_canonical:\s*(\{.*\})", content)
            if g_canonical_match: 
                data['g_canonical'] = ast.literal_eval(g_canonical_match.group(1))
            fix_order_match = re.search(r"Fix Order:\s*(\[.*\])", content)
            if fix_order_match: 
                data['fix_order'] = ast.literal_eval(fix_order_match.group(1))
        return data
    except Exception as e:
        print(f"❌ 解析实例文件 {file_path} 失败: {e}")
        return None

def run_limited_astar_test(start_state, g_canonical, fix_order, max_nodes=20000, guidance=None):
    """运行带限制的A*测试"""
    guidance_name = "无指导" if guidance is None else guidance.__class__.__name__
    
    print("\n" + "=" * 80)
    print(f"带限制的A*搜索测试 ({guidance_name})")
    print(f"最大节点数: {max_nodes}")
    print("=" * 80)
    
    log_file = os.path.join(SCRIPT_DIR, f"limited_astar_{guidance_name.lower()}_log.txt")

    try:
        planner = LimitedGraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            log_file=log_file,
            max_nodes=max_nodes
        )
        print("✅ 成功创建带限制的规划器实例")
        
        print(f"🚀 开始运行带限制的A*搜索 ({guidance_name})...")
        start_time = time.time()
        
        solution, nodes_count = planner.a_star_search(llm=guidance)
        
        duration = time.time() - start_time
        
        print("-" * 80)
        print(f"{guidance_name} A*测试结果:")
        print("-" * 80)
        
        if solution:
            print(f"✅ 找到解决方案! (路径长度: {len(solution)} 步)")
            print(f"   解决方案: {solution}")
        else:
            print("❌ 未找到解决方案")
        
        print(f"   - 搜索节点数: {nodes_count}")
        print(f"   - 最大节点限制: {max_nodes}")
        print(f"   - 节点利用率: {nodes_count/max_nodes:.2%}")
        print(f"   - 耗时: {duration:.4f} 秒")
        print(f"   - 日志文件: {log_file}")
        
        return solution, nodes_count, duration
        
    except Exception as e:
        print(f"❌ 带限制的A*测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0, 0

def compare_results(pure_result, guided_result):
    """比较两种方法的结果"""
    print("\n" + "=" * 80)
    print("性能对比分析")
    print("=" * 80)
    
    pure_solution, pure_nodes, pure_time = pure_result
    guided_solution, guided_nodes, guided_time = guided_result
    
    print(f"{'指标':<25} {'无指导A*':<20} {'CNN-Transformer指导':<25} {'改进':<20}")
    print("-" * 90)
    
    # 解决方案对比
    if pure_solution and guided_solution:
        solution_improvement = len(pure_solution) - len(guided_solution)
        print(f"{'解决方案长度':<25} {len(pure_solution):<20} {len(guided_solution):<25} {solution_improvement:+d}")
    elif pure_solution:
        print(f"{'解决方案长度':<25} {len(pure_solution):<20} {'未找到':<25} {'失败':<20}")
    elif guided_solution:
        print(f"{'解决方案长度':<25} {'未找到':<20} {len(guided_solution):<25} {'成功找到!':<20}")
    else:
        print(f"{'解决方案长度':<25} {'未找到':<20} {'未找到':<25} {'都未找到':<20}")
    
    # 搜索效率对比
    if pure_nodes > 0 and guided_nodes > 0:
        nodes_improvement = pure_nodes - guided_nodes
        nodes_ratio = guided_nodes / pure_nodes
        print(f"{'搜索节点数':<25} {pure_nodes:<20} {guided_nodes:<25} {nodes_improvement:+d} ({nodes_ratio:.2%})")
    else:
        print(f"{'搜索节点数':<25} {pure_nodes:<20} {guided_nodes:<25} {'N/A':<20}")
    
    # 时间对比
    if pure_time > 0 and guided_time > 0:
        time_improvement = pure_time - guided_time
        time_ratio = guided_time / pure_time
        print(f"{'耗时 (秒)':<25} {pure_time:.4f:<20} {guided_time:.4f:<25} {time_improvement:+.4f} ({time_ratio:.2%})")
    else:
        print(f"{'耗时 (秒)':<25} {pure_time:.4f:<20} {guided_time:.4f:<25} {'N/A':<20}")

if __name__ == "__main__":
    # 配置
    instance_path = os.path.join(SCRIPT_DIR, "instance_28.txt")
    model_path = os.path.join(SCRIPT_DIR, "models", "cnn_transformer_guidance.pth")
    
    # 测试参数
    MAX_NODES = 20000  # 最大节点数限制
    
    # 测试控制
    RUN_PURE_ASTAR = True      # 运行无指导的A*
    RUN_CNN_GUIDED = True      # 运行CNN-Transformer指导的A*
    RUN_COMPARISON = True      # 进行性能对比
    
    print("=" * 100)
    print("带节点限制的A*搜索测试")
    print(f"测试实例: {os.path.basename(instance_path)}")
    print(f"模型路径: {model_path}")
    print(f"最大节点数: {MAX_NODES}")
    print("=" * 100)

    # 检查文件
    if not os.path.exists(instance_path):
        print(f"❌ 实例文件不存在: {instance_path}")
        sys.exit(1)
        
    if RUN_CNN_GUIDED and not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        sys.exit(1)

    # 解析实例
    instance_data = parse_instance_file(instance_path)
    if not instance_data:
        print("❌ 无法解析实例文件")
        sys.exit(1)
    
    start_state = instance_data['start_state']
    g_canonical = instance_data['g_canonical']
    fix_order = instance_data['fix_order']

    pure_result = (None, 0, 0)
    guided_result = (None, 0, 0)
    
    # 运行无指导A*测试
    if RUN_PURE_ASTAR:
        pure_result = run_limited_astar_test(
            start_state, g_canonical, fix_order, 
            max_nodes=MAX_NODES, guidance=None
        )
    
    # 运行CNN-Transformer指导A*测试
    if RUN_CNN_GUIDED:
        try:
            guidance = CNNTransformerGuidance(model_path)
            guided_result = run_limited_astar_test(
                start_state, g_canonical, fix_order,
                max_nodes=MAX_NODES, guidance=guidance
            )
        except Exception as e:
            print(f"❌ 创建CNN-Transformer指导失败: {e}")
            guided_result = (None, 0, 0)
    
    # 性能对比
    if RUN_COMPARISON and RUN_PURE_ASTAR and RUN_CNN_GUIDED:
        compare_results(pure_result, guided_result)
    
    print("\n" + "=" * 100)
    print("✅ 测试完成")
    print("=" * 100)