"""
改进的CNN实现示例
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class ImprovedBlocksWorldCNN(nn.Module):
    """
    基于深度分析的改进CNN架构
    """
    def __init__(self, n_stacks=6, n_blocks=15, n_layers=22):
        super().__init__()
        
        # 栈内关系提取器（垂直卷积）
        self.stack_conv1 = nn.Conv2d(n_layers, 64, 
                                     kernel_size=(5, 1), 
                                     padding=(2, 0))
        self.stack_bn1 = nn.BatchNorm2d(64)
        
        self.stack_conv2 = nn.Conv2d(64, 128, 
                                     kernel_size=(3, 1), 
                                     padding=(1, 0))
        self.stack_bn2 = nn.BatchNorm2d(128)
        
        # 栈间关系建模（水平卷积）
        self.cross_stack_conv = nn.Conv2d(128, 128, 
                                          kernel_size=(1, 3), 
                                          padding=(0, 1))
        self.cross_stack_bn = nn.BatchNorm2d(128)
        
        # 注意力机制
        self.spatial_attention = SpatialAttention(128)
        
        # 全局特征提取（使用空洞卷积增大感受野）
        self.global_conv = nn.Conv2d(128, 256, 
                                     kernel_size=3, 
                                     padding=2,
                                     dilation=2)  # 感受野5×5
        self.global_bn = nn.BatchNorm2d(256)
        
        # 不使用池化，保留位置信息
        
    def forward(self, x):
        # x shape: (batch, 22, 15, 6)
        
        # 栈内特征提取
        x = F.relu(self.stack_bn1(self.stack_conv1(x)))
        x = F.relu(self.stack_bn2(self.stack_conv2(x)))
        
        # 栈间关系
        x = F.relu(self.cross_stack_bn(self.cross_stack_conv(x)))
        
        # 应用空间注意力
        x = self.spatial_attention(x)
        
        # 全局整合
        x = F.relu(self.global_bn(self.global_conv(x)))
        
        return x

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=1)
        
    def forward(self, x):
        attention = torch.sigmoid(self.conv(x))
        return x * attention

class StackSpecificCNN(nn.Module):
    """
    另一种设计：每个栈独立处理
    """
    def __init__(self, n_stacks=6, n_blocks=15, n_layers=22):
        super().__init__()
        
        # 为每个栈创建独立的卷积路径
        self.stack_encoders = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(n_layers, 32, kernel_size=3, padding=1),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Conv1d(32, 64, kernel_size=3, padding=1),
                nn.BatchNorm1d(64),
                nn.ReLU(),
            ) for _ in range(n_stacks)
        ])
        
        # 融合所有栈的特征
        self.fusion = nn.Conv1d(64 * n_stacks, 256, kernel_size=1)
        
    def forward(self, x):
        # x shape: (batch, 22, 15, 6)
        batch_size = x.size(0)
        
        # 分别处理每个栈
        stack_features = []
        for i in range(6):
            stack_data = x[:, :, :, i]  # (batch, 22, 15)
            stack_feat = self.stack_encoders[i](stack_data)
            stack_features.append(stack_feat)
        
        # 合并所有栈的特征
        combined = torch.cat(stack_features, dim=1)
        output = self.fusion(combined)
        
        return output

# 测试形状
if __name__ == "__main__":
    print("\n测试改进的CNN架构:")
    print("-" * 40)
    
    # 创建模型
    model1 = ImprovedBlocksWorldCNN()
    model2 = StackSpecificCNN()
    
    # 测试输入
    dummy_input = torch.randn(2, 22, 15, 6)
    
    print(f"输入形状: {dummy_input.shape}")
    
    # 测试前向传播
    with torch.no_grad():
        output1 = model1(dummy_input)
        print(f"ImprovedCNN输出形状: {output1.shape}")
        
        output2 = model2(dummy_input)
        print(f"StackSpecificCNN输出形状: {output2.shape}")
    
    # 参数统计
    total_params1 = sum(p.numel() for p in model1.parameters())
    total_params2 = sum(p.numel() for p in model2.parameters())
    
    print(f"\nImprovedCNN参数量: {total_params1:,}")
    print(f"StackSpecificCNN参数量: {total_params2:,}")
