import os
import json
import math
import argparse
import torch
from torch.utils.data import DataLoader, Subset, WeightedRandomSampler
import torch.nn as nn
import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
import re
from typing import List

from cnn_transformer_legend import (
    BlocksWorldDataset,
    CNNTransformerClassifier,
)

# 解析列名推断形状（n_layers, n_rows, n_cols）
# 列名格式示例：goal_pos_0_stack_0, current_pos_1_buffer_0, action_0_pos_2_stack_1
DEFAULT_B = 15
DEFAULT_S = 5

def infer_shape_from_csv_header(header):
    # 从优化的列名格式解析形状
    # 列名格式: {layer}_{pos}_{row}_{stack/buffer}_{col}
    # 例如: goal_pos_0_stack_0, current_pos_1_buffer_0, action_0_pos_2_stack_1
    
    layers = set()
    rows = set()
    cols = set()
    
    for col in header:
        parts = col.split('_')
        if len(parts) >= 5:
            # 解析层
            if parts[0] == 'goal':
                layers.add(0)
            elif parts[0] == 'current':
                layers.add(1)
            elif parts[0] == 'action':
                action_idx = int(parts[1])
                layers.add(2 + action_idx)
            
            # 解析行（位置）
            if parts[1] == 'pos':
                rows.add(int(parts[2]))
            
            # 解析列（栈或缓冲）
            if parts[3] == 'stack':
                cols.add(int(parts[4]))
            elif parts[3] == 'buffer':
                cols.add(5 + int(parts[4]))  # 假设5个栈后是缓冲列
    
    n_layers = max(layers) + 1 if layers else 22
    n_rows = max(rows) + 1 if rows else 15
    n_cols = max(cols) + 1 if cols else 6
    
    print(f"从优化列名推断形状: n_layers={n_layers}, n_rows={n_rows}, n_cols={n_cols}")
    return (n_layers, n_rows, n_cols)

def create_sequential_splits(dataset_size, val_ratio=0.2, test_ratio=0.1):
    """
    创建序列感知的数据分割，避免数据泄露
    使用时间序列分割：前面的数据用于训练，后面的数据用于验证和测试
    """
    indices = list(range(dataset_size))
    
    # 计算分割点
    test_size = int(dataset_size * test_ratio)
    val_size = int(dataset_size * val_ratio)
    train_size = dataset_size - val_size - test_size
    
    # 序列分割：训练集 | 验证集 | 测试集
    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size + val_size]
    test_indices = indices[train_size + val_size:]
    
    print(f"序列分割: 训练集={len(train_indices)}, 验证集={len(val_indices)}, 测试集={len(test_indices)}")
    return train_indices, val_indices, test_indices

def train_one_epoch(model, loader, optimizer, criterion_best, criterion_worst, device, worst_loss_coef: float = 0.5):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_batches = 0

    for batch_matrix, batch_labels in loader:
        batch_matrix = batch_matrix.to(device)
        batch_labels = batch_labels.to(device)

        optimizer.zero_grad()
        best_logits, worst_logits = model(batch_matrix)

        # 标签有效性掩码，-1表示无效标签
        best_mask = batch_labels[:, 0] != -1
        worst_mask = batch_labels[:, 1] != -1

        loss = 0.0
        current_loss_value = 0.0

        if best_mask.any():
            loss_best = criterion_best(best_logits[best_mask], batch_labels[best_mask, 0])
            loss = loss + loss_best
            current_loss_value += float(loss_best.item())
        if worst_mask.any() and worst_loss_coef > 0:
            loss_worst = criterion_worst(worst_logits[worst_mask], batch_labels[worst_mask, 1])
            loss = loss + worst_loss_coef * loss_worst
            current_loss_value += float((worst_loss_coef * loss_worst).item())

        if loss != 0:  # 只有当存在有效标签时才反向传播
            loss.backward()
            optimizer.step()
            total_loss += current_loss_value
            total_batches += 1

    return total_loss / max(1, total_batches)

def evaluate(model, loader, criterion_best, criterion_worst, device):
    """评估模型"""
    model.eval()
    total_loss = 0.0
    total_batches = 0
    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0
    
    with torch.no_grad():
        for batch_matrix, batch_labels in loader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)
            
            best_logits, worst_logits = model(batch_matrix)
            
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1
            
            loss = 0
            current_loss_value = 0
            
            if best_mask.any():
                loss_best = criterion_best(best_logits[best_mask], batch_labels[best_mask, 0])
                loss += loss_best
                current_loss_value += loss_best.item()

                preds = best_logits[best_mask].argmax(dim=1)
                correct_best += (preds == batch_labels[best_mask, 0]).sum().item()
                total_best += int(best_mask.sum().item())

            if worst_mask.any():
                loss_worst = criterion_worst(worst_logits[worst_mask], batch_labels[worst_mask, 1])
                loss += loss_worst
                current_loss_value += loss_worst.item()

                preds = worst_logits[worst_mask].argmax(dim=1)
                correct_worst += (preds == batch_labels[worst_mask, 1]).sum().item()
                total_worst += int(worst_mask.sum().item())
                
            if loss != 0:
                total_loss += current_loss_value
                total_batches += 1
    
    avg_loss = total_loss / max(1, total_batches)
    acc_best = correct_best / total_best if total_best > 0 else 0.0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0.0
    
    return avg_loss, acc_best, acc_worst

def prepare_data(data, orig_shape, new_shape):
    """数据预处理函数，与原版保持一致"""
    batch_size = data.shape[0]
    new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
    new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data
    return new_data

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--matrix_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_matrix.csv"))
    parser.add_argument("--labels_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_labels.csv"))
    parser.add_argument("--epochs", type=int, default=5000)
    parser.add_argument("--batch_size", type=int, default=32)
    parser.add_argument("--lr", type=float, default=5e-5)
    parser.add_argument("--weight_decay", type=float, default=3e-4)
    parser.add_argument("--val_ratio", type=float, default=0.2)
    parser.add_argument("--test_ratio", type=float, default=0.1)
    parser.add_argument("--patience", type=int, default=100)
    parser.add_argument("--label_smoothing", type=float, default=0.1)
    parser.add_argument("--classifier_dropout", type=float, default=0.1)
    parser.add_argument("--embed_dim", type=int, default=64)
    parser.add_argument("--n_heads", type=int, default=8)
    parser.add_argument("--n_hidden", type=int, default=256)
    parser.add_argument("--num_transformer_layers", type=int, default=6)
    parser.add_argument("--use_class_weights_best", action="store_true")
    parser.add_argument("--worst_loss_coef", type=float, default=0.5)
    parser.add_argument("--early_stop_metric", choices=["val_loss","best_acc"], default="val_loss")
    parser.add_argument("--use_weighted_sampler", action="store_true")
    parser.add_argument("--model_out", default=os.path.join(os.path.dirname(__file__), "models", "cnn_transformer_guidance_fixed.pth"))
    parser.add_argument("--metadata_out", default=os.path.join(os.path.dirname(__file__), "models", "metadata_fixed.json"))
    args = parser.parse_args()

    os.makedirs(os.path.dirname(args.model_out), exist_ok=True)

    # 推断数据形状
    header = pd.read_csv(args.matrix_csv, nrows=0).columns.tolist()
    n_layers, n_rows, n_cols = infer_shape_from_csv_header(header)
    print(f"Inferred shape: n_layers={n_layers}, n_rows={n_rows}, n_cols={n_cols}")

    # 输出维度（类别数）= 实际动作数量 (n_stacks * (n_stacks - 1))
    # 对于5个栈的情况，有5*4=20个可能的动作
    n_stacks = 5  # 固定为5个栈
    n_classes = n_stacks * (n_stacks - 1)  # 20个动作类别
    orig_shape = (n_layers, n_rows, n_cols)
    new_shape = orig_shape  # 保持一致

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 使用修复的训练函数
    model = train_model_fixed(
        matrix_file=args.matrix_csv,
        label_file=args.labels_csv,
        orig_shape=orig_shape,
        new_shape=new_shape,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        weight_decay=args.weight_decay,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        patience=args.patience,
        label_smoothing_epsilon=args.label_smoothing,
        classifier_dropout_rate=args.classifier_dropout,
        embed_dim=args.embed_dim,
        n_heads=args.n_heads,
        n_hidden=args.n_hidden,
        num_transformer_layers=args.num_transformer_layers,
        model_path=args.model_out,
        use_class_weights_best=args.use_class_weights_best,
        worst_loss_coef=args.worst_loss_coef,
        early_stop_metric=args.early_stop_metric,
        use_weighted_sampler=args.use_weighted_sampler,
    )

    # 保存元数据
    metadata = {
        # 原始推断的形状参数
        "n_layers": n_layers,
        "n_rows": n_rows,
        "n_cols": n_cols,
        "n_classes": n_classes,
        # 新的CNN架构参数（固定值）
        "n_stacks": 5,
        "max_blocks": 15,
        "buffer_rows": 0,
        "buffer_cols": 1,
        # Transformer参数
        "embed_dim": args.embed_dim,
        "n_heads": args.n_heads,
        "n_hidden": args.n_hidden,
        "num_transformer_layers": args.num_transformer_layers,
        "classifier_dropout": args.classifier_dropout,
        # 数据文件路径
        "matrix_csv": os.path.abspath(args.matrix_csv),
        "labels_csv": os.path.abspath(args.labels_csv),
        # 数据分割信息
        "data_split_method": "sequential",
        "val_ratio": args.val_ratio,
        "test_ratio": args.test_ratio,
    }
    with open(args.metadata_out, "w", encoding="utf-8") as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    print(f"Saved metadata to {args.metadata_out}")

def train_model_fixed(matrix_file, label_file, orig_shape, new_shape, epochs=100, batch_size=32, lr=3e-4, weight_decay=1e-4,
                     val_ratio=0.2, test_ratio=0.1, patience=10, label_smoothing_epsilon=0.1, classifier_dropout_rate=0.1,
                     embed_dim=64, n_heads=8, n_hidden=256, num_transformer_layers=6, model_path="cnn_transformer_guidance_fixed.pth",
                     use_class_weights_best: bool=False, worst_loss_coef: float=0.5, early_stop_metric: str="val_loss", use_weighted_sampler: bool=False):
    """修复数据泄露问题的训练函数"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 创建数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape, new_shape)
    
    
    # 使用序列感知的数据分割
    train_indices, val_indices, test_indices = create_sequential_splits(
        len(dataset), val_ratio=val_ratio, test_ratio=test_ratio
    )
    
    # 创建数据子集
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)
    test_dataset = Subset(dataset, test_indices)

    # 可选：根据训练集 best 标签分布使用加权采样，缓解类别不平衡
    if use_weighted_sampler:
        labels_np = pd.read_csv(label_file).values[train_indices, 0]
        labels_np = np.where(labels_np == -1, 999, labels_np)  # 跳过无效标签
        valid_mask = labels_np != 999
        class_counts = np.bincount(labels_np[valid_mask], minlength=20)
        class_weights = np.ones(20, dtype=np.float32)
        nonzero = class_counts > 0
        class_weights[nonzero] = class_counts[nonzero].sum() / (len(class_counts[nonzero]) * class_counts[nonzero])
        sample_weights = np.ones(len(train_indices), dtype=np.float32)
        sample_weights[valid_mask] = class_weights[labels_np[valid_mask]]
        sampler = WeightedRandomSampler(weights=torch.tensor(sample_weights, dtype=torch.double), num_samples=len(train_indices), replacement=True)
        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, sampler=sampler)
    else:
        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_dataloader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    # 初始化模型
    n_classes = 5 * (5 - 1)  # 20个动作类别
    
    # 对于优化的数据格式，直接使用固定参数
    model = CNNTransformerClassifier(
        n_layers=new_shape[0],
        n_stacks=5,
        max_blocks=15,
        buffer_rows=0,
        buffer_cols=1,
        embed_dim=embed_dim,
        n_heads=n_heads,
        n_hidden=n_hidden,
        n_classes=n_classes,
        num_transformer_layers=num_transformer_layers,
        classifier_dropout_rate=classifier_dropout_rate
    ).to(device)

    # 检查是否存在已有模型文件，若存在则加载
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"Loaded existing model from {model_path} for incremental training")
    else:
        print(f"No existing model found at {model_path}, starting training from scratch")

    # 使用标签平滑的损失函数；可选地对 best 头使用类权重
    if use_class_weights_best:
        # 以训练集计算 best 头的类权重（仅对出现过的类别做反频率加权，未出现类别设为1）
        y_train = pd.read_csv(label_file).values[train_indices, 0]
        y_train = y_train[y_train != -1]
        if y_train.size:
            uniq, cnt = np.unique(y_train, return_counts=True)
            weights = np.ones(20, dtype=np.float32)
            # 反频率并按类别数归一，避免极端值
            weights[uniq] = (cnt.sum() / (len(uniq) * cnt))
            weights = weights / weights.mean()
            class_weights = torch.tensor(weights, dtype=torch.float32, device=device)
            criterion_best = nn.CrossEntropyLoss(weight=class_weights, label_smoothing=label_smoothing_epsilon)
        else:
            criterion_best = nn.CrossEntropyLoss(label_smoothing=label_smoothing_epsilon)
    else:
        criterion_best = nn.CrossEntropyLoss(label_smoothing=label_smoothing_epsilon)
    criterion_worst = nn.CrossEntropyLoss(label_smoothing=label_smoothing_epsilon)

    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)

    # 学习率调度器（以验证损失为度量）
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=patience // 2, factor=0.5, verbose=True)

    # 早停相关变量
    best_val_loss = float('inf')
    best_best_acc = 0.0
    epochs_no_improve = 0
    best_model_state_dict = model.state_dict()

    print(f"开始训练，使用序列感知的数据分割避免数据泄露...")
    print(f"训练集大小: {len(train_dataset)}, 验证集大小: {len(val_dataset)}, 测试集大小: {len(test_dataset)}")

    for epoch in range(epochs):
        train_loss = train_one_epoch(model, train_dataloader, optimizer, criterion_best, criterion_worst, device, worst_loss_coef=worst_loss_coef)
        val_loss, acc_best, acc_worst = evaluate(model, val_dataloader, criterion_best, criterion_worst, device)
        
        print(f"Epoch {epoch + 1}/{epochs} | train_loss={train_loss:.4f} | val_loss={val_loss:.4f} | acc_best={acc_best:.4f} | acc_worst={acc_worst:.4f}")
        
        # 更新学习率（仍然基于验证损失）
        scheduler.step(val_loss)

        # 早停判断（可选：基于 val_loss 或 best_acc）
        improved = False
        if early_stop_metric == "val_loss":
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                improved = True
        else:  # best_acc
            if acc_best > best_best_acc:
                best_best_acc = acc_best
                improved = True

        if improved:
            epochs_no_improve = 0
            best_model_state_dict = model.state_dict()
            print("Early-stop metric improved. Saving model state.")
        else:
            epochs_no_improve += 1
            print(f"Early-stop metric did not improve for {epochs_no_improve} epoch(s).")

        if epochs_no_improve >= patience:
            print(f"Early stopping triggered after {epoch + 1} epochs.")
            model.load_state_dict(best_model_state_dict)
            break
    
    # 保存最佳模型
    if not (epochs_no_improve >= patience):
        best_model_state_dict = model.state_dict()

    torch.save(best_model_state_dict, model_path)
    print(f"Model saved to {model_path}")

    # 加载回最佳模型用于测试
    model.load_state_dict(best_model_state_dict)
    
    # 在测试集上评估
    test_loss, test_acc_best, test_acc_worst = evaluate(model, test_dataloader, criterion_best, criterion_worst, device)
    print(f"\n=== 测试集结果（真实泛化性能）===")
    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Best Action Accuracy: {test_acc_best:.4f}")
    print(f"Test Worst Action Accuracy: {test_acc_worst:.4f}")
    
    return model

if __name__ == "__main__":
    main()