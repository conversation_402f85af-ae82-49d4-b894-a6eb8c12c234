#!/usr/bin/env python3
"""
A* 算法测试脚本 (纯A* vs LLM指导A*)
可方便地在主函数中切换测试模式。
"""

import os
import sys
import time
import re
import ast

# --- 环境设置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

# --- 模块导入 ---
try:
    from pre_marshalling import GraphPlanningBlocksWorld
    print("成功导入 GraphPlanningBlocksWorld (原始版本，包含完整日志)")
except ImportError as e:
    print(f"导入A*规划器失败: {e}")
    sys.exit(1)

try:
    from simple_llm_guidance import SimpleLLMGuidance
    LLM_GUIDANCE_AVAILABLE = True
    print("成功从 legend 目录导入 SimpleLLMGuidance")
except ImportError as e:
    LLM_GUIDANCE_AVAILABLE = False
    print(f"警告: 从 legend 导入 LLM 指导模块失败: {e} - LLM 测试将不可用。")

# --- 辅助函数 ---
def parse_instance_file(file_path):
    data = {}
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            start_state_match = re.search(r"Start State:\s*(\{.*\})", content)
            if start_state_match: data['start_state'] = ast.literal_eval(start_state_match.group(1))
            g_canonical_match = re.search(r"G_canonical:\s*(\{.*\})", content)
            if g_canonical_match: data['g_canonical'] = ast.literal_eval(g_canonical_match.group(1))
            fix_order_match = re.search(r"Fix Order:\s*(\[.*\])", content)
            if fix_order_match: data['fix_order'] = ast.literal_eval(fix_order_match.group(1))
        return data
    except Exception as e:
        print(f"解析实例文件 {file_path} 失败: {e}")
        return None

# --- 测试执行函数 ---
def run_a_star_test(start_state, g_canonical, fix_order):
    """运行纯A*测试"""
    print("\n" + "=" * 60)
    print("纯A*测试")
    print("=" * 60)
    
    log_file = os.path.join(SCRIPT_DIR, "astar_test_log.txt")

    try:
        planner = GraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            log_file=log_file
        )
        print("成功创建规划器实例")
        
        print("开始运行纯A*搜索...")
        start_time = time.time()
        
        solution, nodes_count = planner.a_star_search(llm=None)
        
        duration = time.time() - start_time
        
        print("-" * 60)
        print("纯A*测试结果:")
        print("-" * 60)
        
        if solution:
            print(f"✓ 找到解决方案! (路径长度: {len(solution)} 步)")
        else:
            print("✗ 未找到解决方案")
        
        print(f"  - 搜索节点数: {nodes_count}")
        print(f"  - 耗时: {duration:.4f} 秒")
        
    except Exception as e:
        print(f"纯A*测试执行失败: {e}")

def run_llm_guided_test(start_state, g_canonical, fix_order):
    """运行LLM指导的A*测试"""
    if not LLM_GUIDANCE_AVAILABLE:
        print("\nLLM指导模块不可用，跳过测试。")
        return
        
    print("\n" + "=" * 60)
    print("LLM指导A*测试 (GPT-5)")
    print("=" * 60)
    
    log_file = os.path.join(SCRIPT_DIR, "llm_guided_test_log.txt")

    try:
        llm_instance = SimpleLLMGuidance()
        print("成功创建GPT-5 LLM指导实例")
        
        planner = GraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            log_file=log_file
        )
        print("成功创建规划器实例")
        
        print("开始运行LLM指导的A*搜索...")
        start_time = time.time()
        
        solution, nodes_count = planner.a_star_search(llm=llm_instance)
        
        duration = time.time() - start_time
        
        print("-" * 60)
        print("LLM指导测试结果:")
        print("-" * 60)
        
        if solution:
            print(f"✓ 找到解决方案! (路径长度: {len(solution)} 步)")
        else:
            print("✗ 未找到解决方案")
            
        print(f"  - 搜索节点数: {nodes_count}")
        print(f"  - 耗时: {duration:.4f} 秒")
        
    except Exception as e:
        print(f"LLM指导测试执行失败: {e}")

if __name__ == "__main__":
    # --- 配置区 ---
    # 选择要测试的实例
    instance_path = os.path.join(SCRIPT_DIR, "instance_28.txt")  # 请将需要测试的实例文件复制/放置到 legend 目录
    # 也可改为：instance_path = os.path.join(SCRIPT_DIR, "instance_1.txt")

    # --- 测试控制区 (通过注释/取消注释来选择运行模式) ---
    RUN_PURE_ASTAR = True      # 是否运行纯A*测试
    RUN_LLM_GUIDED = True      # 是否运行LLM指导测试
    
    # 快速切换选项 (取消注释其中一行来快速选择模式):
    # RUN_PURE_ASTAR, RUN_LLM_GUIDED = True, False   # 只运行纯A*
    RUN_PURE_ASTAR, RUN_LLM_GUIDED = False, True   # 只运行LLM指导
    # RUN_PURE_ASTAR, RUN_LLM_GUIDED = True, True    # 运行对比测试

    # --- 执行区 ---
    print("=" * 80)
    print("A*算法测试启动")
    print(f"测试实例: {os.path.basename(instance_path)}")
    
    test_modes = []
    if RUN_PURE_ASTAR: test_modes.append("纯A*")
    if RUN_LLM_GUIDED: test_modes.append("LLM指导")
    print(f"运行模式: {' + '.join(test_modes) if test_modes else '无测试'}")
    print("=" * 80)

    instance_data = parse_instance_file(instance_path)
    
    if instance_data:
        start_state = instance_data['start_state']
        g_canonical = instance_data['g_canonical']
        fix_order = instance_data['fix_order']

        # --- 根据配置运行相应测试 ---
        
        if RUN_PURE_ASTAR:
            run_a_star_test(start_state, g_canonical, fix_order)
        
        if RUN_LLM_GUIDED:
            run_llm_guided_test(start_state, g_canonical, fix_order)
            
        if not RUN_PURE_ASTAR and not RUN_LLM_GUIDED:
            print("未选择任何测试模式，程序结束。")

    else:
        print("无法加载实例数据，测试终止。")
