import sys
import os
import re
import json
import numpy as np
import pandas as pd
import ast

# 添加路径（相对 legend 目录）
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)

# 导入原始的解析和生成函数
from generate_samples_from_log import parse_log, apply_amplification, get_stable_prefix_and_suffixes

# 导入正确的 GraphPlanningBlocksWorld 类
from pre_marshalling_astar_neural_dynamic_weights import GraphPlanningBlocksWorld

def extract_fix_order_from_log(log_content):
    """从日志中提取 fix_order"""
    lines = log_content.split('\n')
    fix_stacks = set()
    
    for line in lines:
        if "Current fix stack:" in line:
            match = re.search(r'Current fix stack: \*\*Stack(\d+)\*\*', line)
            if match:
                stack_id = int(match.group(1))
                fix_stacks.add(stack_id)
    
    # 按数字顺序排序并转换为字符串格式
    fix_order = [f"Stack{i}" for i in sorted(fix_stacks)]
    return fix_order

def generate_sample_matrix_fixed(log_samples,
                                current_state_layer_idx=1,
                                goal_state_layer_idx=0,
                                coeffs=None):
    """修正版本的样本矩阵生成函数"""
    if coeffs is None:
        coeffs = {
            "PRIORITY_TASK_BLOCK": 15,
            "OTHER_PROBLEMATIC_BLOCKS": 10,
            "FIX_STACK_STABLE_PART": 7,
            "NEXT_TARGET_TO_ADD": 8,
            "FUTURE_TARGET_AWARENESS_LOW": 3
        }

    if not log_samples:
        print("错误: 没有有效的日志样本")
        return None, None, None, None

    # 从第一个样本提取状态信息
    first_sample = log_samples[0]
    current_state = first_sample["current_state"]
    goal_state = first_sample["goal_state"]
    
    # 创建一个简单的 fix_order（按栈编号排序）
    stack_names = sorted(current_state.keys())
    fix_order = stack_names
    
    print(f"使用 fix_order: {fix_order}")
    
    # 创建规划器实例
    temp_planner = GraphPlanningBlocksWorld(current_state, goal_state, fix_order)

    samples_matrix = np.zeros((len(log_samples), temp_planner.n_layers, 
                               temp_planner.nn_n_rows, temp_planner.nn_n_cols))
    labels = np.full((len(log_samples), 2), -1, dtype=int)

    all_system_block_names = temp_planner.blocks
    all_system_stack_names = temp_planner.stacks
    
    for i, sample in enumerate(log_samples):
        if not sample.get("current_state") or not sample.get("goal_state"):
            print(f"警告: 样本 {i} (Node {sample.get('node')}) 缺少状态信息，跳过")
            continue
            
        planner = GraphPlanningBlocksWorld(sample["current_state"], sample["goal_state"], fix_order)

        # 生成原始矩阵
        raw_layers_matrix = planner._generate_n_layers_matrix()
        
        original_current_state_matrix = np.copy(raw_layers_matrix[current_state_layer_idx, :, :])
        modified_current_state_matrix = raw_layers_matrix[current_state_layer_idx, :, :]

        current_state_dict = sample.get("current_state")
        goal_state_dict = sample.get("goal_state")
        fix_stack_id = sample.get("fix_stack_id")
        attention_block_name = sample.get("attention_block_name")
        priority_task_desc = sample.get("priority_task_description", "")

        block_to_row_idx = {name: idx for idx, name in enumerate(all_system_block_names)}

        if fix_stack_id is not None:
            fix_stack_key = f"Stack{fix_stack_id}"
            current_blocks_in_fix_stack = current_state_dict.get(fix_stack_key, [])
            goal_blocks_for_fix_stack = goal_state_dict.get(fix_stack_key, [])

            stable_prefix, current_problem_suffix, future_target_suffix = \
                get_stable_prefix_and_suffixes(current_blocks_in_fix_stack, goal_blocks_for_fix_stack)

            amplified_as_problem_or_priority = set()

            # 应用特征放大
            for block_name in stable_prefix:
                if block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[block_name], coeffs["FIX_STACK_STABLE_PART"])

            task_type = "unknown"
            if "移走" in priority_task_desc:
                task_type = "remove"
            elif "移入" in priority_task_desc:
                task_type = "add"

            if task_type == "remove":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])
                    amplified_as_problem_or_priority.add(attention_block_name)

                for block_name in current_problem_suffix:
                    if block_name != attention_block_name and block_name in block_to_row_idx:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["OTHER_PROBLEMATIC_BLOCKS"])
                        amplified_as_problem_or_priority.add(block_name)

                for block_name in future_target_suffix:
                    if block_name in block_to_row_idx and block_name not in amplified_as_problem_or_priority:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["FUTURE_TARGET_AWARENESS_LOW"])

            elif task_type == "add":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])

                if attention_block_name and future_target_suffix and \
                        attention_block_name == future_target_suffix[0] and \
                        len(future_target_suffix) > 1 and \
                        future_target_suffix[1] in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[future_target_suffix[1]], coeffs["NEXT_TARGET_TO_ADD"])

        samples_matrix[i] = raw_layers_matrix

        # 处理标签
        successors = planner.get_successors()
        best_action = sample["best_action"]
        worst_action = sample["worst_action"]
        
        for j, (next_state, action) in enumerate(successors[:planner.n_blocks]):
            if best_action is not None and action == best_action:
                labels[i, 0] = 2 + j
            if worst_action is not None and action == worst_action:
                labels[i, 1] = 2 + j
        
        if labels[i, 0] == -1:
            print(f"Sample {i}: Best action '{best_action}' not found")
        if labels[i, 1] == -1:
            print(f"Sample {i}: Worst action '{worst_action}' not found")
    
    return samples_matrix, labels, temp_planner.blocks, temp_planner.stacks

def save_to_csv(samples_matrix, labels, blocks, stacks, output_prefix="samples"):
    """保存为CSV文件"""
    n_samples, n_layers, n_rows, n_cols = samples_matrix.shape

    column_names = []
    all_cols = blocks + stacks
    for layer in range(n_layers):
        for row in range(n_rows):
            for col in range(n_cols):
                if layer == 0:
                    layer_name = "goal"
                elif layer == 1:
                    layer_name = "current"
                else:
                    layer_name = f"action_{layer - 2}"
                
                if row < len(blocks):
                    row_name = f"on_{blocks[row]}"
                else:
                    row_name = "clear"
                
                col_name = all_cols[col] if col < len(all_cols) else "none"
                column_names.append(f"{layer_name}_{row_name}_{col_name}")

    flat_matrix = samples_matrix.reshape(n_samples, -1)
    matrix_df = pd.DataFrame(flat_matrix, columns=column_names)
    labels_df = pd.DataFrame(labels, columns=["best_action_idx", "worst_action_idx"])

    output_prefix_path = output_prefix if os.path.isabs(output_prefix) or os.path.dirname(output_prefix) else os.path.join(SCRIPT_DIR, output_prefix)
    matrix_df.to_csv(f"{output_prefix_path}_matrix.csv", index=False)
    labels_df.to_csv(f"{output_prefix_path}_labels.csv", index=False)

def process_log_to_matrix_fixed(log_content, output_prefix="samples"):
    """修正版本的主处理函数"""
    log_samples = parse_log(log_content)
    print(f"解析得到 {len(log_samples)} 个样本")
    
    if not log_samples:
        print("错误: 没有解析到有效样本")
        return None, None, None, None
    
    samples_matrix, labels, blocks, stacks = generate_sample_matrix_fixed(log_samples)
    if samples_matrix is not None:
        save_to_csv(samples_matrix, labels, blocks, stacks, output_prefix)
    
    return samples_matrix, labels, blocks, stacks

def main():
    log_file = os.path.join(SCRIPT_DIR, 'llm_guided_test_log.txt')
    if not os.path.exists(log_file):
        print(f"错误: 找不到日志文件 {log_file}")
        return
    
    with open(log_file, 'r', encoding='utf-8') as f:
        log_content = f.read()
    
    print(f"正在处理日志文件: {log_file}")
    print(f"日志内容长度: {len(log_content)} 字符")
    
    try:
        samples_matrix, labels, blocks, stacks = process_log_to_matrix_fixed(
            log_content,
            output_prefix=os.path.join(SCRIPT_DIR, 'llm_guided_samples')
        )

        if samples_matrix is not None:
            print('✅ 成功生成训练样本!')
            print(f'样本矩阵形状: {samples_matrix.shape}')
            print(f'标签形状: {labels.shape}')
            print(f'积木块: {blocks}')
            print(f'栈: {stacks}')
            print(f'前5个样本的标签: {labels[:5]}')
            
            matrix_file = os.path.join(SCRIPT_DIR, 'llm_guided_samples_matrix.csv')
            labels_file = os.path.join(SCRIPT_DIR, 'llm_guided_samples_labels.csv')

            if os.path.exists(matrix_file) and os.path.exists(labels_file):
                print(f'✅ 训练文件已生成:')
                print(f'  - {matrix_file}')
                print(f'  - {labels_file}')
            else:
                print('⚠️ 训练文件生成可能有问题')
        else:
            print('❌ 样本矩阵生成失败')
            
    except Exception as e:
        print(f'❌ 处理失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()