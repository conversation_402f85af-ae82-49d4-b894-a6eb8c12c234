import json
import re
import google.generativeai as genai
import os
import re
import ast
from openai import OpenAI

# 配置GPT-5参数
API_KEYS = ["sk-lbM5ROGcKpS4oSSznR6mOiLKScC51SzPGbAo2v4sU5Dgiytt"]
BASE_URL = "http://10.100.100.110:2999/v1"
MODEL_NAME = "gpt-5"

USEFUL_API_KEY_INDEX = 0

def chat_with_ai(input_text, api_keys=API_KEYS):
    global USEFUL_API_KEY_INDEX
    for api_key_index in range(len(api_keys)):
        index = (api_key_index + USEFUL_API_KEY_INDEX) % len(api_keys)
        current_api_key = api_keys[index]
        try:
            if not current_api_key.startswith('AIzaSy') or BASE_URL.startswith('http://'): # local ollama or vllm
                client = OpenAI(
                    base_url=BASE_URL,
                    api_key=current_api_key,
                    timeout=300.0,
                )

                completion = client.chat.completions.create(
                    model=MODEL_NAME,
                    messages=[
                        {
                            "role": "user",
                            "content": input_text
                        }
                    ]
                )

                response = completion.choices[0].message.content
            else:
                genai.configure(api_key=current_api_key)
                model = genai.GenerativeModel(MODEL_NAME)
                prompt_parts = [input_text]
                response = model.generate_content(prompt_parts).text

            USEFUL_API_KEY_INDEX = index
            return response

        except Exception as e:
            print(f"API Key {api_key_index + 1} 尝试失败: {e}")
            print(f"尝试下一个 API Key...")
            continue # 当前 API Key 失败，尝试下一个

    # 所有 API Key 失败，抛出异常
    raise Exception("所有 API Key 尝试失败，请检查 API Key 或网络连接。")

def get_priority_task(stack, current_blocks, goal_blocks):
    """动态生成当前修复栈的优先任务"""
    # 检查目标状态是否为空
    if not goal_blocks:
        return f"{stack}目标为空，无需进一步操作"

    # 情况 1：栈为空，优先移入目标栈底块
    if not current_blocks:
        return f"将{goal_blocks[0]}移入{stack}"

    # 情况 2：栈底错误，优先移走顶部错误块
    if current_blocks[0] != goal_blocks[0]:
        return f"移走{stack}顶部错误块{current_blocks[-1]}"

    # 情况 3：栈底正确，检查顶部是否匹配
    for i in range(min(len(current_blocks), len(goal_blocks))):
        if current_blocks[i] != goal_blocks[i]:
            return f"移走{stack}顶部错误块{current_blocks[-1]}"

    # 情况 4：当前栈块全部正确，移入下一块（如果有）
    if len(current_blocks) < len(goal_blocks):
        return f"移入目标状态的下一块{goal_blocks[len(current_blocks)]}"

    # 情况 5：栈已完全匹配目标状态
    return f"{stack}已匹配目标状态，无需操作"


class LLMGuidance:
    def __init__(self):
        self.model = MODEL_NAME

    def evaluate_actions(self, current_state, goal_state, planner, successors):
        current_fix_stack = current_state.current_fix_stack
        if current_fix_stack is None:
            return {"best_action": "uncertain", "best_reason": "all stacks fixed",
                    "worst_action": "uncertain", "worst_reason": "all stacks fixed"}, [], current_fix_stack

        def get_stack_order(stacks):
            stacks_desc = []
            for stack_name, blocks in stacks.items():
                if blocks:
                    block_strs = [str(b) for b in blocks]
                    stack_desc = f"{stack_name}: [{', '.join(block_strs)}]（底→顶）"
                    stacks_desc.append(stack_desc)
                else:
                    stacks_desc.append(f"{stack_name}: []（空栈）")
            return "; ".join(stacks_desc)

        def get_stack_issue(stack, current_blocks, goal_blocks):
            """动态生成当前修复栈的问题描述"""
            if not goal_blocks:
                return f"{stack}目标为空，无需修复"
            if not current_blocks:
                return f"{stack}为空，需移入目标栈底块{goal_blocks[0]}"
            if current_blocks[0] != goal_blocks[0]:
                return f"{stack}栈底应为{goal_blocks[0]}，当前为{current_blocks[0]}"
            for i in range(1, min(len(current_blocks), len(goal_blocks))):
                if current_blocks[i] != goal_blocks[i]:
                    return f"{stack}栈底正确，顶部块{current_blocks[i:]}需调整为{goal_blocks[i:]}"
            if len(current_blocks) < len(goal_blocks):
                return f"{stack}栈底正确，需添加{goal_blocks[len(current_blocks):]}"
            if len(current_blocks) > len(goal_blocks):
                return f"{stack}栈底正确，需移除多余块{current_blocks[len(goal_blocks):]}"
            return f"{stack}已匹配目标状态"

        def generate_state_description(state):
            stacks = state.stacks
            stack_order = get_stack_order(stacks)
            return f"- {stack_order}"

        current_desc = generate_state_description(current_state)
        goal_desc = generate_state_description(goal_state)

        fix_stack_curr = current_state.stacks[current_fix_stack]
        fix_stack_goal = goal_state.stacks.get(current_fix_stack, [])

        # 生成当前修复栈和目标修复栈的描述
        current_fix_desc = get_stack_order({current_fix_stack: fix_stack_curr})
        goal_fix_desc = get_stack_order({current_fix_stack: fix_stack_goal})

        # 生成动作描述
        stack_names = list(current_state.stacks.keys())
        actions_desc = []
        actions = []
        for i, (next_state, action) in enumerate(successors):
            result_desc = generate_state_description(next_state)
            i_stack, j_stack = action
            source_stack = stack_names[i_stack - 1]
            target_stack = stack_names[j_stack - 1]
            i_block = current_state.stacks[source_stack][-1]
            if current_state.stacks[target_stack]:
                j_block = current_state.stacks[target_stack][-1]
                desc = (f"Action {i + 1}: [{i_stack},{j_stack}] \n"
                        f"- 将{i_stack}的栈顶块{i_block}移动到{j_stack}的栈顶\n"
                        f"Result:\n{result_desc}")
            else:
                desc = (f"Action {i + 1}: [{i_stack},{j_stack}] \n"
                        f"- 将{i_stack}的栈顶块{i_block}移动到{j_stack}的栈顶\n"
                        f"Result:\n{result_desc}")
            actions.append(action)
            actions_desc.append(desc)
        actions_text = "\n".join(actions_desc)

        prompt = (
            "你是一个Blocks World问题的专家。你的任务是评估一组可行动作，选出最优和最劣动作，帮助机器人手臂从当前状态接近目标状态。\n"
            "\n"
            "**问题背景**：\n"
            "Blocks World是一个积木堆叠问题，包含多个独立的垂直栈。每个栈由列表表示，**列表的第一个元素是栈底，最后一个元素是栈顶**。例如，[A, B, C]表示A在栈底，C在栈顶。机器人手臂一次只能执行一个动作：拿起某个栈顶的块并放到另一个栈顶。\n"
            "- 在构建目标状态时，**必须从栈底开始，逐层向上构建**。例如，对于目标状态[C, D, E]（底→顶），必须先放置C，再D，最后E。\n"
            "\n"
            "**核心规则**：\n"
            "1. 所有积木块都在栈内，每个栈是一个独立的垂直栈。\n"
            "2. 手臂只能拿起栈顶的块（即列表的最后一个元素），并将其放到另一个栈顶（即在另一个栈的列表末尾添加该块）。\n"
            "3. 栈具有先入后出（LIFO）特性：要访问栈中的某个块，必须先移走其上的所有块。\n"
            "4. 动作表示为 [i, j]，即将第 i 个栈的栈顶块移动到第 j 个栈的栈顶。\n"
            "5. 在移动块时，不仅要考虑当前栈的改进，还要评估该动作是否会阻碍未来将目标块移入当前修复栈或访问其他关键块。避免将块移动到未来需要访问的块之上。\n"
            "\n"
            "**当前修复栈**：\n"
            f"- **{current_fix_stack}**：当前状态为{current_fix_desc}，目标状态为{goal_fix_desc}。\n"
            f"- 当前问题：{get_stack_issue(current_fix_stack, fix_stack_curr, fix_stack_goal)}。\n"
            f"- 优先任务：{get_priority_task(current_fix_stack, fix_stack_curr, fix_stack_goal)}。\n"
            f"- **特别注意**：\n"
            f"- 在修复StackX时，首要任务是确保StackX的栈底是目标状态中的栈底块。在栈底放置正确之前，不应将任何其他块放入StackX。\n"
            f"- 在选择动作时，不仅要关注当前修复栈的局部改进，还要考虑该动作是否会阻碍后续将正确块移入当前修复栈。\n"
            
            "\n"
            "**任务**：\n"
            "根据当前状态、目标状态和可行动作列表，选出：\n"
            f"- **最优动作(优先级)**：优先选择能直接减少当前修复栈（{current_fix_stack}）与目标状态差异的动作。\n"
            "1. 移走当前修复栈顶部的错误块，优先移至空栈或无关栈。\n"
            "2. 将目标栈底的正确块移入当前修复栈。\n"
            "3. 若目标块被其他块阻挡，优先将阻挡块移动到其他栈（非当前修复栈）。\n"
            f"- **最劣动作（优先级）**：选择增加当前修复栈与目标状态差异或无助于减少差异的动作。\n"
            "1. 移走当前修复栈中已正确的块。\n"
            "2. 将不属于目标状态的块移入当前修复栈。\n"
            "3. 将块移动到未来需要访问的块之上。\n"
            "- 即使动作影响相近，也要根据次要标准（如对其他栈影响最小，或移动距离最短）选出最优和最劣动作。\n"
            "\n"
            "**当前状态**：\n"
            f"{current_desc}\n"
            "\n"
            "**目标状态**：\n"
            f"{goal_desc}\n"
            "\n"
            "**可行动作及其后果**：\n"
            f"{actions_text}\n"
            "**要求**：\n"
            "1. **动作质量**：\n"
            "   - **进展性**：动作应增加当前修复栈与目标状态的匹配块数，或减少栈顶错误块数。\n"
            "   - **简洁性**：避免显著增加不必要移动。\n"
            "2. **置信度**：根据量化指标（如匹配块数、栈顶错误块数）给出明确结果。\n"
            "3. 输出格式：\n"
            "```json\n"
            "{\n"
            "  \"best_action\": <动作编号,比如1>,\n"
            "  \"best_reason\": \"<简洁理由，不超50字符>\",\n"
            "  \"worst_action\": <动作编号，比如1>,\n"
            "  \"worst_reason\": \"<简洁理由，不超50字符>\"\n"
            "}\n"
            "```\n"
            "\n"
            "**评估原则（优先级顺序）**：\n"
            "1. **当前修复栈的栈底调整**：\n"
            "   - 若当前修复栈为空或栈底与目标不符，优先将目标栈底的块移动到该栈，或移走顶部错误块。\n"
            "   - 若栈底正确，优先移入目标状态的下一块。\n"
            "2. **移除阻挡块**：\n"
            "   - 若目标块被其他块阻挡，优先将阻挡块移动到其他栈（非当前修复栈），以便后续将目标块移动到正确位置。\n"
            "3. **对其他栈的影响**：\n"
            "   - 避免将块移动到其他栈，导致其他栈的修复难度显著增加，除非必要。\n"
            "4. **避免冗余复杂性**：\n"
            "   - 避免将块移动到不必要的栈上，增加后续修复难度。\n"
            "5. **避免阻碍未来移动**：\n"
            "   - 移走阻挡块时，优先选择空栈或不包含未来需要移动块的栈，避免将高层块放在低层块之上，除非必要。\n"
            "\n"
            "**量化指标**：\n"
            "- **栈底正确性**：动作后当前修复栈的栈底是否与目标状态的栈底相同。\n"
            "- **匹配块数**：动作后当前修复栈与目标状态的匹配块数量（从栈底开始）。\n"
            "- **栈顶错误块数**：动作后当前修复栈顶部不属于目标状态的块数量。\n"
            "- **未来移动阻碍程度**：动作后，移动的块被放置在未来需要移动的目标块之上，若有，视为次优动作。\n"
            "- 优先级顺序：1. 栈底正确性；2. 匹配块数增加；3. 栈顶错误块数减少。避免使栈底错误的动作。\n"
            "\n"
            "**特别注意**：\n"
            f"- 优先考虑当前修复栈（{current_fix_stack}）的块内容和顺序完全匹配目标状态。\n"
            "- 其他栈可作为临时'桌面'使用，但应尽量减少对其结构的干扰。\n"
            "- 平衡对其他栈的影响，避免过度阻碍后续栈的修复。\n"
        )
        # print(prompt)
        try:
            response = chat_with_ai(prompt)
            matches = re.findall(r'```json\s*([\s\S]*?)\s*```', response, re.MULTILINE)
            if not matches:
                return {"best_action": "uncertain", "best_reason": "unknown", "worst_action": "uncertain",
                        "worst_reason": "unknown"}, actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                             fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
            try:
                return json.loads(matches[0]), actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                        fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
            except json.JSONDecodeError:
                return {"best_action": "uncertain", "best_reason": "unknown", "worst_action": "uncertain",
                        "worst_reason": "unknown"}, actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                             fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
        except Exception as e:
            # 捕获 chat_with_ai 的异常并终止探索
            raise Exception(f"AI交互失败: {str(e)}")


# 为了兼容性，保留 SimpleLLMGuidance 别名
SimpleLLMGuidance = LLMGuidance
