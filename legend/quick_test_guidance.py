#!/usr/bin/env python3
"""
快速测试CNN-Transformer指导功能是否正常工作
"""

import os
import sys
import re
import ast

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

try:
    from limited_astar_guidance import LimitedGraphPlanningBlocksWorld
    from cnn_transformer_guidance import CNNTransformerGuidance
    print("✅ 成功导入所需模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def parse_instance_file(file_path):
    """解析实例文件"""
    data = {}
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            start_state_match = re.search(r"Start State:\s*(\{.*\})", content)
            if start_state_match: 
                data['start_state'] = ast.literal_eval(start_state_match.group(1))
            g_canonical_match = re.search(r"G_canonical:\s*(\{.*\})", content)
            if g_canonical_match: 
                data['g_canonical'] = ast.literal_eval(g_canonical_match.group(1))
            fix_order_match = re.search(r"Fix Order:\s*(\[.*\])", content)
            if fix_order_match: 
                data['fix_order'] = ast.literal_eval(fix_order_match.group(1))
        return data
    except Exception as e:
        print(f"❌ 解析实例文件失败: {e}")
        return None

def quick_test():
    """快速测试指导功能"""
    print("=" * 60)
    print("快速测试CNN-Transformer指导功能")
    print("=" * 60)
    
    # 文件路径
    instance_path = os.path.join(SCRIPT_DIR, "instance_28.txt")
    model_path = os.path.join(SCRIPT_DIR, "models", "cnn_transformer_guidance.pth")
    
    # 解析实例
    instance_data = parse_instance_file(instance_path)
    if not instance_data:
        return
    
    start_state = instance_data['start_state']
    g_canonical = instance_data['g_canonical']
    fix_order = instance_data['fix_order']
    
    print(f"起始状态: {start_state}")
    print(f"目标状态: {g_canonical}")
    
    # 加载指导模型
    try:
        guidance = CNNTransformerGuidance(model_path)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 创建规划器并获取初始后继状态
    try:
        planner = LimitedGraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            max_nodes=10  # 只测试很少的节点
        )
        
        # 获取初始状态的后继状态
        planner.state.stacks = start_state
        successors = planner.get_successors()
        
        print(f"\n找到 {len(successors)} 个后继状态")
        
        # 转换格式并测试指导
        converted_successors = [(next_state.stacks, action) for next_state, action in successors]
        
        print("\n测试指导功能...")
        result = guidance.evaluate_actions(start_state, g_canonical, planner, converted_successors)
        
        print("✅ 指导功能测试成功!")
        print(f"最佳动作: {result['best_action']}")
        print(f"最差动作: {result['worst_action']}")
        print(f"置信度: {result['confidence']:.4f}")
        
    except Exception as e:
        print(f"❌ 指导功能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()