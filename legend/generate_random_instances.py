#!/usr/bin/env python3
"""
生成大量5栈15块的随机Blocks World算例
用于训练CNN-Transformer指导模型
"""

import os
import random
import json
from typing import Dict, List, Tuple
import itertools

def generate_random_start_state(num_stacks: int = 5, num_blocks: int = 15) -> Dict[str, List[int]]:
    """
    生成随机的起始状态
    
    Args:
        num_stacks: 栈的数量
        num_blocks: 块的数量
        
    Returns:
        随机起始状态字典
    """
    # 创建块列表
    blocks = list(range(1, num_blocks + 1))
    random.shuffle(blocks)
    
    # 创建栈
    stacks = {f"Stack{i+1}": [] for i in range(num_stacks)}
    stack_names = list(stacks.keys())
    
    # 随机分配块到栈中
    for block in blocks:
        stack_name = random.choice(stack_names)
        stacks[stack_name].append(block)
    
    return stacks

def generate_random_goal_state(num_stacks: int = 5, num_blocks: int = 15) -> Dict[str, List[int]]:
    """
    生成随机的目标状态
    
    Args:
        num_stacks: 栈的数量
        num_blocks: 块的数量
        
    Returns:
        随机目标状态字典
    """
    # 创建块列表
    blocks = list(range(1, num_blocks + 1))
    random.shuffle(blocks)
    
    # 创建栈
    stacks = {f"Stack{i+1}": [] for i in range(num_stacks)}
    stack_names = list(stacks.keys())
    
    # 随机分配块到栈中，但确保分布相对均匀
    for i, block in enumerate(blocks):
        # 使用轮转方式确保每个栈都有一些块
        if i < num_stacks:
            stack_name = stack_names[i]
        else:
            stack_name = random.choice(stack_names)
        stacks[stack_name].append(block)
    
    return stacks

def generate_tower_goal_state(num_stacks: int = 5, num_blocks: int = 15) -> Dict[str, List[int]]:
    """
    生成塔状目标状态（所有块在一个栈中）
    
    Args:
        num_stacks: 栈的数量
        num_blocks: 块的数量
        
    Returns:
        塔状目标状态字典
    """
    stacks = {f"Stack{i+1}": [] for i in range(num_stacks)}
    
    # 随机选择一个栈放置所有块
    target_stack = random.choice(list(stacks.keys()))
    
    # 生成随机顺序的块序列
    blocks = list(range(1, num_blocks + 1))
    random.shuffle(blocks)
    
    stacks[target_stack] = blocks
    
    return stacks

def generate_sorted_goal_state(num_stacks: int = 5, num_blocks: int = 15) -> Dict[str, List[int]]:
    """
    生成排序目标状态（块按某种顺序排列）
    
    Args:
        num_stacks: 栈的数量
        num_blocks: 块的数量
        
    Returns:
        排序目标状态字典
    """
    stacks = {f"Stack{i+1}": [] for i in range(num_stacks)}
    
    # 选择排序方式
    sort_type = random.choice(['ascending', 'descending', 'mixed'])
    
    if sort_type == 'ascending':
        # 升序：小块在底部
        target_stack = random.choice(list(stacks.keys()))
        stacks[target_stack] = list(range(1, num_blocks + 1))
    elif sort_type == 'descending':
        # 降序：大块在底部
        target_stack = random.choice(list(stacks.keys()))
        stacks[target_stack] = list(range(num_blocks, 0, -1))
    else:
        # 混合：在多个栈中排序
        blocks = list(range(1, num_blocks + 1))
        random.shuffle(blocks)
        
        # 将块分配到栈中并在每个栈内排序
        blocks_per_stack = num_blocks // num_stacks
        remainder = num_blocks % num_stacks
        
        start_idx = 0
        for i, stack_name in enumerate(stacks.keys()):
            end_idx = start_idx + blocks_per_stack + (1 if i < remainder else 0)
            stack_blocks = blocks[start_idx:end_idx]
            stack_blocks.sort()  # 栈内升序
            stacks[stack_name] = stack_blocks
            start_idx = end_idx
    
    return stacks

def is_valid_instance(start_state: Dict, goal_state: Dict) -> bool:
    """
    检查算例是否有效（起始状态和目标状态不同）
    
    Args:
        start_state: 起始状态
        goal_state: 目标状态
        
    Returns:
        是否有效
    """
    return start_state != goal_state

def estimate_difficulty(start_state: Dict, goal_state: Dict) -> int:
    """
    估算算例难度（需要移动的块数量）
    
    Args:
        start_state: 起始状态
        goal_state: 目标状态
        
    Returns:
        难度估计值
    """
    # 计算位置不匹配的块数量
    misplaced_blocks = 0
    
    # 为每个状态创建块位置映射
    def get_block_positions(state):
        positions = {}
        for stack_name, blocks in state.items():
            for pos, block in enumerate(blocks):
                positions[block] = (stack_name, pos)
        return positions
    
    start_positions = get_block_positions(start_state)
    goal_positions = get_block_positions(goal_state)
    
    for block in start_positions:
        if start_positions[block] != goal_positions[block]:
            misplaced_blocks += 1
    
    return misplaced_blocks

def generate_instance_batch(batch_size: int = 100, num_stacks: int = 5, num_blocks: int = 15) -> List[Dict]:
    """
    生成一批随机算例
    
    Args:
        batch_size: 批次大小
        num_stacks: 栈数量
        num_blocks: 块数量
        
    Returns:
        算例列表
    """
    instances = []
    
    goal_generators = [
        generate_random_goal_state,
        generate_tower_goal_state,
        generate_sorted_goal_state
    ]
    
    attempts = 0
    max_attempts = batch_size * 10  # 防止无限循环
    
    while len(instances) < batch_size and attempts < max_attempts:
        attempts += 1
        
        # 生成起始状态
        start_state = generate_random_start_state(num_stacks, num_blocks)
        
        # 随机选择目标状态生成方式
        goal_generator = random.choice(goal_generators)
        goal_state = goal_generator(num_stacks, num_blocks)
        
        # 检查有效性
        if not is_valid_instance(start_state, goal_state):
            continue
        
        # 估算难度
        difficulty = estimate_difficulty(start_state, goal_state)
        
        # 跳过过于简单的算例
        if difficulty < 3:
            continue
        
        # 生成修复顺序
        stack_names = list(start_state.keys())
        fix_order = stack_names.copy()
        random.shuffle(fix_order)
        
        instance = {
            "id": len(instances) + 1,
            "start_state": start_state,
            "goal_state": goal_state,
            "fix_order": fix_order,
            "difficulty": difficulty,
            "num_stacks": num_stacks,
            "num_blocks": num_blocks
        }
        
        instances.append(instance)
    
    return instances

def save_instances_to_files(instances: List[Dict], output_dir: str):
    """
    将算例保存到文件
    
    Args:
        instances: 算例列表
        output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为JSON格式（便于程序读取）
    json_file = os.path.join(output_dir, "random_instances.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(instances, f, indent=2, ensure_ascii=False)
    
    # 保存为单独的文本文件（便于人工查看）
    for instance in instances:
        filename = f"instance_{instance['id']:03d}.txt"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"Instance ID: {instance['id']}\n")
            f.write(f"Difficulty: {instance['difficulty']}\n")
            f.write(f"Num Stacks: {instance['num_stacks']}\n")
            f.write(f"Num Blocks: {instance['num_blocks']}\n")
            f.write(f"Start State: {instance['start_state']}\n")
            f.write(f"G_canonical: {instance['goal_state']}\n")
            f.write(f"Fix Order: {instance['fix_order']}\n")
    
    print(f"✅ 已生成 {len(instances)} 个算例")
    print(f"   - JSON文件: {json_file}")
    print(f"   - 文本文件: {output_dir}/instance_*.txt")

def analyze_instances(instances: List[Dict]):
    """
    分析生成的算例统计信息
    
    Args:
        instances: 算例列表
    """
    if not instances:
        print("❌ 没有算例可分析")
        return
    
    difficulties = [inst['difficulty'] for inst in instances]
    
    print("\n📊 算例统计分析:")
    print(f"   - 总数量: {len(instances)}")
    print(f"   - 难度范围: {min(difficulties)} - {max(difficulties)}")
    print(f"   - 平均难度: {sum(difficulties) / len(difficulties):.2f}")
    
    # 难度分布
    difficulty_counts = {}
    for diff in difficulties:
        difficulty_counts[diff] = difficulty_counts.get(diff, 0) + 1
    
    print("   - 难度分布:")
    for diff in sorted(difficulty_counts.keys()):
        count = difficulty_counts[diff]
        percentage = count / len(instances) * 100
        print(f"     难度 {diff}: {count} 个 ({percentage:.1f}%)")

def main():
    """主函数"""
    print("=" * 80)
    print("生成5栈15块随机Blocks World算例")
    print("=" * 80)
    
    # 配置参数
    BATCH_SIZE = 500  # 生成500个算例
    NUM_STACKS = 5
    NUM_BLOCKS = 15
    OUTPUT_DIR = "random_instances"
    
    print(f"配置参数:")
    print(f"  - 批次大小: {BATCH_SIZE}")
    print(f"  - 栈数量: {NUM_STACKS}")
    print(f"  - 块数量: {NUM_BLOCKS}")
    print(f"  - 输出目录: {OUTPUT_DIR}")
    
    # 设置随机种子以确保可重现性
    random.seed(42)
    
    print(f"\n🎲 开始生成随机算例...")
    instances = generate_instance_batch(BATCH_SIZE, NUM_STACKS, NUM_BLOCKS)
    
    if not instances:
        print("❌ 未能生成任何有效算例")
        return
    
    # 分析算例
    analyze_instances(instances)
    
    # 保存算例
    print(f"\n💾 保存算例到文件...")
    save_instances_to_files(instances, OUTPUT_DIR)
    
    print(f"\n✅ 算例生成完成!")
    print(f"   下一步: 使用这些算例收集LLM指导的训练数据")

if __name__ == "__main__":
    main()