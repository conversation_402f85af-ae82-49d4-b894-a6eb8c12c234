import os
import json
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader, Subset

from cnn_transformer_legend import BlocksWorldDataset, CNNTransformerClassifier

def infer_shape_from_onehot_header(header):
    """从one-hot编码格式的CSV头部推断数据形状"""
    # 按层分组分析
    by_layer = {}
    for col in header:
        if col.startswith('goal_'):
            layer = 'goal'
        elif col.startswith('current_'):
            layer = 'current'  
        elif col.startswith('action_'):
            action_num = col.split('_')[1]
            layer = f'action_{action_num}'
        else:
            layer = 'unknown'
            
        if layer not in by_layer:
            by_layer[layer] = []
        by_layer[layer].append(col)

    # 从特征总数推断形状
    features_per_layer = len(by_layer.get('goal', []))
    n_layers = len(by_layer)
    
    # 对于320特征/层，使用16x20
    if features_per_layer == 320:
        n_rows, n_cols = 16, 20
    else:
        # 尝试因数分解
        for r in range(10, 30):
            if features_per_layer % r == 0:
                c = features_per_layer // r
                if 10 <= c <= 30:
                    n_rows, n_cols = r, c
                    break
        else:
            n_rows, n_cols = 16, 20  # 默认值
    
    return (n_layers, n_rows, n_cols)

def create_sequential_splits(dataset_size, val_ratio=0.2, test_ratio=0.1):
    """创建顺序切分"""
    val_size = int(dataset_size * val_ratio)
    test_size = int(dataset_size * test_ratio)
    train_size = dataset_size - val_size - test_size
    
    train_indices = list(range(train_size))
    val_indices = list(range(train_size, train_size + val_size))
    test_indices = list(range(train_size + val_size, dataset_size))
    
    return train_indices, val_indices, test_indices

def eval_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    total_loss = 0.0
    
    # Best头指标
    best_correct_top1 = 0
    best_correct_top3 = 0  
    best_correct_top5 = 0
    best_total = 0
    
    # Worst头指标
    worst_correct_top1 = 0
    worst_correct_top3 = 0
    worst_correct_top5 = 0
    worst_total = 0
    
    criterion = torch.nn.CrossEntropyLoss(ignore_index=-1)
    
    with torch.no_grad():
        for data, labels in data_loader:
            data = data.to(device)
            labels = labels.to(device)
            
            best_logits, worst_logits = model(data)
            
            if labels.dim() == 1:
                # 单标签格式
                best_labels = labels
                worst_labels = torch.full_like(labels, -1)
            else:
                # 双标签格式
                best_labels = labels[:, 0]
                worst_labels = labels[:, 1]
            
            # 计算损失
            best_loss = criterion(best_logits, best_labels)
            worst_loss = criterion(worst_logits, worst_labels)
            total_loss += (best_loss + 0.1 * worst_loss).item()
            
            # Best头评估
            best_valid_mask = best_labels != -1
            if best_valid_mask.sum() > 0:
                best_probs = torch.softmax(best_logits[best_valid_mask], dim=-1)
                best_targets = best_labels[best_valid_mask]
                
                # Top-k准确率
                _, best_pred_top5 = torch.topk(best_probs, min(5, best_probs.size(1)), dim=1)
                _, best_pred_top3 = torch.topk(best_probs, min(3, best_probs.size(1)), dim=1)
                _, best_pred_top1 = torch.topk(best_probs, 1, dim=1)
                
                # 计算准确率
                for i, target in enumerate(best_targets):
                    if target in best_pred_top1[i]:
                        best_correct_top1 += 1
                    if target in best_pred_top3[i]:
                        best_correct_top3 += 1
                    if target in best_pred_top5[i]:
                        best_correct_top5 += 1
                
                best_total += best_valid_mask.sum().item()
            
            # Worst头评估
            worst_valid_mask = worst_labels != -1
            if worst_valid_mask.sum() > 0:
                worst_probs = torch.softmax(worst_logits[worst_valid_mask], dim=-1)
                worst_targets = worst_labels[worst_valid_mask]
                
                # Top-k准确率
                _, worst_pred_top5 = torch.topk(worst_probs, min(5, worst_probs.size(1)), dim=1)
                _, worst_pred_top3 = torch.topk(worst_probs, min(3, worst_probs.size(1)), dim=1)
                _, worst_pred_top1 = torch.topk(worst_probs, 1, dim=1)
                
                # 计算准确率
                for i, target in enumerate(worst_targets):
                    if target in worst_pred_top1[i]:
                        worst_correct_top1 += 1
                    if target in worst_pred_top3[i]:
                        worst_correct_top3 += 1
                    if target in worst_pred_top5[i]:
                        worst_correct_top5 += 1
                
                worst_total += worst_valid_mask.sum().item()
    
    # 计算最终指标
    results = {
        'loss': total_loss / len(data_loader),
        'best_top1': best_correct_top1 / best_total if best_total > 0 else 0.0,
        'best_top3': best_correct_top3 / best_total if best_total > 0 else 0.0,
        'best_top5': best_correct_top5 / best_total if best_total > 0 else 0.0,
        'best_total': best_total,
        'worst_top1': worst_correct_top1 / worst_total if worst_total > 0 else 0.0,
        'worst_top3': worst_correct_top3 / worst_total if worst_total > 0 else 0.0,
        'worst_top5': worst_correct_top5 / worst_total if worst_total > 0 else 0.0,
        'worst_total': worst_total,
    }
    
    return results

def main():
    # 加载元数据
    with open('models/fullsense_metadata.json', 'r') as f:
        metadata = json.load(f)
    
    print(f"模型架构: {metadata.get('architecture', 'unknown')}")
    print(f"训练轮数: {metadata['epoch']}")
    print(f"最佳验证损失: {metadata['val_loss']:.4f}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = CNNTransformerClassifier(
        n_layers=metadata['n_layers'],
        n_rows=metadata['n_rows'],
        n_cols=metadata['n_cols'],
        embed_dim=metadata['embed_dim'],
        n_heads=metadata['n_heads'],
        n_hidden=metadata['n_hidden'],
        n_classes=metadata['n_classes'],
        num_transformer_layers=metadata['num_transformer_layers'],
        classifier_dropout_rate=metadata['classifier_dropout'],
    ).to(device)
    
    # 加载权重
    model.load_state_dict(torch.load('models/fullsense_model.pth', map_location=device))
    print("✅ 模型加载成功")
    
    # 加载数据
    df = pd.read_csv('llm_guided_samples_matrix.csv')
    orig_shape = infer_shape_from_onehot_header(df.columns.tolist())
    
    dataset = BlocksWorldDataset(
        matrix_file='llm_guided_samples_matrix.csv',
        label_file='llm_guided_samples_labels.csv',
        orig_shape=orig_shape,
        new_shape=orig_shape
    )
    
    # 创建数据切分
    train_indices, val_indices, test_indices = create_sequential_splits(len(dataset))
    
    val_dataset = Subset(dataset, val_indices)
    test_dataset = Subset(dataset, test_indices)
    
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)
    
    # 评估模型
    print("\n=== 验证集性能 ===")
    val_results = eval_model(model, val_loader, device)
    for key, value in val_results.items():
        print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")
    
    print("\n=== 测试集性能 ===")
    test_results = eval_model(model, test_loader, device)
    for key, value in test_results.items():
        print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")
    
    # 对比分析
    print(f"\n=== 完整感知架构 vs 之前结果对比 ===")
    print(f"数据集大小: {len(dataset)} (vs 之前726)")
    print(f"测试集Best Top-1: {test_results['best_top1']:.4f} (vs 之前最佳0.2105)")
    print(f"模型参数: 186,888 (vs 之前约500k)")
    print(f"训练轮数: {metadata['epoch']} (vs 之前71-171轮)")
    
if __name__ == "__main__":
    main()
