import os
import json
import argparse
import time
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader, Subset

# Local imports (script is placed inside legend/)
from train_guidance_model_fixed import infer_shape_from_csv_header, create_sequential_splits
from cnn_transformer_legend import BlocksWorldDataset, CNNTransformerClassifier

try:
    import matplotlib.pyplot as plt
    HAS_MPL = True
except Exception:
    HAS_MPL = False

from sklearn.metrics import confusion_matrix


def ensure_dir(p):
    os.makedirs(p, exist_ok=True)
    return p


def load_metadata(metadata_path: str = None):
    # If a specific metadata file is provided, use it
    if metadata_path:
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r', encoding='utf-8') as f:
                md = json.load(f)
            md['__path__'] = metadata_path
            return md
        else:
            print(f"Warning: metadata file not found: {metadata_path}")
    # Fallback priority: tuned metadata then fixed
    candidates = [
        os.path.join(os.path.dirname(__file__), 'models', 'metadata_t3_d03.json'),
        os.path.join(os.path.dirname(__file__), 'models', 'metadata_fixed.json'),
    ]
    for c in candidates:
        if os.path.exists(c):
            with open(c, 'r', encoding='utf-8') as f:
                md = json.load(f)
            md['__path__'] = c
            return md
    return None


def load_model(metadata, device, override_model_path: str = None):
    # pick model path based on metadata file or override
    if metadata is None and not override_model_path:
        return None, None
    if override_model_path:
        model_path = override_model_path
    else:
        base = os.path.basename(metadata.get('__path__', ''))
        # default paired filename
        if base.startswith('metadata_') and base.endswith('.json'):
            model_name = 'cnn_transformer_guidance' + base[len('metadata'):-len('.json')] + '.pth'
            model_path = os.path.join(os.path.dirname(metadata['__path__']), model_name)
        else:
            model_path = os.path.join(os.path.dirname(metadata['__path__']), 'cnn_transformer_guidance_fixed.pth')

    if not os.path.exists(model_path):
        return None, None

    model = CNNTransformerClassifier(
        n_layers=metadata['n_layers'] if metadata else 22,
        n_stacks=metadata.get('n_stacks', 5) if metadata else 5,
        max_blocks=metadata.get('max_blocks', 15) if metadata else 15,
        buffer_rows=metadata.get('buffer_rows', 0) if metadata else 0,
        buffer_cols=metadata.get('buffer_cols', 1) if metadata else 1,
        embed_dim=metadata.get('embed_dim', 64) if metadata else 64,
        n_heads=metadata.get('n_heads', 8) if metadata else 8,
        n_hidden=metadata.get('n_hidden', 256) if metadata else 256,
        n_classes=metadata.get('n_classes', 20) if metadata else 20,
        num_transformer_layers=metadata.get('num_transformer_layers', 6) if metadata else 6,
        classifier_dropout_rate=metadata.get('classifier_dropout', 0.1) if metadata else 0.1,
    ).to(device)

    state = torch.load(model_path, map_location=device)
    model.load_state_dict(state)
    model.eval()
    return model, model_path


def compute_label_stats(labels: np.ndarray):
    info = {}
    for idx, name in enumerate(['best', 'worst']):
        arr = labels[:, idx]
        valid = arr[arr != -1]
        if valid.size == 0:
            info[name] = {'valid': 0}
            continue
        uniq, cnt = np.unique(valid, return_counts=True)
        maj_acc = float(cnt.max() / valid.size)
        info[name] = {
            'count': int(arr.size),
            'valid': int(valid.size),
            'valid_ratio': float(valid.size / arr.size),
            'unique': int(uniq.size),
            'min': int(valid.min()),
            'max': int(valid.max()),
            'majority_baseline': maj_acc,
            'top5_classes': [(int(uniq[i]), int(cnt[i])) for i in np.argsort(-cnt)[:5]],
        }
    return info


def split_indices(n, val_ratio=0.2, test_ratio=0.1):
    return create_sequential_splits(n, val_ratio=val_ratio, test_ratio=test_ratio)


def eval_model(model, loader, device, topk=(1, 3, 5)):
    metrics = {
        'best': {f'top{k}': 0 for k in topk},
        'worst': {f'top{k}': 0 for k in topk},
        'best_total': 0,
        'worst_total': 0,
        'best_preds': [],
        'best_targets': [],
    }
    with torch.no_grad():
        for x, y in loader:
            x = x.to(device)
            y = y.to(device)
            best_logits, worst_logits = model(x)
            # best
            best_mask = y[:, 0] != -1
            if best_mask.any():
                targets = y[best_mask, 0]
                metrics['best_total'] += int(best_mask.sum().item())
                for k in topk:
                    topv, topi = torch.topk(best_logits[best_mask], k=k, dim=1)
                    match = (topi == targets.unsqueeze(1)).any(dim=1).sum().item()
                    metrics['best'][f'top{k}'] += int(match)
                metrics['best_preds'].append(best_logits[best_mask].argmax(dim=1).cpu().numpy())
                metrics['best_targets'].append(targets.cpu().numpy())
            # worst
            worst_mask = y[:, 1] != -1
            if worst_mask.any():
                targets = y[worst_mask, 1]
                metrics['worst_total'] += int(worst_mask.sum().item())
                for k in topk:
                    topv, topi = torch.topk(worst_logits[worst_mask], k=k, dim=1)
                    match = (topi == targets.unsqueeze(1)).any(dim=1).sum().item()
                    metrics['worst'][f'top{k}'] += int(match)
    # normalize
    for head in ['best', 'worst']:
        total = metrics[f'{head}_total']
        for k in topk:
            key = f'top{k}'
            metrics[head][key] = (metrics[head][key] / total) if total > 0 else 0.0
    # stack preds/targets
    if metrics['best_preds']:
        metrics['best_preds'] = np.concatenate(metrics['best_preds'])
    else:
        metrics['best_preds'] = np.array([], dtype=int)
    if metrics['best_targets']:
        metrics['best_targets'] = np.concatenate(metrics['best_targets'])
    else:
        metrics['best_targets'] = np.array([], dtype=int)
    return metrics


def plot_distribution(counts, title, out_png):
    if not HAS_MPL:
        return
    labels = list(counts.keys())
    values = [counts[k] for k in labels]
    plt.figure(figsize=(8, 3))
    plt.bar([str(k) for k in labels], values)
    plt.title(title)
    plt.xlabel('class index')
    plt.ylabel('count')
    plt.tight_layout()
    plt.savefig(out_png)
    plt.close()


def plot_confusion(cm, title, out_png):
    if not HAS_MPL:
        return
    plt.figure(figsize=(6, 5))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title(title)
    plt.colorbar()
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.tight_layout()
    plt.savefig(out_png)
    plt.close()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--matrix_csv', default=os.path.join(os.path.dirname(__file__), 'optimized_llm_guided_samples_matrix.csv'))
    parser.add_argument('--labels_csv', default=os.path.join(os.path.dirname(__file__), 'optimized_llm_guided_samples_labels.csv'))
    parser.add_argument('--metadata', default=None, help='Path to metadata json to evaluate a specific model')
    parser.add_argument('--model_path', default=None, help='Override model .pth path (optional)')
    parser.add_argument('--val_ratio', type=float, default=0.2)
    parser.add_argument('--test_ratio', type=float, default=0.1)
    parser.add_argument('--batch_size', type=int, default=64)
    args = parser.parse_args()

    ts = time.strftime('%Y%m%d_%H%M%S')
    report_dir = ensure_dir(os.path.join(os.path.dirname(__file__), 'reports', ts))

    # 1) Label-only stats
    labels_np = pd.read_csv(args.labels_csv).values
    overall_stats = compute_label_stats(labels_np)

    with open(os.path.join(report_dir, 'label_stats_overall.json'), 'w', encoding='utf-8') as f:
        json.dump(overall_stats, f, ensure_ascii=False, indent=2)

    # per split
    n = labels_np.shape[0]
    train_idx, val_idx, test_idx = split_indices(n, args.val_ratio, args.test_ratio)
    split_stats = {}
    for name, idxs in [('train', train_idx), ('val', val_idx), ('test', test_idx)]:
        split_stats[name] = compute_label_stats(labels_np[idxs])
    with open(os.path.join(report_dir, 'label_stats_splits.json'), 'w', encoding='utf-8') as f:
        json.dump(split_stats, f, ensure_ascii=False, indent=2)

    # plots for distributions (best)
    if HAS_MPL:
        for name, idxs in [('train', train_idx), ('val', val_idx), ('test', test_idx)]:
            arr = labels_np[idxs, 0]
            valid = arr[arr != -1]
            if valid.size:
                uniq, cnt = np.unique(valid, return_counts=True)
                counts = {int(k): int(v) for k, v in zip(uniq, cnt)}
                plot_distribution(counts, f'Best label distribution ({name})', os.path.join(report_dir, f'best_dist_{name}.png'))

    # 2) Model-based metrics (if model available)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    metadata = load_metadata(args.metadata)
    model, model_path = load_model(metadata, device, override_model_path=args.model_path)
    model_metrics = {}
    if model is not None:
        # infer shape
        header = pd.read_csv(args.matrix_csv, nrows=0).columns.tolist()
        n_layers, n_rows, n_cols = infer_shape_from_csv_header(header)
        shape = (n_layers, n_rows, n_cols)
        dataset = BlocksWorldDataset(args.matrix_csv, args.labels_csv, shape, shape)
        val_loader = DataLoader(Subset(dataset, val_idx), batch_size=args.batch_size, shuffle=False)
        test_loader = DataLoader(Subset(dataset, test_idx), batch_size=args.batch_size, shuffle=False)
        # evaluate
        model_metrics['val'] = eval_model(model, val_loader, device)
        model_metrics['test'] = eval_model(model, test_loader, device)
        # confusion matrix for best on test
        y_true = model_metrics['test']['best_targets']
        y_pred = model_metrics['test']['best_preds']
        if y_true.size and y_pred.size:
            cm = confusion_matrix(y_true, y_pred, labels=list(range(metadata.get('n_classes', 20))))
            cm_path = os.path.join(report_dir, 'confusion_best_test.npy')
            np.save(cm_path, cm)
            plot_confusion(cm, 'Confusion Matrix (Best, Test)', os.path.join(report_dir, 'confusion_best_test.png'))
    else:
        model_metrics['info'] = 'No model file found; skipped model-based evaluation.'

    with open(os.path.join(report_dir, 'model_metrics.json'), 'w', encoding='utf-8') as f:
        json.dump(model_metrics, f, ensure_ascii=False, indent=2, default=lambda o: o if isinstance(o, (int, float, str)) else None)

    print('Diagnostics written to:', report_dir)
    if model is not None:
        print('Evaluated model:', model_path)


if __name__ == '__main__':
    main()

