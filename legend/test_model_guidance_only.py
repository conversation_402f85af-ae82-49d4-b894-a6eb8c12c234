#!/usr/bin/env python3
"""
直接测试CNN-Transformer模型指导功能
不运行A*搜索，只测试模型的推理能力
"""

import os
import sys
import re
import ast
import json

# 添加路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

try:
    from cnn_transformer_guidance import CNNTransformerGuidance
    print("✅ 成功导入 CNNTransformerGuidance")
except ImportError as e:
    print(f"❌ 导入CNN-Transformer指导模块失败: {e}")
    sys.exit(1)

def parse_instance_file(file_path):
    """解析实例文件"""
    data = {}
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            start_state_match = re.search(r"Start State:\s*(\{.*\})", content)
            if start_state_match: 
                data['start_state'] = ast.literal_eval(start_state_match.group(1))
            g_canonical_match = re.search(r"G_canonical:\s*(\{.*\})", content)
            if g_canonical_match: 
                data['g_canonical'] = ast.literal_eval(g_canonical_match.group(1))
            fix_order_match = re.search(r"Fix Order:\s*(\[.*\])", content)
            if fix_order_match: 
                data['fix_order'] = ast.literal_eval(fix_order_match.group(1))
        return data
    except Exception as e:
        print(f"❌ 解析实例文件 {file_path} 失败: {e}")
        return None

def generate_mock_successors(current_state):
    """生成一些模拟的后继状态用于测试"""
    successors = []
    
    # 找到所有可能的移动
    for stack_name, blocks in current_state.items():
        if blocks:  # 如果栈不为空
            top_block = blocks[-1]  # 栈顶块
            
            # 尝试移动到其他栈
            for target_stack in current_state.keys():
                if target_stack != stack_name:
                    # 创建移动后的状态
                    new_state = {k: v.copy() for k, v in current_state.items()}
                    new_state[stack_name].pop()  # 从源栈移除
                    new_state[target_stack].append(top_block)  # 添加到目标栈
                    
                    action = (top_block, stack_name, target_stack)
                    successors.append((new_state, action))
                    
                    # 限制生成的后继状态数量
                    if len(successors) >= 10:
                        break
        if len(successors) >= 10:
            break
    
    return successors

def test_model_guidance():
    """测试模型指导功能"""
    print("=" * 80)
    print("CNN-Transformer模型指导功能测试")
    print("=" * 80)
    
    # 文件路径
    instance_path = os.path.join(SCRIPT_DIR, "instance_28.txt")
    model_path = os.path.join(SCRIPT_DIR, "models", "cnn_transformer_guidance.pth")
    
    # 检查文件是否存在
    if not os.path.exists(instance_path):
        print(f"❌ 实例文件不存在: {instance_path}")
        return
        
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📁 实例文件: {instance_path}")
    print(f"🤖 模型文件: {model_path}")
    
    # 解析实例
    print("\n📖 解析实例文件...")
    instance_data = parse_instance_file(instance_path)
    if not instance_data:
        return
    
    start_state = instance_data['start_state']
    goal_state = instance_data['g_canonical']
    
    print("✅ 实例解析成功")
    print(f"   起始状态: {start_state}")
    print(f"   目标状态: {goal_state}")
    
    # 加载模型
    print("\n🤖 加载CNN-Transformer模型...")
    try:
        guidance = CNNTransformerGuidance(model_path)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 生成模拟后继状态
    print("\n🎯 生成模拟后继状态...")
    successors = generate_mock_successors(start_state)
    print(f"✅ 生成了 {len(successors)} 个后继状态")
    
    # 显示后继状态
    print("\n📋 后继状态列表:")
    for i, (next_state, action) in enumerate(successors):
        print(f"   {i}: {action} -> {next_state}")
    
    # 测试模型推理
    print("\n🧠 测试模型推理...")
    try:
        result = guidance.evaluate_actions(start_state, goal_state, None, successors)
        print("✅ 模型推理成功")
        
        print("\n📊 推理结果:")
        print(f"   最佳动作: {result['best_action']}")
        print(f"   最佳理由: {result['best_reason']}")
        print(f"   最差动作: {result['worst_action']}")
        print(f"   最差理由: {result['worst_reason']}")
        print(f"   置信度: {result['confidence']:.4f}")
        
        # 显示详细分数
        if 'all_scores' in result:
            print("\n📈 所有动作的详细分数:")
            for score_info in result['all_scores']:
                print(f"   动作 {score_info['action_idx']}: {score_info['action']}")
                print(f"      最佳预测: {score_info['best_pred']} (置信度: {score_info['best_confidence']:.4f})")
                print(f"      最差预测: {score_info['worst_pred']} (置信度: {score_info['worst_confidence']:.4f})")
                print(f"      综合分数: {score_info['combined_score']:.4f}")
                print()
        
    except Exception as e:
        print(f"❌ 模型推理失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("=" * 80)
    print("✅ 测试完成")
    print("=" * 80)

if __name__ == "__main__":
    test_model_guidance()