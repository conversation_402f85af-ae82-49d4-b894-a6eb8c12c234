[{"id": 1, "start_state": {"Stack1": [9, 14, 8, 3], "Stack2": [7, 15, 4, 1], "Stack3": [], "Stack4": [12, 2], "Stack5": [13, 6, 10, 5, 11]}, "goal_state": {"Stack1": [10, 4, 11, 9, 1, 8, 2, 14, 5, 6, 7, 12, 3, 15, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 2, "start_state": {"Stack1": [15, 14, 11, 5], "Stack2": [4, 3, 8, 13], "Stack3": [6, 7, 9, 12, 1], "Stack4": [10, 2], "Stack5": []}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack2", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 3, "start_state": {"Stack1": [15], "Stack2": [12, 1, 9, 5, 7], "Stack3": [10, 2, 11], "Stack4": [13, 6, 4], "Stack5": [14, 8, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [11, 5, 14, 2, 12, 13, 1, 8, 9, 3, 4, 6, 15, 10, 7]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 4, "start_state": {"Stack1": [11, 12], "Stack2": [4, 6, 5, 9, 10], "Stack3": [15, 2, 8], "Stack4": [14], "Stack5": [3, 7, 1, 13]}, "goal_state": {"Stack1": [4, 5, 11], "Stack2": [3, 9, 13], "Stack3": [2, 7, 12], "Stack4": [6, 8, 14], "Stack5": [1, 10, 15]}, "fix_order": ["Stack2", "Stack3", "Stack5", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 5, "start_state": {"Stack1": [14, 2, 12, 3], "Stack2": [13, 10, 9, 11], "Stack3": [7], "Stack4": [1, 6], "Stack5": [4, 5, 15, 8]}, "goal_state": {"Stack1": [9, 1, 11], "Stack2": [7, 6, 4], "Stack3": [10], "Stack4": [15, 14, 13, 2, 12], "Stack5": [3, 8, 5]}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 6, "start_state": {"Stack1": [13, 11, 1, 14], "Stack2": [5, 9, 7], "Stack3": [3], "Stack4": [8, 10, 6, 2], "Stack5": [4, 15, 12]}, "goal_state": {"Stack1": [12], "Stack2": [5, 4, 13], "Stack3": [6, 11, 10, 15], "Stack4": [2, 9, 14, 8, 3], "Stack5": [1, 7]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 7, "start_state": {"Stack1": [14, 3, 8], "Stack2": [4, 7], "Stack3": [12], "Stack4": [11, 10], "Stack5": [2, 5, 15, 9, 13, 1, 6]}, "goal_state": {"Stack1": [], "Stack2": [13, 10, 2, 8, 1, 9, 14, 15, 3, 7, 5, 4, 6, 12, 11], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 8, "start_state": {"Stack1": [12, 1, 9], "Stack2": [13, 7, 14, 2], "Stack3": [11, 8, 15, 4, 6], "Stack4": [], "Stack5": [10, 5, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [4, 9, 10, 6, 12, 3, 8, 13, 5, 7, 11, 2, 1, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 9, "start_state": {"Stack1": [8, 15], "Stack2": [5, 11, 6, 1, 9, 3, 2], "Stack3": [7, 10], "Stack4": [4, 12], "Stack5": [14, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 10, "start_state": {"Stack1": [13, 4, 8], "Stack2": [14], "Stack3": [10, 15, 9, 2, 11, 1], "Stack4": [5, 6], "Stack5": [3, 12, 7]}, "goal_state": {"Stack1": [14, 11, 1, 3, 8, 5, 4, 15, 9, 13, 12, 6, 7, 10, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 11, "start_state": {"Stack1": [10], "Stack2": [12, 8, 4, 2], "Stack3": [5, 6, 11], "Stack4": [7, 3, 15], "Stack5": [1, 14, 9, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [9, 3, 5, 1, 2, 13, 7, 15, 4, 14, 12, 8, 6, 11, 10]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 12, "start_state": {"Stack1": [10, 5, 15], "Stack2": [7, 9, 14, 11, 4], "Stack3": [], "Stack4": [2, 6, 12, 8, 3], "Stack5": [1, 13]}, "goal_state": {"Stack1": [1], "Stack2": [2, 4, 9, 3], "Stack3": [14, 10, 11, 8], "Stack4": [12, 7, 6, 13], "Stack5": [5, 15]}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 13, "start_state": {"Stack1": [15, 9, 4], "Stack2": [10], "Stack3": [12], "Stack4": [8, 14, 7, 11, 3, 6, 5], "Stack5": [1, 2, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [6, 15, 1, 5, 11, 3, 2, 8, 4, 10, 12, 9, 14, 7, 13], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 14, "start_state": {"Stack1": [13, 3, 14], "Stack2": [4], "Stack3": [11, 2, 15, 12, 1, 10], "Stack4": [5, 6, 7, 9, 8], "Stack5": []}, "goal_state": {"Stack1": [3, 8, 14], "Stack2": [5, 7, 9], "Stack3": [10, 12, 13], "Stack4": [2, 11, 15], "Stack5": [1, 4, 6]}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 15, "start_state": {"Stack1": [9, 1, 2, 3], "Stack2": [7, 13], "Stack3": [15, 6, 8], "Stack4": [4, 12], "Stack5": [14, 11, 10, 5]}, "goal_state": {"Stack1": [10], "Stack2": [12], "Stack3": [5, 3, 11, 9], "Stack4": [15, 8, 7, 1], "Stack5": [4, 13, 14, 2, 6]}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 16, "start_state": {"Stack1": [15, 7], "Stack2": [6, 12, 4], "Stack3": [9, 5, 14, 2], "Stack4": [10, 8], "Stack5": [11, 3, 1, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 17, "start_state": {"Stack1": [11, 10], "Stack2": [14, 8, 15, 12, 7], "Stack3": [1, 9, 13, 3, 6], "Stack4": [2, 4], "Stack5": [5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [6, 8, 14, 1, 7, 11, 3, 15, 12, 4, 2, 5, 10, 9, 13]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 18, "start_state": {"Stack1": [6, 10, 7, 15, 11, 12], "Stack2": [5, 1, 3], "Stack3": [8], "Stack4": [9, 13, 14], "Stack5": [4, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 19, "start_state": {"Stack1": [6, 11, 5], "Stack2": [12, 9, 4, 14, 15], "Stack3": [10, 8], "Stack4": [2, 1, 7], "Stack5": [3, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 20, "start_state": {"Stack1": [13, 11, 9], "Stack2": [], "Stack3": [8, 15, 7], "Stack4": [6, 12, 14, 5, 3, 2], "Stack5": [1, 10, 4]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 21, "start_state": {"Stack1": [14, 12, 13], "Stack2": [9], "Stack3": [7, 15, 2, 8, 10], "Stack4": [6, 4, 3, 1, 5], "Stack5": [11]}, "goal_state": {"Stack1": [13, 14, 11, 3, 12, 10, 4, 8, 1, 9, 7, 2, 15, 5, 6], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 22, "start_state": {"Stack1": [11, 13, 14], "Stack2": [7, 10, 3], "Stack3": [4, 2], "Stack4": [15, 6, 12, 8, 5, 9], "Stack5": [1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack1", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 23, "start_state": {"Stack1": [], "Stack2": [5, 9, 15], "Stack3": [11, 4, 1], "Stack4": [2, 12, 10, 7, 13], "Stack5": [14, 8, 3, 6]}, "goal_state": {"Stack1": [4, 2, 3, 8], "Stack2": [15, 11, 14], "Stack3": [10, 6], "Stack4": [9, 13, 7, 12], "Stack5": [5, 1]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 24, "start_state": {"Stack1": [4, 10, 5, 1, 15], "Stack2": [2, 14], "Stack3": [12, 8, 7, 9, 11], "Stack4": [13, 3, 6], "Stack5": []}, "goal_state": {"Stack1": [15, 3], "Stack2": [1, 4, 9], "Stack3": [6, 2, 12, 10], "Stack4": [8, 5, 13, 11], "Stack5": [14, 7]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 25, "start_state": {"Stack1": [2, 15, 4], "Stack2": [12], "Stack3": [3, 6, 9, 5, 11], "Stack4": [1, 8], "Stack5": [10, 14, 13, 7]}, "goal_state": {"Stack1": [9, 14], "Stack2": [12, 3, 15, 5], "Stack3": [6, 13, 1, 4], "Stack4": [7, 2], "Stack5": [8, 10, 11]}, "fix_order": ["Stack4", "Stack2", "Stack1", "Stack3", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 26, "start_state": {"Stack1": [12, 3, 6, 14], "Stack2": [5], "Stack3": [11, 7, 9, 2], "Stack4": [4, 13, 8], "Stack5": [1, 10, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack4", "Stack5", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 27, "start_state": {"Stack1": [7], "Stack2": [6, 3], "Stack3": [4, 15], "Stack4": [13, 14, 8, 2, 11, 12], "Stack5": [10, 9, 1, 5]}, "goal_state": {"Stack1": [10, 2, 13, 4, 6], "Stack2": [11, 9, 15], "Stack3": [1, 3, 12], "Stack4": [7, 14], "Stack5": [5, 8]}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 28, "start_state": {"Stack1": [5, 15, 12, 13], "Stack2": [7, 1, 3, 10, 14], "Stack3": [6, 11, 9, 2], "Stack4": [], "Stack5": [8, 4]}, "goal_state": {"Stack1": [6, 13, 2, 9], "Stack2": [4], "Stack3": [10, 8], "Stack4": [1, 11, 7, 12, 3], "Stack5": [5, 15, 14]}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 29, "start_state": {"Stack1": [7, 13], "Stack2": [5, 3], "Stack3": [6], "Stack4": [1, 9, 11, 14, 10, 15, 2], "Stack5": [4, 12, 8]}, "goal_state": {"Stack1": [9, 4, 2, 12, 10, 14, 1, 8, 3, 13, 5, 11, 15, 7, 6], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack5", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 30, "start_state": {"Stack1": [3, 7, 14], "Stack2": [4, 8, 12], "Stack3": [5, 2, 6, 9], "Stack4": [13], "Stack5": [15, 1, 11, 10]}, "goal_state": {"Stack1": [14, 2, 12, 9, 7], "Stack2": [4, 11, 5, 10], "Stack3": [6], "Stack4": [8, 15], "Stack5": [3, 1, 13]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack4", "Stack2"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 31, "start_state": {"Stack1": [13, 3], "Stack2": [2], "Stack3": [11, 1, 5, 6], "Stack4": [4, 14, 8, 7, 9, 15], "Stack5": [12, 10]}, "goal_state": {"Stack1": [3, 13, 14], "Stack2": [2, 6, 15], "Stack3": [4, 7, 12], "Stack4": [1, 5, 11], "Stack5": [8, 9, 10]}, "fix_order": ["Stack1", "Stack4", "Stack5", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 32, "start_state": {"Stack1": [4, 14, 15, 8, 2], "Stack2": [], "Stack3": [6, 12], "Stack4": [13, 9, 11, 3, 7, 1], "Stack5": [5, 10]}, "goal_state": {"Stack1": [], "Stack2": [9, 1, 3, 11, 5, 4, 15, 14, 7, 2, 6, 12, 8, 10, 13], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 33, "start_state": {"Stack1": [7], "Stack2": [8, 1, 10, 12, 15], "Stack3": [5], "Stack4": [11, 9, 13], "Stack5": [14, 2, 3, 6, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [6, 4, 13, 7, 11, 9, 15, 1, 10, 5, 12, 14, 2, 8, 3], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 34, "start_state": {"Stack1": [14, 8, 9, 15], "Stack2": [1, 10, 11], "Stack3": [13, 7], "Stack4": [3, 5, 2], "Stack5": [6, 4, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [4, 2, 11, 1, 14, 13, 8, 15, 7, 5, 12, 6, 3, 10, 9]}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 35, "start_state": {"Stack1": [7, 3, 15], "Stack2": [1, 2], "Stack3": [13, 14, 12, 5], "Stack4": [9, 10], "Stack5": [4, 8, 6, 11]}, "goal_state": {"Stack1": [], "Stack2": [10, 2, 14, 5, 7, 12, 1, 9, 3, 11, 15, 8, 13, 6, 4], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack2", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 36, "start_state": {"Stack1": [], "Stack2": [5, 7, 4], "Stack3": [2, 1, 12], "Stack4": [9, 11, 13, 10, 14], "Stack5": [6, 8, 15, 3]}, "goal_state": {"Stack1": [], "Stack2": [2, 15, 6, 9, 12, 11, 7, 8, 1, 5, 3, 10, 13, 4, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 37, "start_state": {"Stack1": [13, 10, 6, 7], "Stack2": [5, 12, 4, 2, 8], "Stack3": [14, 1, 15, 3], "Stack4": [11, 9], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack2", "Stack3", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 38, "start_state": {"Stack1": [8, 13, 12], "Stack2": [15, 3, 4], "Stack3": [5], "Stack4": [14, 6, 11], "Stack5": [1, 2, 7, 10, 9]}, "goal_state": {"Stack1": [13, 12, 9, 11, 8, 5, 1, 14, 2, 3, 7, 15, 4, 6, 10], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 39, "start_state": {"Stack1": [3, 9, 4, 2], "Stack2": [10, 5, 15, 13], "Stack3": [11, 6, 1], "Stack4": [7, 8, 14], "Stack5": [12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [12, 2, 15, 10, 3, 9, 7, 13, 6, 14, 4, 11, 1, 5, 8]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 40, "start_state": {"Stack1": [9, 4], "Stack2": [3], "Stack3": [10, 2, 1, 14, 5, 7], "Stack4": [13, 8, 6, 15], "Stack5": [12, 11]}, "goal_state": {"Stack1": [8, 11, 5, 10], "Stack2": [9, 13, 7, 6], "Stack3": [15, 14, 12], "Stack4": [3, 1], "Stack5": [4, 2]}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 41, "start_state": {"Stack1": [9, 11], "Stack2": [7, 2, 8], "Stack3": [4, 12, 13], "Stack4": [5, 10, 3, 6], "Stack5": [1, 15, 14]}, "goal_state": {"Stack1": [4, 15, 14], "Stack2": [7, 1, 6, 13, 3, 8], "Stack3": [10, 11], "Stack4": [12, 2], "Stack5": [5, 9]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 42, "start_state": {"Stack1": [3, 14, 2], "Stack2": [4, 9, 8], "Stack3": [12], "Stack4": [13, 6, 7], "Stack5": [1, 15, 11, 5, 10]}, "goal_state": {"Stack1": [10, 12], "Stack2": [11, 1, 6, 5], "Stack3": [8, 7], "Stack4": [2], "Stack5": [13, 15, 9, 4, 14, 3]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 43, "start_state": {"Stack1": [7, 4], "Stack2": [12, 5, 15], "Stack3": [6, 8, 14], "Stack4": [3, 11, 9, 10, 13, 2, 1], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 44, "start_state": {"Stack1": [5], "Stack2": [3, 1, 2, 15], "Stack3": [10, 8, 14], "Stack4": [7, 12, 9, 13], "Stack5": [11, 4, 6]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [10, 6, 13, 12, 14, 3, 5, 11, 4, 15, 9, 7, 2, 1, 8], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 45, "start_state": {"Stack1": [11, 13, 5], "Stack2": [6], "Stack3": [4, 7, 2], "Stack4": [12, 15, 3], "Stack5": [8, 9, 1, 10, 14]}, "goal_state": {"Stack1": [], "Stack2": [8, 12, 5, 15, 3, 6, 2, 11, 14, 1, 13, 7, 9, 10, 4], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 46, "start_state": {"Stack1": [2, 11, 10], "Stack2": [12], "Stack3": [14, 15, 6], "Stack4": [1, 8, 3], "Stack5": [4, 9, 13, 5, 7]}, "goal_state": {"Stack1": [13, 9], "Stack2": [15, 1, 3], "Stack3": [14, 6], "Stack4": [2, 5, 4, 7, 11], "Stack5": [8, 10, 12]}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 47, "start_state": {"Stack1": [11], "Stack2": [4, 5], "Stack3": [2, 1, 13, 10, 14, 6, 9, 8], "Stack4": [15, 3], "Stack5": [12, 7]}, "goal_state": {"Stack1": [2, 8, 13], "Stack2": [4, 11, 15], "Stack3": [1, 10, 12], "Stack4": [3, 5, 6], "Stack5": [7, 9, 14]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack2", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 48, "start_state": {"Stack1": [8, 7, 6, 14], "Stack2": [1, 2, 9], "Stack3": [10, 15], "Stack4": [11, 13, 3, 5, 12], "Stack5": [4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [6, 3, 5, 8, 7, 9, 12, 14, 4, 10, 15, 2, 13, 1, 11], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 49, "start_state": {"Stack1": [13], "Stack2": [11, 4, 15, 6], "Stack3": [7, 3, 12, 9], "Stack4": [1, 14, 10], "Stack5": [8, 2, 5]}, "goal_state": {"Stack1": [15, 9, 5, 13, 10, 14, 1, 4, 6, 8, 12, 3, 2, 11, 7], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 50, "start_state": {"Stack1": [], "Stack2": [7, 14, 9, 8], "Stack3": [13, 10, 4], "Stack4": [3, 2, 12, 11], "Stack5": [15, 1, 6, 5]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 51, "start_state": {"Stack1": [15, 11, 4, 9], "Stack2": [3, 14], "Stack3": [], "Stack4": [13, 5, 1, 2, 6], "Stack5": [8, 7, 10, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 52, "start_state": {"Stack1": [7, 4], "Stack2": [8, 13, 2, 12], "Stack3": [9, 10], "Stack4": [3, 14, 11, 5, 15, 6], "Stack5": [1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 53, "start_state": {"Stack1": [15, 4, 14, 7, 9, 11], "Stack2": [1, 10, 13], "Stack3": [3], "Stack4": [6, 8], "Stack5": [2, 12, 5]}, "goal_state": {"Stack1": [3, 9, 10], "Stack2": [2, 6, 11], "Stack3": [5, 8, 13], "Stack4": [7, 12, 15], "Stack5": [1, 4, 14]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 54, "start_state": {"Stack1": [12], "Stack2": [15, 3], "Stack3": [2, 13, 9], "Stack4": [10, 8, 14, 7, 11, 4], "Stack5": [6, 1, 5]}, "goal_state": {"Stack1": [11, 1, 12, 14], "Stack2": [9, 15, 4], "Stack3": [10, 13, 7, 3], "Stack4": [2, 8], "Stack5": [6, 5]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 55, "start_state": {"Stack1": [7], "Stack2": [12, 6, 15], "Stack3": [13, 9, 2, 14], "Stack4": [5, 3, 11, 10], "Stack5": [1, 8, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [11, 5, 7, 12, 1, 15, 3, 10, 4, 6, 13, 2, 14, 8, 9], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 56, "start_state": {"Stack1": [7, 6, 4, 3, 5], "Stack2": [9, 10, 14, 8], "Stack3": [2], "Stack4": [1, 15], "Stack5": [12, 11, 13]}, "goal_state": {"Stack1": [], "Stack2": [6, 12, 7, 9, 5, 10, 15, 8, 13, 1, 3, 2, 4, 11, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 57, "start_state": {"Stack1": [5], "Stack2": [3, 8, 2], "Stack3": [10, 14, 11, 12], "Stack4": [13, 9, 15], "Stack5": [7, 4, 6, 1]}, "goal_state": {"Stack1": [9, 14, 11, 4], "Stack2": [3, 8, 12], "Stack3": [10, 1], "Stack4": [6, 7, 15], "Stack5": [13, 2, 5]}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack4", "Stack2"], "difficulty": 11, "num_stacks": 5, "num_blocks": 15}, {"id": 58, "start_state": {"Stack1": [12, 11], "Stack2": [14, 8, 5], "Stack3": [6, 10, 15], "Stack4": [4, 3, 9], "Stack5": [13, 7, 1, 2]}, "goal_state": {"Stack1": [5, 4], "Stack2": [9, 10], "Stack3": [12, 8, 2], "Stack4": [13, 11, 14, 15], "Stack5": [3, 1, 6, 7]}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 59, "start_state": {"Stack1": [4, 13, 11, 12, 7], "Stack2": [2, 8], "Stack3": [5, 3], "Stack4": [10, 1, 15], "Stack5": [9, 6, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 60, "start_state": {"Stack1": [15, 9], "Stack2": [13, 12, 7], "Stack3": [1, 6, 11, 2, 14], "Stack4": [8, 10, 3, 4, 5], "Stack5": []}, "goal_state": {"Stack1": [10, 13, 14], "Stack2": [11, 12, 15], "Stack3": [6, 7, 9], "Stack4": [1, 3, 5], "Stack5": [2, 4, 8]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 61, "start_state": {"Stack1": [2, 6], "Stack2": [14, 7, 4], "Stack3": [1, 5, 10], "Stack4": [9, 8, 12], "Stack5": [13, 15, 11, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 62, "start_state": {"Stack1": [9, 15, 14, 3], "Stack2": [8, 13], "Stack3": [12, 2, 4], "Stack4": [5], "Stack5": [1, 6, 10, 7, 11]}, "goal_state": {"Stack1": [14, 15, 6, 13, 7, 3, 8, 12, 10, 11, 9, 4, 5, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack2", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 63, "start_state": {"Stack1": [1, 9, 4, 10, 15, 3], "Stack2": [13, 2, 5], "Stack3": [14, 12, 6], "Stack4": [11, 8], "Stack5": [7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 64, "start_state": {"Stack1": [9, 5, 1, 13], "Stack2": [15, 4, 14, 11], "Stack3": [2, 12, 3], "Stack4": [8, 7], "Stack5": [6, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 5, 13, 8, 4, 15, 3, 2, 14, 12, 7, 10, 6, 11, 9], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 65, "start_state": {"Stack1": [6], "Stack2": [7, 9, 12, 3, 14], "Stack3": [15, 5], "Stack4": [1, 8, 10, 4], "Stack5": [11, 13, 2]}, "goal_state": {"Stack1": [14, 1, 2, 5, 10, 13, 6, 12, 15, 11, 9, 7, 4, 3, 8], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 66, "start_state": {"Stack1": [10, 11, 3, 5], "Stack2": [15, 12], "Stack3": [8, 9, 4], "Stack4": [2, 7, 14, 1], "Stack5": [13, 6]}, "goal_state": {"Stack1": [8], "Stack2": [12, 6], "Stack3": [1, 5, 10, 14, 4, 13], "Stack4": [9], "Stack5": [11, 2, 3, 7, 15]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 67, "start_state": {"Stack1": [8, 6], "Stack2": [], "Stack3": [14, 12, 3, 1], "Stack4": [13, 9, 2, 10, 11, 5], "Stack5": [7, 4, 15]}, "goal_state": {"Stack1": [1, 13, 14], "Stack2": [3, 6], "Stack3": [5, 4, 9, 12], "Stack4": [10, 2], "Stack5": [11, 7, 15, 8]}, "fix_order": ["Stack2", "Stack3", "Stack5", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 68, "start_state": {"Stack1": [11, 2, 12, 8, 3], "Stack2": [13, 7, 14], "Stack3": [5, 10, 9, 4], "Stack4": [6, 1, 15], "Stack5": []}, "goal_state": {"Stack1": [2, 6, 8, 11, 9], "Stack2": [5, 12], "Stack3": [7, 4, 10, 3], "Stack4": [13, 15, 1], "Stack5": [14]}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 69, "start_state": {"Stack1": [5, 11, 7, 9], "Stack2": [12, 4, 13, 1, 2], "Stack3": [6, 3, 10], "Stack4": [14, 8], "Stack5": [15]}, "goal_state": {"Stack1": [2, 7, 13], "Stack2": [3, 8, 11], "Stack3": [4, 6, 9], "Stack4": [1, 5, 14], "Stack5": [10, 12, 15]}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 70, "start_state": {"Stack1": [15, 12, 7, 5, 14, 6], "Stack2": [3], "Stack3": [4, 2], "Stack4": [13, 1, 11], "Stack5": [8, 9, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [7, 4, 6, 8, 12, 1, 14, 15, 5, 3, 10, 13, 9, 11, 2], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 71, "start_state": {"Stack1": [3, 9, 15, 12], "Stack2": [8, 6, 13, 4], "Stack3": [11, 2, 1, 14], "Stack4": [5], "Stack5": [10, 7]}, "goal_state": {"Stack1": [15, 9, 7], "Stack2": [8, 4, 12, 13], "Stack3": [1, 10, 2], "Stack4": [3], "Stack5": [11, 14, 5, 6]}, "fix_order": ["Stack2", "Stack3", "Stack4", "Stack5", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 72, "start_state": {"Stack1": [5, 14, 2, 3, 6], "Stack2": [12, 15], "Stack3": [9, 4, 1], "Stack4": [7, 8], "Stack5": [13, 10, 11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [12, 3, 7, 10, 15, 13, 11, 14, 5, 4, 6, 1, 8, 9, 2], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 73, "start_state": {"Stack1": [12, 10, 13, 15], "Stack2": [5, 8, 6], "Stack3": [11, 1], "Stack4": [14, 3, 4, 7], "Stack5": [2, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 74, "start_state": {"Stack1": [12, 8, 15, 11], "Stack2": [1, 2, 10, 9, 6], "Stack3": [4], "Stack4": [14, 5, 3], "Stack5": [13, 7]}, "goal_state": {"Stack1": [], "Stack2": [9, 10, 4, 1, 13, 6, 7, 12, 3, 11, 2, 14, 15, 5, 8], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 75, "start_state": {"Stack1": [8, 14, 2, 15, 11, 6], "Stack2": [13, 4, 12, 3], "Stack3": [5, 7], "Stack4": [10], "Stack5": [1, 9]}, "goal_state": {"Stack1": [5, 10], "Stack2": [9, 3, 11, 4], "Stack3": [13, 8, 2], "Stack4": [12, 14, 1, 6, 15], "Stack5": [7]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 76, "start_state": {"Stack1": [13], "Stack2": [6, 2, 1], "Stack3": [8, 14], "Stack4": [3, 7, 5, 4], "Stack5": [9, 12, 11, 15, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [2, 7, 12, 14, 8, 11, 9, 15, 13, 4, 10, 6, 1, 3, 5], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 77, "start_state": {"Stack1": [3, 5], "Stack2": [14, 7, 13], "Stack3": [11, 15, 12], "Stack4": [4, 9], "Stack5": [10, 8, 2, 6, 1]}, "goal_state": {"Stack1": [7, 3, 11, 14, 1], "Stack2": [13, 8, 15], "Stack3": [12, 5, 4], "Stack4": [6], "Stack5": [2, 9, 10]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 78, "start_state": {"Stack1": [9, 7], "Stack2": [11, 14, 12, 6], "Stack3": [4, 1, 15], "Stack4": [10, 13], "Stack5": [2, 8, 3, 5]}, "goal_state": {"Stack1": [5, 7, 10], "Stack2": [1, 13, 14], "Stack3": [9, 11, 12], "Stack4": [4, 6, 15], "Stack5": [2, 3, 8]}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack2", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 79, "start_state": {"Stack1": [9, 11, 1, 13], "Stack2": [15, 8], "Stack3": [10, 2, 5, 6], "Stack4": [14, 12, 7], "Stack5": [3, 4]}, "goal_state": {"Stack1": [8, 13, 7], "Stack2": [1, 11, 15, 6, 2], "Stack3": [12, 3], "Stack4": [4, 5, 9], "Stack5": [14, 10]}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 80, "start_state": {"Stack1": [3, 12, 10, 1], "Stack2": [6, 13, 8], "Stack3": [15, 9, 2], "Stack4": [11, 7, 4, 5], "Stack5": [14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [5, 8, 13, 14, 1, 4, 15, 10, 12, 6, 3, 7, 9, 11, 2], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 81, "start_state": {"Stack1": [8, 6], "Stack2": [1, 3], "Stack3": [5], "Stack4": [15, 10, 7], "Stack5": [4, 9, 14, 2, 11, 12, 13]}, "goal_state": {"Stack1": [5, 12, 11], "Stack2": [6], "Stack3": [9, 10, 1, 13], "Stack4": [2, 4, 3], "Stack5": [7, 14, 15, 8]}, "fix_order": ["Stack5", "Stack2", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 82, "start_state": {"Stack1": [1, 7], "Stack2": [5, 11], "Stack3": [9], "Stack4": [6, 4, 12, 15, 3, 2], "Stack5": [8, 14, 10, 13]}, "goal_state": {"Stack1": [], "Stack2": [2, 9, 13, 10, 4, 7, 3, 14, 11, 15, 6, 5, 8, 12, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 83, "start_state": {"Stack1": [15, 13, 11, 7], "Stack2": [6, 8, 4, 2, 10], "Stack3": [5], "Stack4": [3, 12], "Stack5": [1, 9, 14]}, "goal_state": {"Stack1": [11], "Stack2": [3, 7, 6], "Stack3": [13, 4, 12, 15], "Stack4": [2, 10, 8], "Stack5": [9, 1, 14, 5]}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 84, "start_state": {"Stack1": [8], "Stack2": [7, 14, 9], "Stack3": [6, 13, 11, 10, 1], "Stack4": [2, 12, 4, 3], "Stack5": [15, 5]}, "goal_state": {"Stack1": [4, 14], "Stack2": [15, 5, 8], "Stack3": [12, 1, 2, 7, 10, 9, 11], "Stack4": [13, 6], "Stack5": [3]}, "fix_order": ["Stack3", "Stack4", "Stack1", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 85, "start_state": {"Stack1": [3], "Stack2": [10, 9, 8, 6], "Stack3": [2, 14, 4, 1, 11], "Stack4": [13, 15], "Stack5": [12, 7, 5]}, "goal_state": {"Stack1": [1, 5, 7], "Stack2": [8, 9, 14], "Stack3": [4, 10, 12], "Stack4": [2, 6, 15], "Stack5": [3, 11, 13]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 86, "start_state": {"Stack1": [7, 11], "Stack2": [3, 4, 14, 13, 10], "Stack3": [5, 12, 8], "Stack4": [6, 9, 2, 1], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 87, "start_state": {"Stack1": [7, 6, 2, 3, 14], "Stack2": [5, 10, 13, 4], "Stack3": [9, 8], "Stack4": [1, 11], "Stack5": [15, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 88, "start_state": {"Stack1": [15, 9, 1, 3], "Stack2": [13, 7, 14, 10, 4, 6, 5], "Stack3": [2], "Stack4": [11], "Stack5": [8, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [5, 3, 14, 4, 7, 6, 8, 11, 2, 12, 15, 1, 10, 9, 13], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 89, "start_state": {"Stack1": [15, 3, 10], "Stack2": [2, 5, 11], "Stack3": [8, 1, 12], "Stack4": [6, 4], "Stack5": [9, 14, 7, 13]}, "goal_state": {"Stack1": [], "Stack2": [14, 2, 3, 5, 7, 10, 11, 15, 12, 6, 13, 4, 8, 9, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 90, "start_state": {"Stack1": [1, 10, 3, 14], "Stack2": [8, 4], "Stack3": [13, 7, 6, 11, 5, 12], "Stack4": [9, 2], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack2", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 91, "start_state": {"Stack1": [12, 8], "Stack2": [1, 14], "Stack3": [2], "Stack4": [4, 10, 11, 15, 5, 7], "Stack5": [9, 13, 6, 3]}, "goal_state": {"Stack1": [3, 10, 15, 13, 8, 7, 14, 12, 4, 9, 1, 6, 11, 5, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 92, "start_state": {"Stack1": [12, 1], "Stack2": [6, 4, 11], "Stack3": [13, 5], "Stack4": [8, 15, 10, 9, 3], "Stack5": [2, 7, 14]}, "goal_state": {"Stack1": [3, 6], "Stack2": [10, 11, 15], "Stack3": [12, 13, 2, 14], "Stack4": [5, 1, 8], "Stack5": [9, 7, 4]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 93, "start_state": {"Stack1": [15, 14, 11], "Stack2": [9, 5, 1, 4, 2, 12], "Stack3": [3], "Stack4": [10, 7, 13, 8], "Stack5": [6]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack1", "Stack4"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 94, "start_state": {"Stack1": [5], "Stack2": [2, 4, 7, 13], "Stack3": [9], "Stack4": [1, 11, 14], "Stack5": [6, 12, 15, 3, 10, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [2, 1, 4, 11, 13, 15, 10, 14, 9, 7, 12, 3, 8, 5, 6], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 95, "start_state": {"Stack1": [4, 1, 8, 12, 13], "Stack2": [9, 3, 6], "Stack3": [2, 15, 11], "Stack4": [5, 14], "Stack5": [7, 10]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 96, "start_state": {"Stack1": [1, 3], "Stack2": [12, 9, 6], "Stack3": [15, 5, 10], "Stack4": [11, 4], "Stack5": [13, 7, 2, 8, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack3", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 97, "start_state": {"Stack1": [6, 8, 1], "Stack2": [11, 5], "Stack3": [4, 14, 2, 3], "Stack4": [13, 9], "Stack5": [10, 7, 12, 15]}, "goal_state": {"Stack1": [1, 5], "Stack2": [15, 2, 14, 12], "Stack3": [10, 7, 3, 4, 9, 6], "Stack4": [11], "Stack5": [8, 13]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 98, "start_state": {"Stack1": [15, 10, 2], "Stack2": [8], "Stack3": [7, 9, 4, 6], "Stack4": [1, 14], "Stack5": [5, 12, 3, 13, 11]}, "goal_state": {"Stack1": [15, 6, 12, 1, 14, 9, 13], "Stack2": [10, 7, 3], "Stack3": [5, 2], "Stack4": [11, 4], "Stack5": [8]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 99, "start_state": {"Stack1": [11, 3, 4, 12, 15], "Stack2": [], "Stack3": [14, 1, 6, 8], "Stack4": [13, 7, 5], "Stack5": [2, 9, 10]}, "goal_state": {"Stack1": [], "Stack2": [2, 14, 10, 1, 11, 6, 8, 13, 5, 15, 7, 12, 3, 4, 9], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 100, "start_state": {"Stack1": [8, 10, 4, 2, 9], "Stack2": [1, 13], "Stack3": [3, 5, 15], "Stack4": [11], "Stack5": [14, 12, 7, 6]}, "goal_state": {"Stack1": [11], "Stack2": [7, 15], "Stack3": [9, 14, 4, 10, 12], "Stack4": [13, 5], "Stack5": [1, 2, 3, 6, 8]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack2", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 101, "start_state": {"Stack1": [3, 6, 13, 4], "Stack2": [8, 7, 12], "Stack3": [1, 9, 5, 15], "Stack4": [10], "Stack5": [14, 2, 11]}, "goal_state": {"Stack1": [2, 14], "Stack2": [15, 4, 10], "Stack3": [6, 5, 13], "Stack4": [8, 12, 1], "Stack5": [3, 7, 11, 9]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack3", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 102, "start_state": {"Stack1": [7, 2, 3], "Stack2": [8, 6, 9, 15], "Stack3": [1, 14, 12, 13], "Stack4": [5, 4, 11], "Stack5": [10]}, "goal_state": {"Stack1": [12, 15, 14, 8, 7, 13, 4, 3, 10, 5, 9, 2, 6, 11, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 103, "start_state": {"Stack1": [4, 8], "Stack2": [14, 5, 10, 15, 3, 9], "Stack3": [12, 11], "Stack4": [1, 13, 6, 7], "Stack5": [2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 104, "start_state": {"Stack1": [5, 8], "Stack2": [2, 11, 13, 10], "Stack3": [6], "Stack4": [14, 1, 9, 3], "Stack5": [7, 4, 12, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack4", "Stack2", "Stack1", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 105, "start_state": {"Stack1": [3, 9, 2, 13], "Stack2": [5, 14, 8], "Stack3": [], "Stack4": [6, 10], "Stack5": [12, 1, 11, 7, 4, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 106, "start_state": {"Stack1": [15, 2, 8, 7], "Stack2": [11, 12], "Stack3": [14, 3], "Stack4": [10, 9], "Stack5": [5, 1, 6, 13, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 107, "start_state": {"Stack1": [13], "Stack2": [1, 9, 5, 11], "Stack3": [8, 4, 15, 6], "Stack4": [2, 7, 10], "Stack5": [3, 14, 12]}, "goal_state": {"Stack1": [8, 3, 1, 7, 4], "Stack2": [2, 14], "Stack3": [13, 10, 6], "Stack4": [5, 15], "Stack5": [11, 12, 9]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 108, "start_state": {"Stack1": [2, 8, 3], "Stack2": [15, 6, 14, 11], "Stack3": [13, 1, 12], "Stack4": [10], "Stack5": [7, 4, 9, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 109, "start_state": {"Stack1": [8, 3, 11, 2], "Stack2": [10], "Stack3": [4, 9, 13, 12], "Stack4": [14], "Stack5": [1, 7, 6, 5, 15]}, "goal_state": {"Stack1": [2, 3, 15], "Stack2": [1, 9, 11], "Stack3": [4, 6, 14], "Stack4": [7, 10, 13], "Stack5": [5, 8, 12]}, "fix_order": ["Stack5", "Stack4", "Stack2", "Stack1", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 110, "start_state": {"Stack1": [5], "Stack2": [15, 1, 7, 6, 12, 11], "Stack3": [13, 3, 10, 14], "Stack4": [2, 4], "Stack5": [8, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 111, "start_state": {"Stack1": [10], "Stack2": [5, 2, 6], "Stack3": [12, 7, 15], "Stack4": [9, 1, 11], "Stack5": [4, 13, 8, 3, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [13, 14, 11, 10, 4, 5, 12, 2, 8, 3, 7, 6, 15, 1, 9], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 112, "start_state": {"Stack1": [6], "Stack2": [2, 9, 5, 15], "Stack3": [13, 8, 4], "Stack4": [14, 1, 7, 11, 12], "Stack5": [3, 10]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 113, "start_state": {"Stack1": [9, 2, 7, 15], "Stack2": [4, 14], "Stack3": [10, 1, 13, 3], "Stack4": [5, 8, 12], "Stack5": [11, 6]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [4, 10, 14, 8, 12, 11, 3, 2, 7, 1, 6, 5, 13, 15, 9], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 114, "start_state": {"Stack1": [1, 4], "Stack2": [9, 11], "Stack3": [3, 14, 6, 15, 5, 2, 12], "Stack4": [7, 8], "Stack5": [10, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 115, "start_state": {"Stack1": [13, 10, 6, 3], "Stack2": [7, 5, 11], "Stack3": [8, 2, 4], "Stack4": [15, 14, 12], "Stack5": [9, 1]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 116, "start_state": {"Stack1": [9], "Stack2": [5], "Stack3": [10, 13, 8, 14, 11], "Stack4": [4, 2, 12, 6], "Stack5": [1, 3, 7, 15]}, "goal_state": {"Stack1": [11, 6], "Stack2": [10, 15, 14], "Stack3": [2, 12, 9], "Stack4": [7, 13, 1], "Stack5": [4, 8, 3, 5]}, "fix_order": ["Stack2", "Stack3", "Stack1", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 117, "start_state": {"Stack1": [9, 10], "Stack2": [13, 3, 8, 15], "Stack3": [6, 14, 12], "Stack4": [1, 2, 4, 11], "Stack5": [5, 7]}, "goal_state": {"Stack1": [7, 10], "Stack2": [11, 6, 5], "Stack3": [8, 2, 14, 1], "Stack4": [12, 13, 15, 9, 3], "Stack5": [4]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 118, "start_state": {"Stack1": [15, 11, 1], "Stack2": [14, 4, 8, 5], "Stack3": [7, 13], "Stack4": [12, 6, 10, 9, 2], "Stack5": [3]}, "goal_state": {"Stack1": [3, 10, 15], "Stack2": [7, 12, 14], "Stack3": [1, 4, 6], "Stack4": [8, 11, 13], "Stack5": [2, 5, 9]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 119, "start_state": {"Stack1": [12, 5, 3], "Stack2": [15, 8, 1], "Stack3": [6, 14, 4, 2], "Stack4": [11, 10], "Stack5": [13, 9, 7]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack3", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 120, "start_state": {"Stack1": [7, 1], "Stack2": [3, 2], "Stack3": [14, 15, 9, 12], "Stack4": [11, 8], "Stack5": [4, 6, 10, 13, 5]}, "goal_state": {"Stack1": [], "Stack2": [14, 7, 3, 8, 11, 5, 9, 2, 12, 4, 13, 6, 10, 1, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 121, "start_state": {"Stack1": [4, 14, 15], "Stack2": [3, 7], "Stack3": [2, 11], "Stack4": [5, 8, 1], "Stack5": [12, 10, 9, 13, 6]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 122, "start_state": {"Stack1": [4, 3, 5], "Stack2": [14, 10, 7, 9, 1, 13], "Stack3": [2], "Stack4": [12], "Stack5": [8, 15, 6, 11]}, "goal_state": {"Stack1": [3, 5, 13], "Stack2": [1, 4, 14], "Stack3": [6, 8, 15], "Stack4": [2, 10, 11], "Stack5": [7, 9, 12]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 123, "start_state": {"Stack1": [9, 3], "Stack2": [2, 4], "Stack3": [13, 12, 11], "Stack4": [14, 6, 8, 10], "Stack5": [1, 15, 5, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 124, "start_state": {"Stack1": [3, 6], "Stack2": [8, 14, 1], "Stack3": [2, 11, 7, 12, 15], "Stack4": [9, 4], "Stack5": [13, 10, 5]}, "goal_state": {"Stack1": [3, 5, 13, 14], "Stack2": [11, 2], "Stack3": [7, 9, 8], "Stack4": [15, 6, 1, 12], "Stack5": [4, 10]}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack1", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 125, "start_state": {"Stack1": [5, 7, 12], "Stack2": [13, 10], "Stack3": [11, 15, 4], "Stack4": [2, 14, 3, 9], "Stack5": [8, 1, 6]}, "goal_state": {"Stack1": [], "Stack2": [10, 14, 1, 2, 6, 12, 5, 11, 7, 15, 4, 9, 8, 3, 13], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 126, "start_state": {"Stack1": [5, 6, 13, 7], "Stack2": [12, 3, 10, 14, 11], "Stack3": [4], "Stack4": [15, 1], "Stack5": [9, 8, 2]}, "goal_state": {"Stack1": [7, 15], "Stack2": [11, 12, 5], "Stack3": [9, 13], "Stack4": [10, 6, 14], "Stack5": [3, 4, 8, 2, 1]}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 127, "start_state": {"Stack1": [2, 3, 15, 5], "Stack2": [9, 1], "Stack3": [11, 13, 14], "Stack4": [7, 6, 10], "Stack5": [8, 12, 4]}, "goal_state": {"Stack1": [7, 12, 9], "Stack2": [14, 4], "Stack3": [10, 13, 3, 5, 1, 15], "Stack4": [2, 8], "Stack5": [6, 11]}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack3", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 128, "start_state": {"Stack1": [], "Stack2": [5, 13, 8, 14, 2, 7], "Stack3": [12, 3], "Stack4": [10, 6], "Stack5": [1, 15, 4, 9, 11]}, "goal_state": {"Stack1": [4, 6, 11], "Stack2": [12, 9, 2, 3, 10], "Stack3": [5, 8, 14], "Stack4": [1, 13, 15], "Stack5": [7]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 129, "start_state": {"Stack1": [2, 1], "Stack2": [12, 15, 5, 9, 8, 4, 10], "Stack3": [11], "Stack4": [7, 3, 13, 6, 14], "Stack5": []}, "goal_state": {"Stack1": [5, 10, 3], "Stack2": [15], "Stack3": [14], "Stack4": [12, 7, 2], "Stack5": [1, 9, 4, 11, 6, 13, 8]}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 130, "start_state": {"Stack1": [8, 4, 13], "Stack2": [15, 7, 12], "Stack3": [10], "Stack4": [6, 11, 5], "Stack5": [2, 3, 14, 9, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [9, 7, 5, 2, 15, 12, 13, 11, 4, 10, 14, 3, 1, 8, 6], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 131, "start_state": {"Stack1": [13, 7, 6], "Stack2": [10, 8, 14, 12, 9, 2], "Stack3": [4, 5, 3], "Stack4": [1, 15], "Stack5": [11]}, "goal_state": {"Stack1": [11, 3, 7, 6, 12, 5, 2, 4, 13, 10, 1, 9, 14, 15, 8], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 132, "start_state": {"Stack1": [12, 5, 2], "Stack2": [13, 6, 9], "Stack3": [7, 14], "Stack4": [8, 11], "Stack5": [15, 3, 1, 4, 10]}, "goal_state": {"Stack1": [4, 11, 9, 3, 1, 14, 8, 10, 15, 7, 12, 6, 5, 13, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack2", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 133, "start_state": {"Stack1": [], "Stack2": [6, 12, 9], "Stack3": [11, 5], "Stack4": [2, 10, 14], "Stack5": [7, 8, 3, 13, 15, 1, 4]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 134, "start_state": {"Stack1": [8, 7, 10], "Stack2": [], "Stack3": [15, 5, 14, 6, 9, 4, 12], "Stack4": [2], "Stack5": [3, 1, 13, 11]}, "goal_state": {"Stack1": [9, 8], "Stack2": [5], "Stack3": [15, 11, 6, 3], "Stack4": [7, 1, 10], "Stack5": [13, 2, 12, 4, 14]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 135, "start_state": {"Stack1": [11, 8, 1], "Stack2": [9, 3, 13], "Stack3": [12, 10, 4, 2, 15], "Stack4": [14, 5, 6], "Stack5": [7]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 136, "start_state": {"Stack1": [5, 4, 12, 13, 9, 2], "Stack2": [7, 6], "Stack3": [1, 15], "Stack4": [10, 14], "Stack5": [11, 3, 8]}, "goal_state": {"Stack1": [10, 8, 14, 7, 6, 15, 3, 4, 12, 1, 5, 11, 13, 9, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 137, "start_state": {"Stack1": [15, 10, 2, 3], "Stack2": [7, 6, 1], "Stack3": [4, 14, 13], "Stack4": [9, 5], "Stack5": [12, 8, 11]}, "goal_state": {"Stack1": [9, 11, 6], "Stack2": [4, 2, 1, 7], "Stack3": [5, 3], "Stack4": [14, 10, 8, 12, 15], "Stack5": [13]}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack3", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 138, "start_state": {"Stack1": [1, 8, 11], "Stack2": [5, 4], "Stack3": [13, 2, 14, 12], "Stack4": [10], "Stack5": [15, 7, 9, 6, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [6, 14, 5, 2, 12, 1, 11, 8, 3, 9, 13, 15, 4, 7, 10], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 139, "start_state": {"Stack1": [9, 4, 12, 8, 11], "Stack2": [10, 2, 3], "Stack3": [15], "Stack4": [7, 5, 13], "Stack5": [6, 1, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 140, "start_state": {"Stack1": [8, 3, 12, 10, 6], "Stack2": [14, 11], "Stack3": [2, 9, 13, 5], "Stack4": [15], "Stack5": [1, 7, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 8, 5, 11, 6, 2, 10, 3, 4, 9, 15, 13, 14, 7, 12], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 141, "start_state": {"Stack1": [1], "Stack2": [10, 15, 14], "Stack3": [8], "Stack4": [5, 9, 13, 7, 11], "Stack5": [6, 4, 12, 2, 3]}, "goal_state": {"Stack1": [], "Stack2": [11, 2, 15, 3, 4, 13, 8, 1, 6, 10, 14, 7, 5, 9, 12], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 142, "start_state": {"Stack1": [12, 8], "Stack2": [9], "Stack3": [11, 4, 10, 14, 2], "Stack4": [3, 1, 6, 13], "Stack5": [5, 7, 15]}, "goal_state": {"Stack1": [2, 4, 7], "Stack2": [6, 9, 13], "Stack3": [3, 8, 11], "Stack4": [1, 10, 14], "Stack5": [5, 12, 15]}, "fix_order": ["Stack4", "Stack5", "Stack3", "Stack2", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 143, "start_state": {"Stack1": [3, 5], "Stack2": [4, 1, 7, 12, 13], "Stack3": [8, 11, 15], "Stack4": [6, 9], "Stack5": [2, 10, 14]}, "goal_state": {"Stack1": [10, 5, 2], "Stack2": [11, 3, 14], "Stack3": [6, 7], "Stack4": [1, 15, 4, 12, 13], "Stack5": [9, 8]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 144, "start_state": {"Stack1": [11], "Stack2": [1, 9], "Stack3": [15, 14, 12, 10, 6], "Stack4": [4, 8, 5], "Stack5": [2, 13, 7, 3]}, "goal_state": {"Stack1": [], "Stack2": [3, 15, 10, 4, 8, 5, 14, 7, 12, 2, 13, 11, 1, 9, 6], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 145, "start_state": {"Stack1": [6, 5, 2, 1], "Stack2": [7, 15], "Stack3": [3, 11, 9, 4, 14, 10], "Stack4": [8, 12, 13], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [14, 15, 11, 7, 4, 12, 9, 10, 2, 8, 3, 13, 5, 1, 6], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 146, "start_state": {"Stack1": [9, 3, 8], "Stack2": [6, 13], "Stack3": [7, 1], "Stack4": [15, 14, 12, 11, 10, 4], "Stack5": [5, 2]}, "goal_state": {"Stack1": [10, 5, 13, 14, 12, 11, 3, 6, 15, 2, 8, 7, 1, 4, 9], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack1", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 147, "start_state": {"Stack1": [8, 10, 1, 3, 13], "Stack2": [6, 12, 9, 11], "Stack3": [14], "Stack4": [7, 2, 5, 15], "Stack5": [4]}, "goal_state": {"Stack1": [2, 7, 15], "Stack2": [4, 12, 13], "Stack3": [3, 6, 10], "Stack4": [1, 11, 14], "Stack5": [5, 8, 9]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 148, "start_state": {"Stack1": [7, 14, 9, 3, 1, 2], "Stack2": [6, 11], "Stack3": [15], "Stack4": [5, 4], "Stack5": [13, 8, 10, 12]}, "goal_state": {"Stack1": [3, 5, 12, 6], "Stack2": [1, 15, 4, 2], "Stack3": [14, 11, 7], "Stack4": [8, 13], "Stack5": [10, 9]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 149, "start_state": {"Stack1": [11], "Stack2": [4, 13, 12, 1, 10], "Stack3": [6, 7], "Stack4": [5, 9, 2, 3], "Stack5": [8, 14, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack1", "Stack3", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 150, "start_state": {"Stack1": [2, 1, 11], "Stack2": [3, 12, 4, 7, 13, 14], "Stack3": [9], "Stack4": [15, 6, 5], "Stack5": [8, 10]}, "goal_state": {"Stack1": [12, 8, 14], "Stack2": [3, 13], "Stack3": [10, 6], "Stack4": [2, 15, 7, 11], "Stack5": [9, 1, 4, 5]}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 151, "start_state": {"Stack1": [], "Stack2": [3, 13, 5, 10, 12, 2, 6], "Stack3": [1, 7, 14, 4], "Stack4": [15, 8, 11], "Stack5": [9]}, "goal_state": {"Stack1": [15, 3], "Stack2": [13, 4, 2, 12], "Stack3": [7], "Stack4": [6, 5, 10], "Stack5": [11, 14, 9, 8, 1]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 152, "start_state": {"Stack1": [15, 3, 2, 4, 1], "Stack2": [12], "Stack3": [], "Stack4": [10, 9, 5, 6, 11, 14, 7], "Stack5": [8, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 153, "start_state": {"Stack1": [10, 7, 8], "Stack2": [15, 13], "Stack3": [12, 3, 1, 11], "Stack4": [9, 6, 14, 2], "Stack5": [5, 4]}, "goal_state": {"Stack1": [7, 12, 14], "Stack2": [3, 8, 9], "Stack3": [1, 5, 6], "Stack4": [2, 10, 11], "Stack5": [4, 13, 15]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 154, "start_state": {"Stack1": [2, 9], "Stack2": [5, 14], "Stack3": [1, 7, 13, 12, 11, 3], "Stack4": [4, 15, 6], "Stack5": [8, 10]}, "goal_state": {"Stack1": [4, 6, 7], "Stack2": [1, 3, 15], "Stack3": [5, 10, 13], "Stack4": [9, 11, 12], "Stack5": [2, 8, 14]}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 155, "start_state": {"Stack1": [7, 13, 14], "Stack2": [9, 8, 5], "Stack3": [1, 10, 15], "Stack4": [6, 12, 11, 2], "Stack5": [4, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 156, "start_state": {"Stack1": [5, 13], "Stack2": [6, 2, 11, 8, 4, 12, 14], "Stack3": [7, 3], "Stack4": [15], "Stack5": [9, 1, 10]}, "goal_state": {"Stack1": [15, 13, 1], "Stack2": [10, 3, 8], "Stack3": [14, 12, 5], "Stack4": [11, 9, 4, 6], "Stack5": [7, 2]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 157, "start_state": {"Stack1": [12, 6, 3], "Stack2": [4, 10, 8], "Stack3": [2, 9, 15], "Stack4": [14, 1], "Stack5": [7, 11, 13, 5]}, "goal_state": {"Stack1": [], "Stack2": [6, 12, 5, 15, 3, 4, 10, 7, 13, 14, 11, 8, 9, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 158, "start_state": {"Stack1": [13], "Stack2": [2, 4, 5, 1], "Stack3": [3, 15], "Stack4": [9, 14, 12, 8, 7], "Stack5": [11, 10, 6]}, "goal_state": {"Stack1": [6, 14, 3, 9, 8, 11, 4, 5, 12, 1, 2, 7, 13, 10, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 159, "start_state": {"Stack1": [3, 15, 14, 5], "Stack2": [6, 2], "Stack3": [4, 12, 8], "Stack4": [11, 7, 13, 10], "Stack5": [1, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [3, 4, 9, 1, 15, 10, 13, 6, 2, 8, 12, 11, 5, 14, 7]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 160, "start_state": {"Stack1": [9, 15, 8], "Stack2": [10, 11, 14], "Stack3": [1, 13, 6, 3, 12], "Stack4": [], "Stack5": [7, 2, 4, 5]}, "goal_state": {"Stack1": [2, 10, 12], "Stack2": [4, 8, 11], "Stack3": [5, 7, 15], "Stack4": [1, 9, 13], "Stack5": [3, 6, 14]}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 161, "start_state": {"Stack1": [9, 13], "Stack2": [1, 12, 4, 14], "Stack3": [3, 6, 5, 11, 10], "Stack4": [8, 7], "Stack5": [15, 2]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack2", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 162, "start_state": {"Stack1": [4, 5, 9, 10, 14, 8], "Stack2": [13, 12, 7], "Stack3": [3, 11, 2], "Stack4": [6], "Stack5": [1, 15]}, "goal_state": {"Stack1": [4, 13, 14, 2], "Stack2": [1, 6, 10, 12], "Stack3": [8, 5], "Stack4": [15], "Stack5": [9, 7, 11, 3]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 163, "start_state": {"Stack1": [1, 5, 8, 3, 6], "Stack2": [7], "Stack3": [9, 15, 14, 10], "Stack4": [11, 4, 12], "Stack5": [13, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [6, 14, 13, 1, 2, 9, 3, 7, 4, 15, 11, 5, 12, 10, 8], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 164, "start_state": {"Stack1": [15, 3], "Stack2": [12, 5, 10], "Stack3": [6, 1, 8, 11], "Stack4": [4, 14, 9], "Stack5": [13, 7, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 165, "start_state": {"Stack1": [10, 4, 2, 8], "Stack2": [1, 9], "Stack3": [11, 14], "Stack4": [15, 7, 3], "Stack5": [6, 5, 12, 13]}, "goal_state": {"Stack1": [], "Stack2": [3, 2, 4, 1, 12, 10, 7, 6, 14, 15, 9, 13, 8, 11, 5], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 166, "start_state": {"Stack1": [10, 7], "Stack2": [13], "Stack3": [], "Stack4": [9, 15, 4, 3, 11, 14], "Stack5": [12, 2, 6, 5, 1, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [9, 3, 4, 2, 14, 13, 8, 1, 12, 5, 6, 11, 15, 7, 10], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 167, "start_state": {"Stack1": [4, 3, 10, 1, 12], "Stack2": [9], "Stack3": [5, 14, 13], "Stack4": [6, 15], "Stack5": [8, 11, 2, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [2, 13, 14, 4, 11, 5, 3, 9, 7, 6, 15, 10, 8, 12, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 168, "start_state": {"Stack1": [13, 2, 6], "Stack2": [15], "Stack3": [14, 4, 7], "Stack4": [12, 3], "Stack5": [1, 9, 11, 8, 5, 10]}, "goal_state": {"Stack1": [], "Stack2": [1, 8, 4, 14, 3, 10, 15, 7, 12, 9, 5, 11, 2, 13, 6], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 169, "start_state": {"Stack1": [2, 1], "Stack2": [10, 6, 4, 8, 15], "Stack3": [13, 3, 11], "Stack4": [7, 9, 12], "Stack5": [14, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [9, 12, 6, 13, 8, 1, 2, 7, 4, 10, 15, 3, 11, 5, 14], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 170, "start_state": {"Stack1": [8, 6, 2, 13, 3, 7], "Stack2": [1, 11], "Stack3": [10, 9, 5], "Stack4": [12, 14, 4], "Stack5": [15]}, "goal_state": {"Stack1": [3, 4, 6], "Stack2": [7, 12, 14], "Stack3": [1, 5, 8], "Stack4": [10, 11, 13], "Stack5": [2, 9, 15]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 171, "start_state": {"Stack1": [6, 4, 15], "Stack2": [14, 5], "Stack3": [8, 9, 13, 2, 3], "Stack4": [1, 10, 12], "Stack5": [11, 7]}, "goal_state": {"Stack1": [2, 10, 15], "Stack2": [6, 8, 14], "Stack3": [3, 7, 9], "Stack4": [4, 5, 11], "Stack5": [1, 12, 13]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 172, "start_state": {"Stack1": [10, 9, 4], "Stack2": [3], "Stack3": [8, 11, 2], "Stack4": [12, 1, 7, 6, 14], "Stack5": [13, 5, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 173, "start_state": {"Stack1": [], "Stack2": [2, 14, 7, 3, 11], "Stack3": [12, 1, 13, 8], "Stack4": [9, 6, 4], "Stack5": [5, 10, 15]}, "goal_state": {"Stack1": [13, 4], "Stack2": [9, 5, 1, 7], "Stack3": [11, 8, 6], "Stack4": [2, 10, 15], "Stack5": [14, 12, 3]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 174, "start_state": {"Stack1": [9], "Stack2": [10, 3, 13, 4, 11, 7], "Stack3": [6, 12, 1], "Stack4": [15, 5, 2, 8], "Stack5": [14]}, "goal_state": {"Stack1": [12, 14, 15, 4, 8], "Stack2": [11], "Stack3": [5, 2, 10], "Stack4": [7, 9, 13], "Stack5": [3, 1, 6]}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 175, "start_state": {"Stack1": [12, 10, 5], "Stack2": [15, 14, 1], "Stack3": [4, 2, 7, 9], "Stack4": [8, 13, 11], "Stack5": [3, 6]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [9, 8, 1, 13, 3, 2, 4, 14, 11, 10, 15, 5, 7, 6, 12], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 176, "start_state": {"Stack1": [2, 9, 13], "Stack2": [6, 7, 14, 5], "Stack3": [15, 12, 1], "Stack4": [11, 10], "Stack5": [8, 3, 4]}, "goal_state": {"Stack1": [6, 8, 7, 9, 1], "Stack2": [13, 15], "Stack3": [5, 4, 3], "Stack4": [11, 12, 10], "Stack5": [14, 2]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 177, "start_state": {"Stack1": [14, 2, 12, 11, 10], "Stack2": [13, 6], "Stack3": [4, 3, 15], "Stack4": [9, 8, 1], "Stack5": [5, 7]}, "goal_state": {"Stack1": [8], "Stack2": [1, 11], "Stack3": [6, 12, 13], "Stack4": [9, 2, 15, 5, 14], "Stack5": [10, 4, 7, 3]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 178, "start_state": {"Stack1": [5, 10], "Stack2": [4, 6, 2], "Stack3": [14, 8, 1, 7], "Stack4": [9, 12, 15, 11], "Stack5": [13, 3]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 179, "start_state": {"Stack1": [9, 15], "Stack2": [3, 14, 7], "Stack3": [13, 12, 8, 2], "Stack4": [4, 1, 5, 10], "Stack5": [6, 11]}, "goal_state": {"Stack1": [15], "Stack2": [13], "Stack3": [6, 14, 12, 3], "Stack4": [11, 4, 8, 7, 2], "Stack5": [9, 1, 5, 10]}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 180, "start_state": {"Stack1": [5, 6, 8, 2], "Stack2": [9, 11, 13], "Stack3": [10], "Stack4": [3, 7, 14, 15, 12, 1, 4], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 181, "start_state": {"Stack1": [14, 9, 8, 11], "Stack2": [10], "Stack3": [15, 13], "Stack4": [6, 7, 12, 2], "Stack5": [3, 4, 5, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 182, "start_state": {"Stack1": [9], "Stack2": [3, 5, 13, 6, 8, 4], "Stack3": [1, 10], "Stack4": [7, 2, 11, 14], "Stack5": [15, 12]}, "goal_state": {"Stack1": [2, 14, 10, 12, 6, 3, 15, 1, 9, 11, 4, 5, 8, 7, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 183, "start_state": {"Stack1": [1, 6, 8, 12, 4, 2], "Stack2": [13], "Stack3": [], "Stack4": [15, 3], "Stack5": [11, 5, 9, 14, 10, 7]}, "goal_state": {"Stack1": [10, 8, 4], "Stack2": [13, 7, 5, 2], "Stack3": [14, 9, 3], "Stack4": [1, 15, 6, 11], "Stack5": [12]}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack2", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 184, "start_state": {"Stack1": [10, 8, 3, 4], "Stack2": [2, 12], "Stack3": [5, 15, 7], "Stack4": [1, 11, 13, 6], "Stack5": [9, 14]}, "goal_state": {"Stack1": [9, 4], "Stack2": [2, 12, 13], "Stack3": [6, 15, 5], "Stack4": [10, 11, 1], "Stack5": [8, 14, 3, 7]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 10, "num_stacks": 5, "num_blocks": 15}, {"id": 185, "start_state": {"Stack1": [3, 11, 14], "Stack2": [10, 7], "Stack3": [6, 2, 4], "Stack4": [], "Stack5": [8, 12, 9, 1, 5, 13, 15]}, "goal_state": {"Stack1": [], "Stack2": [10, 4, 8, 15, 6, 3, 11, 1, 2, 9, 7, 12, 5, 13, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 186, "start_state": {"Stack1": [5, 11, 6, 8], "Stack2": [15, 3, 10], "Stack3": [2, 13], "Stack4": [9, 12, 4], "Stack5": [1, 14, 7]}, "goal_state": {"Stack1": [1], "Stack2": [14, 2, 13, 8], "Stack3": [10], "Stack4": [12, 11, 5, 15, 6, 3, 7], "Stack5": [9, 4]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 187, "start_state": {"Stack1": [10, 7, 3], "Stack2": [14, 12, 1], "Stack3": [13, 4, 5, 2, 6], "Stack4": [9, 8], "Stack5": [11, 15]}, "goal_state": {"Stack1": [12], "Stack2": [6, 5, 15], "Stack3": [10, 13, 8], "Stack4": [7, 1, 3, 4], "Stack5": [9, 11, 2, 14]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 188, "start_state": {"Stack1": [8, 1, 9, 10, 3], "Stack2": [5, 2], "Stack3": [13, 4], "Stack4": [], "Stack5": [15, 11, 7, 6, 12, 14]}, "goal_state": {"Stack1": [6, 7, 15], "Stack2": [5, 10, 14], "Stack3": [3, 11, 12], "Stack4": [1, 2, 9], "Stack5": [4, 8, 13]}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 189, "start_state": {"Stack1": [12, 15, 11, 14], "Stack2": [4, 5, 9, 3, 1], "Stack3": [10, 13, 7], "Stack4": [6], "Stack5": [2, 8]}, "goal_state": {"Stack1": [], "Stack2": [10, 14, 13, 11, 9, 8, 4, 3, 6, 2, 7, 5, 15, 12, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 190, "start_state": {"Stack1": [11, 6, 1], "Stack2": [15, 5, 7], "Stack3": [12, 14], "Stack4": [10, 9, 8], "Stack5": [4, 3, 2, 13]}, "goal_state": {"Stack1": [1, 2, 7, 4, 9], "Stack2": [10, 3], "Stack3": [8, 6, 14], "Stack4": [12, 15, 11], "Stack5": [5, 13]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 191, "start_state": {"Stack1": [12], "Stack2": [7, 2, 6], "Stack3": [13, 9, 10, 4, 14], "Stack4": [3, 15, 11], "Stack5": [8, 1, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 192, "start_state": {"Stack1": [13, 4, 6, 14, 1], "Stack2": [12], "Stack3": [8, 11], "Stack4": [7, 9, 10, 3], "Stack5": [5, 2, 15]}, "goal_state": {"Stack1": [12, 13, 8], "Stack2": [10, 7, 14, 6], "Stack3": [9, 11, 1], "Stack4": [2, 5, 15], "Stack5": [3, 4]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 193, "start_state": {"Stack1": [6, 4, 3], "Stack2": [15], "Stack3": [7, 5, 8], "Stack4": [12, 14, 9, 10], "Stack5": [11, 1, 13, 2]}, "goal_state": {"Stack1": [15, 7, 2, 5, 12, 3, 10, 4, 1, 14, 6, 13, 8, 9, 11], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 194, "start_state": {"Stack1": [6, 5, 2, 13], "Stack2": [11, 4], "Stack3": [14, 9, 1, 10], "Stack4": [8, 12, 7], "Stack5": [3, 15]}, "goal_state": {"Stack1": [3, 1, 11, 10, 13, 2], "Stack2": [5, 7], "Stack3": [9, 15], "Stack4": [4, 12, 14], "Stack5": [6, 8]}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack1", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 195, "start_state": {"Stack1": [12, 11, 8, 13], "Stack2": [6, 15, 2], "Stack3": [14], "Stack4": [3], "Stack5": [5, 9, 1, 4, 10, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [5, 11, 9, 2, 6, 1, 15, 13, 4, 3, 14, 10, 12, 8, 7], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 196, "start_state": {"Stack1": [7], "Stack2": [4, 14, 2, 1], "Stack3": [15, 12, 10, 3], "Stack4": [9, 11, 13], "Stack5": [6, 5, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack2", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 197, "start_state": {"Stack1": [2, 12, 9, 14, 8], "Stack2": [3, 6, 1], "Stack3": [5, 11, 13], "Stack4": [10, 7, 15], "Stack5": [4]}, "goal_state": {"Stack1": [5, 1, 10, 11], "Stack2": [14, 9, 4, 15, 6], "Stack3": [8, 2, 13], "Stack4": [7], "Stack5": [12, 3]}, "fix_order": ["Stack5", "Stack1", "Stack3", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 198, "start_state": {"Stack1": [14], "Stack2": [2, 12, 9, 15, 4], "Stack3": [7, 13], "Stack4": [11, 10], "Stack5": [1, 3, 8, 5, 6]}, "goal_state": {"Stack1": [], "Stack2": [7, 12, 10, 14, 13, 15, 2, 8, 9, 3, 5, 11, 1, 4, 6], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 199, "start_state": {"Stack1": [2], "Stack2": [5, 12, 7, 10, 14, 9], "Stack3": [11, 8], "Stack4": [4, 13, 6, 3], "Stack5": [1, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [10, 7, 14, 4, 1, 11, 12, 6, 9, 8, 5, 15, 13, 3, 2]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 200, "start_state": {"Stack1": [12, 2, 5, 14, 8, 6], "Stack2": [15], "Stack3": [10, 11, 1, 13], "Stack4": [], "Stack5": [3, 9, 4, 7]}, "goal_state": {"Stack1": [8, 5], "Stack2": [7, 4], "Stack3": [1, 9, 12], "Stack4": [3, 14, 10, 15, 6], "Stack5": [2, 13, 11]}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 201, "start_state": {"Stack1": [3, 13], "Stack2": [11, 7, 12, 8], "Stack3": [1, 15, 9, 4], "Stack4": [5, 14, 10], "Stack5": [2, 6]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 3, 4, 14, 2, 8, 10, 1, 6, 5, 9, 12, 7, 13, 11], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 202, "start_state": {"Stack1": [3, 10, 2, 4], "Stack2": [13, 15, 1, 5], "Stack3": [11, 12, 7], "Stack4": [14, 8], "Stack5": [6, 9]}, "goal_state": {"Stack1": [10], "Stack2": [9, 14, 2, 3], "Stack3": [5, 4, 7], "Stack4": [8, 12, 11], "Stack5": [1, 15, 13, 6]}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 203, "start_state": {"Stack1": [12], "Stack2": [6, 10, 7], "Stack3": [15, 11, 13], "Stack4": [14, 9, 4, 8, 3, 1], "Stack5": [5, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 204, "start_state": {"Stack1": [1, 10, 5, 14, 4], "Stack2": [2, 9], "Stack3": [13, 8, 12], "Stack4": [7, 6, 3], "Stack5": [15, 11]}, "goal_state": {"Stack1": [2, 5, 9], "Stack2": [3, 13, 15], "Stack3": [1, 4, 10], "Stack4": [6, 7, 14], "Stack5": [8, 11, 12]}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 205, "start_state": {"Stack1": [1, 7, 10, 2], "Stack2": [12, 13, 8, 9, 15], "Stack3": [6, 3, 11], "Stack4": [14, 4], "Stack5": [5]}, "goal_state": {"Stack1": [5, 10, 14], "Stack2": [3, 13, 15], "Stack3": [2, 6, 7], "Stack4": [1, 9, 11], "Stack5": [4, 8, 12]}, "fix_order": ["Stack2", "Stack3", "Stack5", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 206, "start_state": {"Stack1": [10, 8, 6], "Stack2": [13, 11, 1], "Stack3": [12, 3], "Stack4": [5, 14, 4], "Stack5": [15, 9, 7, 2]}, "goal_state": {"Stack1": [5, 7, 1], "Stack2": [11, 3], "Stack3": [9, 15, 12, 6], "Stack4": [8, 13, 4], "Stack5": [14, 10, 2]}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 207, "start_state": {"Stack1": [5, 3, 12, 6], "Stack2": [9, 7], "Stack3": [10], "Stack4": [4, 8], "Stack5": [2, 1, 13, 15, 14, 11]}, "goal_state": {"Stack1": [5, 8, 12], "Stack2": [6, 15, 3, 4], "Stack3": [9, 10, 13, 7], "Stack4": [11, 2], "Stack5": [14, 1]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 208, "start_state": {"Stack1": [2, 14, 9, 11, 1], "Stack2": [3, 15, 6, 12, 4, 13], "Stack3": [8], "Stack4": [7, 5], "Stack5": [10]}, "goal_state": {"Stack1": [1, 11, 12], "Stack2": [5, 6, 10], "Stack3": [3, 7, 8], "Stack4": [2, 9, 15], "Stack5": [4, 13, 14]}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 209, "start_state": {"Stack1": [7, 2], "Stack2": [15, 11, 10], "Stack3": [14], "Stack4": [13, 9, 12, 6], "Stack5": [1, 3, 5, 4, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [12, 11, 1, 6, 4, 10, 3, 2, 13, 9, 7, 14, 15, 5, 8], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 210, "start_state": {"Stack1": [6], "Stack2": [10, 4, 7, 8], "Stack3": [9, 11, 12], "Stack4": [2, 1, 15, 13, 14], "Stack5": [3, 5]}, "goal_state": {"Stack1": [7, 13, 10, 9, 2, 4, 8, 5, 3, 1, 12, 11, 6, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 211, "start_state": {"Stack1": [5, 8, 10], "Stack2": [3, 4, 12], "Stack3": [14, 11, 15, 2], "Stack4": [6, 9, 7], "Stack5": [1, 13]}, "goal_state": {"Stack1": [3, 7], "Stack2": [1, 4, 6, 11, 15, 2], "Stack3": [10], "Stack4": [8, 5, 12], "Stack5": [14, 9, 13]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 212, "start_state": {"Stack1": [6, 12, 3, 2, 8, 5, 15, 13], "Stack2": [11], "Stack3": [9, 7], "Stack4": [10, 1], "Stack5": [14, 4]}, "goal_state": {"Stack1": [14, 1], "Stack2": [6, 8], "Stack3": [2, 3, 4, 5, 13, 9], "Stack4": [12, 15, 10], "Stack5": [11, 7]}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 213, "start_state": {"Stack1": [4, 11, 5], "Stack2": [8, 10], "Stack3": [13, 6, 9], "Stack4": [7, 2, 3, 14], "Stack5": [15, 1, 12]}, "goal_state": {"Stack1": [3, 12, 11], "Stack2": [10, 14, 6], "Stack3": [13, 15], "Stack4": [1, 5, 2, 8], "Stack5": [9, 4, 7]}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 214, "start_state": {"Stack1": [4, 14], "Stack2": [9, 10, 2], "Stack3": [11, 6], "Stack4": [8, 3, 1, 7], "Stack5": [13, 15, 5, 12]}, "goal_state": {"Stack1": [8, 4, 11], "Stack2": [10, 15], "Stack3": [7, 6], "Stack4": [3, 1, 2, 5], "Stack5": [13, 9, 12, 14]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack5", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 215, "start_state": {"Stack1": [5, 13], "Stack2": [6, 14], "Stack3": [3, 7, 2, 10], "Stack4": [15, 8, 9], "Stack5": [12, 1, 4, 11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 216, "start_state": {"Stack1": [6, 14], "Stack2": [13, 3], "Stack3": [5, 2, 15, 1], "Stack4": [7, 10, 9, 11, 12], "Stack5": [8, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [2, 11, 15, 10, 7, 6, 4, 9, 3, 8, 5, 12, 1, 13, 14]}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 217, "start_state": {"Stack1": [13, 8, 11, 2], "Stack2": [4, 9, 3, 10], "Stack3": [1], "Stack4": [15, 7, 12, 6], "Stack5": [5, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [10, 5, 1, 9, 4, 6, 15, 13, 8, 2, 14, 7, 12, 11, 3]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 218, "start_state": {"Stack1": [14, 6, 1], "Stack2": [2, 8], "Stack3": [3, 7], "Stack4": [13, 10, 4], "Stack5": [12, 5, 9, 15, 11]}, "goal_state": {"Stack1": [12, 6, 3, 1, 7, 5, 8, 13, 4, 2, 11, 15, 10, 14, 9], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 219, "start_state": {"Stack1": [6, 15], "Stack2": [13, 9], "Stack3": [12, 8, 11, 10], "Stack4": [14, 5, 3], "Stack5": [7, 1, 2, 4]}, "goal_state": {"Stack1": [5, 8, 15], "Stack2": [3, 6, 7], "Stack3": [10, 13, 14], "Stack4": [1, 2, 11], "Stack5": [4, 9, 12]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 220, "start_state": {"Stack1": [4], "Stack2": [8, 11, 6, 2, 10], "Stack3": [15, 13, 12], "Stack4": [3, 7], "Stack5": [1, 14, 5, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [3, 7, 8, 15, 13, 11, 6, 14, 9, 12, 5, 2, 4, 10, 1], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 221, "start_state": {"Stack1": [3, 1, 5], "Stack2": [4], "Stack3": [6, 8, 7, 13], "Stack4": [9, 14], "Stack5": [12, 10, 11, 15, 2]}, "goal_state": {"Stack1": [15, 9], "Stack2": [4, 2, 5, 12], "Stack3": [10, 14, 11], "Stack4": [1, 8, 6], "Stack5": [3, 7, 13]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 222, "start_state": {"Stack1": [11, 14, 2, 1, 4, 15, 9], "Stack2": [10, 6], "Stack3": [7], "Stack4": [5, 8], "Stack5": [3, 12, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 223, "start_state": {"Stack1": [5, 9], "Stack2": [8, 3, 13, 15, 4], "Stack3": [11, 6, 10], "Stack4": [7, 1, 12], "Stack5": [2, 14]}, "goal_state": {"Stack1": [13, 10, 4, 6], "Stack2": [12, 7], "Stack3": [15, 3, 11], "Stack4": [1, 5, 9], "Stack5": [14, 8, 2]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 224, "start_state": {"Stack1": [11, 14], "Stack2": [9, 8, 2], "Stack3": [4, 7, 3, 15], "Stack4": [5, 10, 13, 12, 1], "Stack5": [6]}, "goal_state": {"Stack1": [9, 5, 4, 13, 2, 10, 14, 1, 7, 6, 12, 3, 15, 11, 8], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 225, "start_state": {"Stack1": [10, 13, 5, 1, 12], "Stack2": [14, 8, 4, 6], "Stack3": [2, 11], "Stack4": [7, 9], "Stack5": [15, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 226, "start_state": {"Stack1": [6, 2, 5], "Stack2": [14, 12, 9, 4, 7, 8], "Stack3": [], "Stack4": [11, 10, 15], "Stack5": [1, 13, 3]}, "goal_state": {"Stack1": [], "Stack2": [9, 8, 6, 14, 5, 13, 11, 12, 2, 4, 10, 1, 7, 3, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack5", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 227, "start_state": {"Stack1": [8, 12, 4, 6, 11], "Stack2": [14, 3, 9], "Stack3": [13, 10, 5, 1], "Stack4": [2, 7], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [4, 12, 10, 14, 15, 7, 8, 3, 9, 1, 11, 13, 2, 5, 6], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 228, "start_state": {"Stack1": [8], "Stack2": [15, 13, 6, 9], "Stack3": [7, 2, 12, 11], "Stack4": [14, 4, 3], "Stack5": [5, 1, 10]}, "goal_state": {"Stack1": [4, 6, 12], "Stack2": [3, 5, 11], "Stack3": [1, 10, 13], "Stack4": [8, 9, 14], "Stack5": [2, 7, 15]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 229, "start_state": {"Stack1": [4, 11], "Stack2": [6, 5, 15, 10], "Stack3": [3], "Stack4": [1, 7], "Stack5": [13, 2, 9, 8, 14, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [8, 6, 15, 7, 11, 1, 10, 3, 2, 9, 13, 12, 5, 4, 14], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 230, "start_state": {"Stack1": [2, 9, 14], "Stack2": [8, 4, 1], "Stack3": [7, 6, 15, 12, 11, 13], "Stack4": [5], "Stack5": [10, 3]}, "goal_state": {"Stack1": [1, 6, 9], "Stack2": [3, 8, 11], "Stack3": [4, 5, 12], "Stack4": [2, 7, 10], "Stack5": [13, 14, 15]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 231, "start_state": {"Stack1": [13, 7, 12], "Stack2": [1, 15, 2, 6, 14], "Stack3": [11, 9], "Stack4": [10, 8, 4], "Stack5": [5, 3]}, "goal_state": {"Stack1": [15, 12, 2, 7, 11], "Stack2": [10, 13, 4, 3], "Stack3": [6, 1], "Stack4": [5], "Stack5": [14, 9, 8]}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 232, "start_state": {"Stack1": [9, 14, 4, 7, 5, 15], "Stack2": [6, 3, 10], "Stack3": [1], "Stack4": [12, 13, 8], "Stack5": [11, 2]}, "goal_state": {"Stack1": [12, 1, 2, 14, 4], "Stack2": [3, 11, 6], "Stack3": [13, 8, 7], "Stack4": [15], "Stack5": [10, 9, 5]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 233, "start_state": {"Stack1": [9, 10, 5, 11], "Stack2": [14, 2, 4, 7], "Stack3": [8, 3], "Stack4": [13, 1, 6, 15], "Stack5": [12]}, "goal_state": {"Stack1": [], "Stack2": [2, 9, 1, 15, 5, 4, 11, 10, 14, 8, 12, 6, 7, 13, 3], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 234, "start_state": {"Stack1": [3, 10, 4, 12], "Stack2": [11, 13, 7, 15], "Stack3": [1, 6, 14, 8, 9], "Stack4": [2], "Stack5": [5]}, "goal_state": {"Stack1": [], "Stack2": [3, 5, 13, 12, 8, 2, 6, 1, 10, 11, 4, 7, 9, 15, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 235, "start_state": {"Stack1": [2, 11], "Stack2": [5, 6, 13], "Stack3": [15, 14, 10], "Stack4": [7, 9, 1, 3, 8], "Stack5": [4, 12]}, "goal_state": {"Stack1": [8, 9], "Stack2": [2, 7, 13, 15, 11], "Stack3": [5, 14], "Stack4": [12, 4, 10, 1], "Stack5": [6, 3]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 236, "start_state": {"Stack1": [4, 6, 15, 10], "Stack2": [1, 9, 14], "Stack3": [11, 5, 13, 2, 12], "Stack4": [7], "Stack5": [8, 3]}, "goal_state": {"Stack1": [11, 13, 9, 5, 10], "Stack2": [8], "Stack3": [7, 14, 1, 4, 2], "Stack4": [3, 6], "Stack5": [15, 12]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 237, "start_state": {"Stack1": [8, 7, 14], "Stack2": [1, 5, 2, 11, 15, 3], "Stack3": [10], "Stack4": [13, 9], "Stack5": [4, 6, 12]}, "goal_state": {"Stack1": [2, 7, 14], "Stack2": [1, 4, 15], "Stack3": [5, 10, 12], "Stack4": [3, 6, 8], "Stack5": [9, 11, 13]}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 238, "start_state": {"Stack1": [6, 15, 1], "Stack2": [7, 13, 2], "Stack3": [9, 4, 12, 5, 11, 3], "Stack4": [8, 10], "Stack5": [14]}, "goal_state": {"Stack1": [], "Stack2": [12, 3, 11, 4, 15, 14, 13, 9, 7, 5, 6, 1, 8, 10, 2], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 239, "start_state": {"Stack1": [15, 12, 9], "Stack2": [14, 13], "Stack3": [10], "Stack4": [3, 2, 6, 4, 1, 11, 8], "Stack5": [7, 5]}, "goal_state": {"Stack1": [5, 15, 11], "Stack2": [13, 14, 2, 12], "Stack3": [9, 1, 3], "Stack4": [10, 6, 7], "Stack5": [8, 4]}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 240, "start_state": {"Stack1": [5, 2, 9, 13, 1], "Stack2": [14, 8, 6], "Stack3": [3, 12, 7], "Stack4": [11, 10, 15], "Stack5": [4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 15, 10, 12, 2, 11, 14, 9, 8, 7, 6, 4, 3, 5, 13], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 241, "start_state": {"Stack1": [5, 8, 3], "Stack2": [9, 1, 15, 11], "Stack3": [14], "Stack4": [10, 4, 13], "Stack5": [7, 2, 12, 6]}, "goal_state": {"Stack1": [], "Stack2": [13, 2, 5, 7, 8, 3, 12, 9, 15, 1, 10, 11, 14, 4, 6], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 242, "start_state": {"Stack1": [3, 2, 15, 13, 7, 6], "Stack2": [], "Stack3": [11, 12], "Stack4": [1, 5, 8, 14], "Stack5": [10, 4, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [10, 3, 6, 12, 13, 4, 7, 5, 1, 14, 8, 11, 2, 9, 15]}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 243, "start_state": {"Stack1": [3, 2, 10, 15], "Stack2": [14, 1, 6], "Stack3": [9, 12, 8, 13], "Stack4": [4, 5], "Stack5": [11, 7]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 244, "start_state": {"Stack1": [6, 2, 10, 13, 1], "Stack2": [15, 11, 7], "Stack3": [4, 3], "Stack4": [8, 14], "Stack5": [9, 5, 12]}, "goal_state": {"Stack1": [7, 11], "Stack2": [12, 4, 5, 2, 14, 3], "Stack3": [6, 9, 13], "Stack4": [8, 10], "Stack5": [15, 1]}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 245, "start_state": {"Stack1": [6, 15, 12, 8], "Stack2": [1, 13], "Stack3": [5, 11], "Stack4": [4, 7, 3, 9, 14, 2, 10], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 246, "start_state": {"Stack1": [3, 13, 5, 14, 10], "Stack2": [12, 6, 2, 15], "Stack3": [1, 11], "Stack4": [], "Stack5": [7, 8, 4, 9]}, "goal_state": {"Stack1": [6, 4], "Stack2": [15, 10, 3, 13], "Stack3": [7, 5, 12], "Stack4": [9, 1, 8, 2], "Stack5": [11, 14]}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 247, "start_state": {"Stack1": [4, 9], "Stack2": [1], "Stack3": [15, 2, 5, 14, 12], "Stack4": [11, 13], "Stack5": [3, 6, 10, 7, 8]}, "goal_state": {"Stack1": [8, 9, 11, 5, 10, 15, 1, 4, 12, 14, 7, 6, 13, 3, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack5", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 248, "start_state": {"Stack1": [7, 5, 4], "Stack2": [10, 8], "Stack3": [11, 13, 14], "Stack4": [9, 2, 6, 12], "Stack5": [1, 15, 3]}, "goal_state": {"Stack1": [11, 3, 2, 5], "Stack2": [4, 14], "Stack3": [9, 12, 7], "Stack4": [10, 15, 6], "Stack5": [1, 8, 13]}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack4", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 249, "start_state": {"Stack1": [3, 1, 13, 11, 2, 15], "Stack2": [4, 7, 5, 12], "Stack3": [9, 6], "Stack4": [8, 14], "Stack5": [10]}, "goal_state": {"Stack1": [3, 4], "Stack2": [15, 9, 6], "Stack3": [2, 11, 7, 1], "Stack4": [8, 10, 12], "Stack5": [14, 5, 13]}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack3", "Stack2"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 250, "start_state": {"Stack1": [12, 3, 13, 9, 15, 11], "Stack2": [1, 14, 4, 8], "Stack3": [7, 5, 10], "Stack4": [2], "Stack5": [6]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 251, "start_state": {"Stack1": [5, 6], "Stack2": [2, 8, 9], "Stack3": [7, 15, 10], "Stack4": [3, 14, 13, 12], "Stack5": [1, 11, 4]}, "goal_state": {"Stack1": [6, 10, 3], "Stack2": [7, 4, 14], "Stack3": [13, 15], "Stack4": [5, 1, 12, 8], "Stack5": [11, 9, 2]}, "fix_order": ["Stack4", "Stack2", "Stack1", "Stack3", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 252, "start_state": {"Stack1": [13, 3, 10, 11, 1, 2], "Stack2": [4, 7], "Stack3": [5], "Stack4": [9, 8], "Stack5": [12, 15, 14, 6]}, "goal_state": {"Stack1": [8, 5, 15], "Stack2": [6, 1, 11, 9], "Stack3": [12, 14, 7, 4], "Stack4": [3], "Stack5": [13, 10, 2]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 253, "start_state": {"Stack1": [7, 13, 9, 4], "Stack2": [1, 8, 14], "Stack3": [5, 10, 2], "Stack4": [11, 6, 12], "Stack5": [15, 3]}, "goal_state": {"Stack1": [9, 8, 6, 12, 13, 2, 7, 3, 4, 5, 11, 10, 15, 14, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 254, "start_state": {"Stack1": [15, 13, 14], "Stack2": [10, 7, 1, 8], "Stack3": [2, 12, 9], "Stack4": [3, 6, 5], "Stack5": [4, 11]}, "goal_state": {"Stack1": [1, 11], "Stack2": [13, 14], "Stack3": [6, 8, 5, 2], "Stack4": [15, 9], "Stack5": [3, 10, 7, 4, 12]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 255, "start_state": {"Stack1": [6, 14, 9, 3, 11, 2], "Stack2": [15], "Stack3": [13, 4, 10, 1], "Stack4": [7, 8, 5], "Stack5": [12]}, "goal_state": {"Stack1": [4, 7, 11, 2, 8, 12, 15, 13, 10, 9, 14, 6, 3, 5, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 256, "start_state": {"Stack1": [4], "Stack2": [14, 5, 3, 10], "Stack3": [12, 13, 7, 11, 6], "Stack4": [15, 1], "Stack5": [2, 9, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 257, "start_state": {"Stack1": [3, 12], "Stack2": [1, 10, 8], "Stack3": [13, 15, 6], "Stack4": [2], "Stack5": [5, 4, 7, 14, 11, 9]}, "goal_state": {"Stack1": [], "Stack2": [10, 5, 4, 6, 15, 3, 14, 2, 13, 12, 9, 8, 1, 11, 7], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 258, "start_state": {"Stack1": [8, 4, 15, 9, 2, 14], "Stack2": [6, 5, 10], "Stack3": [3, 11], "Stack4": [7], "Stack5": [13, 12, 1]}, "goal_state": {"Stack1": [14, 9, 2], "Stack2": [6, 8, 4], "Stack3": [11, 10], "Stack4": [5, 7, 1], "Stack5": [15, 3, 12, 13]}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 259, "start_state": {"Stack1": [4, 6, 11, 15], "Stack2": [12], "Stack3": [3, 5], "Stack4": [10, 1, 7, 9, 2], "Stack5": [14, 13, 8]}, "goal_state": {"Stack1": [9, 7, 4, 2, 14, 10, 11, 13, 1, 5, 12, 8, 3, 6, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack3", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 260, "start_state": {"Stack1": [], "Stack2": [4, 3], "Stack3": [5, 8, 12], "Stack4": [13, 2], "Stack5": [1, 9, 14, 7, 15, 10, 11, 6]}, "goal_state": {"Stack1": [7, 14, 5, 9, 13, 11, 6, 8, 10, 2, 4, 3, 12, 15, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 261, "start_state": {"Stack1": [9], "Stack2": [3, 6, 15, 5, 13], "Stack3": [11, 10, 7], "Stack4": [4, 8, 1, 2], "Stack5": [14, 12]}, "goal_state": {"Stack1": [11, 10], "Stack2": [14, 2, 7, 3], "Stack3": [5, 1], "Stack4": [6, 9], "Stack5": [13, 15, 8, 4, 12]}, "fix_order": ["Stack5", "Stack1", "Stack2", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 262, "start_state": {"Stack1": [10, 5, 7, 13], "Stack2": [6, 15, 8], "Stack3": [3, 9, 14, 11, 2], "Stack4": [12], "Stack5": [4, 1]}, "goal_state": {"Stack1": [2, 3, 11], "Stack2": [6, 8, 15], "Stack3": [1, 7, 12], "Stack4": [5, 10, 13], "Stack5": [4, 9, 14]}, "fix_order": ["Stack5", "Stack4", "Stack2", "Stack3", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 263, "start_state": {"Stack1": [10, 12, 9, 6, 4], "Stack2": [1], "Stack3": [2, 13], "Stack4": [8, 3], "Stack5": [11, 5, 7, 14, 15]}, "goal_state": {"Stack1": [12, 15, 11], "Stack2": [3, 8], "Stack3": [13], "Stack4": [14, 5, 6, 4, 9], "Stack5": [1, 2, 10, 7]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 264, "start_state": {"Stack1": [13, 1, 12, 4, 11, 2], "Stack2": [7, 9, 6, 15], "Stack3": [8], "Stack4": [3, 10], "Stack5": [14, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [14, 13, 2, 12, 9, 1, 6, 8, 7, 15, 3, 5, 4, 11, 10], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack1", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 265, "start_state": {"Stack1": [8, 6], "Stack2": [13, 3, 15, 9, 12], "Stack3": [1, 4, 5], "Stack4": [7, 10], "Stack5": [2, 11, 14]}, "goal_state": {"Stack1": [6, 7, 14, 8], "Stack2": [10], "Stack3": [15, 9, 1], "Stack4": [5, 13, 11, 12, 4], "Stack5": [3, 2]}, "fix_order": ["Stack2", "Stack3", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 266, "start_state": {"Stack1": [4, 14, 13], "Stack2": [], "Stack3": [12, 1], "Stack4": [6, 8, 5, 15, 11], "Stack5": [2, 10, 9, 3, 7]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 267, "start_state": {"Stack1": [9, 8, 1], "Stack2": [13], "Stack3": [6, 10, 7, 15, 4, 2], "Stack4": [11, 5, 3], "Stack5": [14, 12]}, "goal_state": {"Stack1": [13, 5], "Stack2": [2, 7, 6, 1, 8], "Stack3": [10, 4, 9, 11], "Stack4": [3, 14, 15], "Stack5": [12]}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 268, "start_state": {"Stack1": [7, 2, 6, 8, 3, 11], "Stack2": [5], "Stack3": [4, 13, 14], "Stack4": [15], "Stack5": [10, 12, 9, 1]}, "goal_state": {"Stack1": [5, 14, 4], "Stack2": [12, 10], "Stack3": [15, 9, 13, 7, 6], "Stack4": [8, 3, 2], "Stack5": [1, 11]}, "fix_order": ["Stack4", "Stack1", "Stack5", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 269, "start_state": {"Stack1": [3, 12, 9], "Stack2": [14], "Stack3": [], "Stack4": [8, 4], "Stack5": [7, 15, 1, 5, 13, 11, 6, 10, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [7, 8, 12, 1, 3, 9, 2, 13, 11, 6, 14, 15, 10, 4, 5], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 270, "start_state": {"Stack1": [4, 8], "Stack2": [3, 9], "Stack3": [12, 13, 7, 10], "Stack4": [11, 15, 2], "Stack5": [1, 5, 14, 6]}, "goal_state": {"Stack1": [], "Stack2": [10, 11, 6, 12, 4, 13, 9, 5, 7, 2, 14, 1, 8, 3, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 271, "start_state": {"Stack1": [1, 4, 14, 6], "Stack2": [], "Stack3": [10, 8, 11, 2], "Stack4": [7, 13, 9, 3], "Stack5": [15, 5, 12]}, "goal_state": {"Stack1": [], "Stack2": [8, 6, 13, 4, 11, 7, 5, 1, 14, 2, 9, 10, 15, 3, 12], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 272, "start_state": {"Stack1": [14, 1, 4], "Stack2": [12, 5], "Stack3": [15, 7, 6, 8, 2, 13], "Stack4": [9], "Stack5": [10, 11, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [9, 14, 6, 8, 7, 1, 11, 12, 15, 2, 10, 13, 5, 3, 4], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack1", "Stack2", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 273, "start_state": {"Stack1": [11, 8, 5, 6], "Stack2": [2, 4, 15], "Stack3": [3, 10, 7, 1], "Stack4": [12, 9, 13], "Stack5": [14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [10, 7, 13, 3, 5, 15, 4, 1, 6, 2, 14, 9, 12, 11, 8], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 274, "start_state": {"Stack1": [6, 9, 2, 15], "Stack2": [1, 8, 5], "Stack3": [4], "Stack4": [12, 7, 11], "Stack5": [13, 10, 3, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 7, 14, 12, 13, 10, 15, 5, 3, 9, 8, 6, 11, 4]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack3", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 275, "start_state": {"Stack1": [10, 15], "Stack2": [13, 7, 5], "Stack3": [3, 11, 14, 4], "Stack4": [1, 9, 12, 8], "Stack5": [6, 2]}, "goal_state": {"Stack1": [3, 1, 2, 12, 10, 4, 8, 15, 7, 14, 11, 5, 6, 13, 9], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 276, "start_state": {"Stack1": [15, 10, 12, 3], "Stack2": [7, 1, 11], "Stack3": [14, 13, 8], "Stack4": [9], "Stack5": [2, 4, 5, 6]}, "goal_state": {"Stack1": [], "Stack2": [8, 2, 14, 15, 11, 10, 1, 9, 4, 7, 12, 6, 3, 13, 5], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 277, "start_state": {"Stack1": [10, 9, 3, 12, 7, 13], "Stack2": [14, 1], "Stack3": [], "Stack4": [6, 4], "Stack5": [8, 2, 15, 11, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 6, 2, 14, 10, 3, 13, 4, 8, 11, 5, 1, 12, 9, 7], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 278, "start_state": {"Stack1": [3, 15, 9], "Stack2": [13, 10, 5], "Stack3": [7, 14, 8], "Stack4": [2, 6, 1, 4], "Stack5": [12, 11]}, "goal_state": {"Stack1": [12, 10], "Stack2": [6, 13], "Stack3": [1, 5, 14, 2, 8], "Stack4": [15, 11, 9], "Stack5": [7, 4, 3]}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 279, "start_state": {"Stack1": [7, 1, 3, 14, 8], "Stack2": [5, 11, 4, 6, 13], "Stack3": [], "Stack4": [2, 12, 15, 9], "Stack5": [10]}, "goal_state": {"Stack1": [2, 6, 10], "Stack2": [4, 14, 15], "Stack3": [1, 5, 13], "Stack4": [7, 9, 11], "Stack5": [3, 8, 12]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 280, "start_state": {"Stack1": [15, 10, 4, 3], "Stack2": [8, 12, 14, 9, 5], "Stack3": [2, 11], "Stack4": [13, 7, 6], "Stack5": [1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 281, "start_state": {"Stack1": [9, 2], "Stack2": [4, 6, 3], "Stack3": [15, 5, 1, 10], "Stack4": [13], "Stack5": [7, 14, 8, 12, 11]}, "goal_state": {"Stack1": [1, 6, 9], "Stack2": [13, 11, 15, 7, 14, 2, 10, 5], "Stack3": [4], "Stack4": [12], "Stack5": [8, 3]}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 282, "start_state": {"Stack1": [11], "Stack2": [8, 9, 1], "Stack3": [2, 14, 13, 3], "Stack4": [12, 4, 10, 15], "Stack5": [5, 6, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 283, "start_state": {"Stack1": [13, 7, 1], "Stack2": [12, 4, 2], "Stack3": [9, 8, 14, 3, 6], "Stack4": [10], "Stack5": [5, 15, 11]}, "goal_state": {"Stack1": [2, 6, 10], "Stack2": [15, 11], "Stack3": [4], "Stack4": [12, 13, 8, 14, 1], "Stack5": [3, 9, 7, 5]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 284, "start_state": {"Stack1": [5, 7, 3, 15], "Stack2": [10, 6, 12], "Stack3": [14, 8, 1], "Stack4": [2, 13], "Stack5": [9, 4, 11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 285, "start_state": {"Stack1": [8, 14, 11, 1, 7, 10], "Stack2": [3, 9, 5], "Stack3": [12], "Stack4": [13, 15], "Stack5": [2, 6, 4]}, "goal_state": {"Stack1": [12], "Stack2": [9, 13, 5, 6, 11], "Stack3": [1, 7, 10, 2, 4], "Stack4": [3, 8], "Stack5": [14, 15]}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack5", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 286, "start_state": {"Stack1": [15], "Stack2": [4, 7, 14], "Stack3": [10, 6, 2, 12], "Stack4": [3, 9], "Stack5": [1, 13, 11, 5, 8]}, "goal_state": {"Stack1": [3, 5, 13], "Stack2": [6], "Stack3": [7, 10, 1, 4, 9], "Stack4": [11, 8], "Stack5": [2, 15, 12, 14]}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 287, "start_state": {"Stack1": [15], "Stack2": [9, 6], "Stack3": [4, 13, 14, 11], "Stack4": [10, 1, 12, 3, 2], "Stack5": [7, 8, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 288, "start_state": {"Stack1": [4, 1], "Stack2": [8, 2, 12, 5, 13, 10], "Stack3": [7, 14, 9, 3], "Stack4": [11, 6, 15], "Stack5": []}, "goal_state": {"Stack1": [8, 7], "Stack2": [5, 6, 2, 14], "Stack3": [13], "Stack4": [10, 1, 9, 3, 15, 4], "Stack5": [12, 11]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 289, "start_state": {"Stack1": [4, 10, 15], "Stack2": [8, 7, 12, 5], "Stack3": [9, 6, 1], "Stack4": [13, 11], "Stack5": [14, 3, 2]}, "goal_state": {"Stack1": [12, 14, 2, 5], "Stack2": [13, 8, 10], "Stack3": [1, 7], "Stack4": [15, 9, 11, 6], "Stack5": [4, 3]}, "fix_order": ["Stack3", "Stack2", "Stack5", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 290, "start_state": {"Stack1": [13, 7], "Stack2": [9], "Stack3": [2, 6, 1, 11], "Stack4": [15, 4, 14, 10], "Stack5": [5, 12, 8, 3]}, "goal_state": {"Stack1": [2, 11, 13, 15, 7, 14, 9, 8, 4, 5, 10, 12, 6, 1, 3], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 291, "start_state": {"Stack1": [9, 14, 12, 5], "Stack2": [10, 15], "Stack3": [4, 8, 1, 7], "Stack4": [2, 3, 11], "Stack5": [13, 6]}, "goal_state": {"Stack1": [1, 4, 11, 8, 3], "Stack2": [6, 13], "Stack3": [15, 5, 7], "Stack4": [14, 2], "Stack5": [10, 12, 9]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 292, "start_state": {"Stack1": [4, 5], "Stack2": [11, 10, 15], "Stack3": [13, 9, 14, 7], "Stack4": [12, 8, 1], "Stack5": [6, 2, 3]}, "goal_state": {"Stack1": [2, 6, 12, 13, 10, 8, 14, 9, 5, 3, 11, 15, 1, 4, 7], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 293, "start_state": {"Stack1": [12, 10], "Stack2": [1, 14, 7], "Stack3": [9, 3, 15], "Stack4": [2, 11, 4, 8, 13], "Stack5": [6, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 294, "start_state": {"Stack1": [12, 6, 13], "Stack2": [7, 1, 5], "Stack3": [4], "Stack4": [9, 2, 14, 11, 15], "Stack5": [10, 3, 8]}, "goal_state": {"Stack1": [1, 15], "Stack2": [13, 3], "Stack3": [6, 11], "Stack4": [8, 12, 5, 10, 2], "Stack5": [4, 14, 9, 7]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 295, "start_state": {"Stack1": [12, 2, 9], "Stack2": [10, 11, 7], "Stack3": [1], "Stack4": [6], "Stack5": [13, 15, 14, 4, 3, 8, 5]}, "goal_state": {"Stack1": [9, 8, 11], "Stack2": [3, 13, 5, 1, 2, 10], "Stack3": [4, 6, 14], "Stack4": [15, 7], "Stack5": [12]}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 296, "start_state": {"Stack1": [6, 12, 1, 2, 4, 11], "Stack2": [], "Stack3": [13], "Stack4": [14, 5, 7, 9], "Stack5": [15, 3, 8, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [2, 3, 10, 8, 4, 15, 9, 11, 6, 13, 7, 5, 1, 12, 14], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 297, "start_state": {"Stack1": [10, 12], "Stack2": [9], "Stack3": [2, 8, 15, 14, 11, 6], "Stack4": [5, 1, 4, 13], "Stack5": [7, 3]}, "goal_state": {"Stack1": [12, 3], "Stack2": [11, 15, 13, 10, 9], "Stack3": [8, 14, 4, 5], "Stack4": [7, 1], "Stack5": [2, 6]}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 298, "start_state": {"Stack1": [8, 7], "Stack2": [4, 14, 9, 6, 5], "Stack3": [13, 12], "Stack4": [1, 15, 2, 11, 3], "Stack5": [10]}, "goal_state": {"Stack1": [7, 14, 6, 12], "Stack2": [9], "Stack3": [3, 15, 4, 13], "Stack4": [11, 8], "Stack5": [1, 2, 10, 5]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 299, "start_state": {"Stack1": [1, 8, 12], "Stack2": [2, 7, 3, 5, 13], "Stack3": [11, 14], "Stack4": [10], "Stack5": [15, 6, 4, 9]}, "goal_state": {"Stack1": [10, 3, 5, 12, 7, 15, 14, 8, 4, 2, 11, 6, 1, 13, 9], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 300, "start_state": {"Stack1": [], "Stack2": [11, 10], "Stack3": [13, 6, 2, 7, 14, 5], "Stack4": [3, 9, 4], "Stack5": [12, 15, 8, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [13, 7, 15, 6, 4, 14, 10, 1, 12, 3, 2, 8, 11, 5, 9]}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 301, "start_state": {"Stack1": [5], "Stack2": [7, 3, 11, 6, 14, 2], "Stack3": [12, 15, 9], "Stack4": [13, 8], "Stack5": [10, 1, 4]}, "goal_state": {"Stack1": [1, 12, 13], "Stack2": [4, 14, 15], "Stack3": [2, 5, 7], "Stack4": [8, 10, 11], "Stack5": [3, 6, 9]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 302, "start_state": {"Stack1": [2, 10], "Stack2": [13, 15], "Stack3": [11, 4, 1, 9, 7, 6], "Stack4": [3, 5, 8], "Stack5": [12, 14]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 303, "start_state": {"Stack1": [5, 14, 12, 3], "Stack2": [2], "Stack3": [7, 1, 10, 6], "Stack4": [9, 8, 4], "Stack5": [13, 15, 11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 304, "start_state": {"Stack1": [], "Stack2": [1, 3, 5, 2], "Stack3": [12, 13, 7, 14, 8, 9, 4], "Stack4": [15, 11, 6], "Stack5": [10]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 305, "start_state": {"Stack1": [5, 10, 11, 9, 13], "Stack2": [15], "Stack3": [3, 4], "Stack4": [2], "Stack5": [14, 7, 6, 12, 1, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack4", "Stack5", "Stack3", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 306, "start_state": {"Stack1": [1, 12, 11, 2], "Stack2": [6, 8, 14], "Stack3": [4, 3, 5], "Stack4": [9, 10, 13], "Stack5": [7, 15]}, "goal_state": {"Stack1": [1, 5, 10], "Stack2": [7, 12, 15], "Stack3": [2, 3, 14], "Stack4": [4, 8, 11], "Stack5": [6, 9, 13]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack5", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 307, "start_state": {"Stack1": [8, 7, 9, 12], "Stack2": [13, 5, 6, 2], "Stack3": [], "Stack4": [1, 3, 15, 10, 11, 4, 14], "Stack5": []}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [5, 6, 12, 4, 1, 7, 8, 13, 11, 10, 9, 3, 15, 2, 14]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 308, "start_state": {"Stack1": [10, 12, 14, 6, 2], "Stack2": [15], "Stack3": [3, 11, 7, 4, 9, 13], "Stack4": [1, 5, 8], "Stack5": []}, "goal_state": {"Stack1": [7], "Stack2": [8, 13, 10, 3, 14], "Stack3": [5, 6, 2], "Stack4": [11, 9, 1], "Stack5": [15, 12, 4]}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 309, "start_state": {"Stack1": [2, 6, 7, 8], "Stack2": [9, 10, 12, 4], "Stack3": [5], "Stack4": [13, 3, 1], "Stack5": [14, 11, 15]}, "goal_state": {"Stack1": [5, 3], "Stack2": [15, 14, 8, 2], "Stack3": [10, 11], "Stack4": [12, 13, 9], "Stack5": [6, 4, 1, 7]}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 310, "start_state": {"Stack1": [14, 2], "Stack2": [3, 5, 11], "Stack3": [15, 9, 13], "Stack4": [4, 12], "Stack5": [1, 8, 10, 6, 7]}, "goal_state": {"Stack1": [10, 1], "Stack2": [9, 7], "Stack3": [2, 15], "Stack4": [8, 12, 6, 14, 3], "Stack5": [13, 5, 4, 11]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 311, "start_state": {"Stack1": [4, 1, 7, 14], "Stack2": [11, 3, 9], "Stack3": [12, 8, 13], "Stack4": [15, 6, 5], "Stack5": [10, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack1", "Stack3", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 312, "start_state": {"Stack1": [5], "Stack2": [2, 13, 7, 15, 12, 1, 3], "Stack3": [9], "Stack4": [14], "Stack5": [10, 8, 6, 4, 11]}, "goal_state": {"Stack1": [14, 11, 4, 1, 3, 12, 2, 6, 10, 8, 13, 9, 15, 5, 7], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 313, "start_state": {"Stack1": [9, 4], "Stack2": [6, 5, 2, 8, 12, 7], "Stack3": [15, 14, 13, 1], "Stack4": [10, 3], "Stack5": [11]}, "goal_state": {"Stack1": [7, 6, 14, 9], "Stack2": [10, 15], "Stack3": [12, 4, 8, 5], "Stack4": [1, 13, 2, 11], "Stack5": [3]}, "fix_order": ["Stack1", "Stack4", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 314, "start_state": {"Stack1": [3, 11, 2, 9], "Stack2": [5], "Stack3": [1, 10, 14, 12, 15, 6], "Stack4": [7, 4], "Stack5": [8, 13]}, "goal_state": {"Stack1": [], "Stack2": [13, 6, 3, 1, 4, 15, 12, 14, 9, 8, 2, 5, 11, 7, 10], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 315, "start_state": {"Stack1": [3, 12, 10, 4], "Stack2": [9, 11], "Stack3": [6, 13], "Stack4": [5, 1, 8, 2], "Stack5": [15, 14, 7]}, "goal_state": {"Stack1": [13, 12], "Stack2": [9, 14, 3, 5], "Stack3": [2, 15, 6], "Stack4": [7, 11, 8], "Stack5": [1, 4, 10]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack5", "Stack3"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 316, "start_state": {"Stack1": [8, 15], "Stack2": [4, 11, 7], "Stack3": [1, 3, 10, 9, 2, 6], "Stack4": [13], "Stack5": [12, 5, 14]}, "goal_state": {"Stack1": [1, 8, 14], "Stack2": [5, 13, 15], "Stack3": [6, 9, 11], "Stack4": [2, 4, 12], "Stack5": [3, 7, 10]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 317, "start_state": {"Stack1": [11, 5, 7], "Stack2": [14, 1, 4, 9, 13, 10, 2], "Stack3": [8, 12], "Stack4": [3, 6], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 8, 13, 11, 5, 6, 12, 3, 15, 4, 10, 14, 9, 7], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 318, "start_state": {"Stack1": [1, 9], "Stack2": [10, 11], "Stack3": [14, 8, 2], "Stack4": [3, 6, 15], "Stack5": [12, 5, 7, 13, 4]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [6, 5, 15, 10, 14, 4, 13, 12, 11, 8, 9, 1, 3, 2, 7]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 319, "start_state": {"Stack1": [8, 14, 15], "Stack2": [6, 2], "Stack3": [13, 7, 1], "Stack4": [9], "Stack5": [5, 11, 4, 3, 10, 12]}, "goal_state": {"Stack1": [6, 12, 11], "Stack2": [5, 10], "Stack3": [8, 14], "Stack4": [13, 15, 3, 7, 9, 2], "Stack5": [1, 4]}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 320, "start_state": {"Stack1": [15, 4, 14, 8, 7, 3], "Stack2": [12], "Stack3": [13, 9, 5], "Stack4": [1], "Stack5": [11, 10, 2, 6]}, "goal_state": {"Stack1": [9, 8], "Stack2": [3, 2, 5, 6], "Stack3": [11, 13, 10], "Stack4": [4, 7], "Stack5": [12, 14, 15, 1]}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 321, "start_state": {"Stack1": [11, 2], "Stack2": [3, 1, 9], "Stack3": [10, 13, 15], "Stack4": [14, 6, 12], "Stack5": [4, 7, 8, 5]}, "goal_state": {"Stack1": [2], "Stack2": [11, 14], "Stack3": [3, 9, 4], "Stack4": [13, 15], "Stack5": [5, 12, 10, 6, 1, 7, 8]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 322, "start_state": {"Stack1": [4, 8, 14], "Stack2": [2, 5], "Stack3": [6, 10, 12], "Stack4": [9, 3], "Stack5": [15, 1, 13, 7, 11]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 323, "start_state": {"Stack1": [2, 15, 1, 4], "Stack2": [13, 14, 11, 5, 6], "Stack3": [10, 7], "Stack4": [8, 9], "Stack5": [3, 12]}, "goal_state": {"Stack1": [8, 4], "Stack2": [13, 3], "Stack3": [5, 10, 7, 6], "Stack4": [2, 12, 11, 15], "Stack5": [1, 9, 14]}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 324, "start_state": {"Stack1": [9, 12], "Stack2": [3, 2, 11], "Stack3": [6, 7, 10, 8, 13], "Stack4": [15, 1], "Stack5": [4, 5, 14]}, "goal_state": {"Stack1": [3, 6, 15, 10, 1, 5, 11, 12, 8, 9, 14, 4, 7, 2, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 325, "start_state": {"Stack1": [11, 10, 3], "Stack2": [1, 15, 6, 14, 2], "Stack3": [9, 7, 4, 12], "Stack4": [13], "Stack5": [5, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [10, 14, 12, 1, 13, 7, 8, 6, 11, 15, 4, 2, 9, 5, 3], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 326, "start_state": {"Stack1": [8, 15, 13, 12, 2, 6], "Stack2": [4, 14, 1], "Stack3": [], "Stack4": [10, 7], "Stack5": [5, 11, 9, 3]}, "goal_state": {"Stack1": [15], "Stack2": [9, 5, 8, 11], "Stack3": [3, 6, 10, 1], "Stack4": [13, 4, 12, 7], "Stack5": [2, 14]}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 327, "start_state": {"Stack1": [], "Stack2": [9, 6], "Stack3": [10, 5, 7, 1], "Stack4": [14, 2, 11, 15, 4, 8, 13, 3, 12], "Stack5": []}, "goal_state": {"Stack1": [1, 2, 8], "Stack2": [3, 5, 7], "Stack3": [6, 9, 11], "Stack4": [10, 12, 13], "Stack5": [4, 14, 15]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 328, "start_state": {"Stack1": [3], "Stack2": [13, 14, 7, 8, 2], "Stack3": [10], "Stack4": [4, 6, 15, 11], "Stack5": [12, 1, 5, 9]}, "goal_state": {"Stack1": [2, 10, 1, 7], "Stack2": [11, 3, 4, 8], "Stack3": [12, 9, 14, 13, 6], "Stack4": [5], "Stack5": [15]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 329, "start_state": {"Stack1": [9, 11, 5], "Stack2": [10, 7, 13, 3], "Stack3": [4], "Stack4": [1, 2, 12, 6, 8], "Stack5": [14, 15]}, "goal_state": {"Stack1": [], "Stack2": [11, 7, 2, 9, 5, 4, 14, 15, 3, 8, 6, 12, 10, 1, 13], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 330, "start_state": {"Stack1": [14, 7], "Stack2": [1, 6, 10], "Stack3": [8, 13, 2, 5], "Stack4": [12, 11], "Stack5": [15, 9, 4, 3]}, "goal_state": {"Stack1": [10, 6, 8], "Stack2": [15, 3, 12], "Stack3": [2, 13], "Stack4": [9, 5, 4, 11], "Stack5": [14, 1, 7]}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 331, "start_state": {"Stack1": [5, 11, 2, 15, 14, 13, 12], "Stack2": [10, 9, 6], "Stack3": [1, 8, 7], "Stack4": [4], "Stack5": [3]}, "goal_state": {"Stack1": [8, 7, 15, 1, 4, 10], "Stack2": [14, 12, 2, 13], "Stack3": [3, 5], "Stack4": [11], "Stack5": [9, 6]}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 332, "start_state": {"Stack1": [3, 13, 8, 4], "Stack2": [10, 15, 9, 7, 1], "Stack3": [6], "Stack4": [5], "Stack5": [14, 11, 2, 12]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack1", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 333, "start_state": {"Stack1": [1, 4, 2, 12, 13, 11], "Stack2": [3], "Stack3": [6, 8, 15], "Stack4": [9, 10, 7], "Stack5": [14, 5]}, "goal_state": {"Stack1": [1], "Stack2": [9, 6, 2, 12], "Stack3": [8, 3, 5, 15], "Stack4": [7, 14, 13], "Stack5": [4, 10, 11]}, "fix_order": ["Stack5", "Stack4", "Stack2", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 334, "start_state": {"Stack1": [13, 6, 8], "Stack2": [1, 3, 10, 14, 7], "Stack3": [12, 4], "Stack4": [9, 5, 2, 15], "Stack5": [11]}, "goal_state": {"Stack1": [], "Stack2": [14, 13, 12, 2, 9, 11, 10, 15, 3, 7, 8, 1, 4, 6, 5], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 335, "start_state": {"Stack1": [10, 3], "Stack2": [12, 9], "Stack3": [15, 2, 6], "Stack4": [5, 7, 4, 11], "Stack5": [13, 1, 8, 14]}, "goal_state": {"Stack1": [14], "Stack2": [15, 7, 8], "Stack3": [2, 1, 11, 13, 6, 9], "Stack4": [10, 3], "Stack5": [4, 12, 5]}, "fix_order": ["Stack3", "Stack1", "Stack4", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 336, "start_state": {"Stack1": [7, 3, 10], "Stack2": [8, 4], "Stack3": [5, 15, 14], "Stack4": [6, 11, 1, 13, 12], "Stack5": [2, 9]}, "goal_state": {"Stack1": [5, 8, 12], "Stack2": [13, 9, 6], "Stack3": [2, 10, 4], "Stack4": [15], "Stack5": [14, 7, 3, 1, 11]}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 337, "start_state": {"Stack1": [12], "Stack2": [6, 1, 15], "Stack3": [13, 9, 10, 4], "Stack4": [2, 7, 3], "Stack5": [8, 11, 14, 5]}, "goal_state": {"Stack1": [5, 15], "Stack2": [7, 11], "Stack3": [10, 3, 8, 14, 2], "Stack4": [12, 13], "Stack5": [4, 9, 1, 6]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 338, "start_state": {"Stack1": [5, 15, 11, 2], "Stack2": [3, 8], "Stack3": [7], "Stack4": [4, 1, 10, 13, 12], "Stack5": [6, 9, 14]}, "goal_state": {"Stack1": [], "Stack2": [14, 3, 7, 11, 15, 6, 8, 10, 9, 1, 12, 4, 5, 13, 2], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 339, "start_state": {"Stack1": [3, 4, 1], "Stack2": [8, 13, 14, 9], "Stack3": [5, 11], "Stack4": [15, 6], "Stack5": [2, 10, 12, 7]}, "goal_state": {"Stack1": [14, 10, 1], "Stack2": [8, 15], "Stack3": [3, 4, 6, 13, 12], "Stack4": [7, 5, 11, 9], "Stack5": [2]}, "fix_order": ["Stack2", "Stack3", "Stack1", "Stack4", "Stack5"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 340, "start_state": {"Stack1": [6, 9], "Stack2": [14, 10, 5, 4], "Stack3": [13, 3, 11, 1], "Stack4": [15, 7, 2], "Stack5": [12, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [2, 14, 12, 6, 7, 11, 1, 4, 5, 10, 9, 15, 8, 3, 13]}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 341, "start_state": {"Stack1": [9, 4, 15, 13, 12, 7], "Stack2": [1, 10], "Stack3": [5, 14, 6, 2], "Stack4": [11, 3, 8], "Stack5": []}, "goal_state": {"Stack1": [13, 3, 8, 4], "Stack2": [7, 15], "Stack3": [6], "Stack4": [11, 5, 9, 12, 14, 2, 1], "Stack5": [10]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 342, "start_state": {"Stack1": [10, 9, 15, 12], "Stack2": [1], "Stack3": [4, 8, 13], "Stack4": [14, 6, 2, 11], "Stack5": [7, 3, 5]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 343, "start_state": {"Stack1": [1, 6], "Stack2": [13, 8, 3], "Stack3": [12, 4, 9, 15], "Stack4": [2, 5], "Stack5": [14, 11, 10, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [2, 6, 15, 5, 8, 9, 1, 7, 4, 13, 10, 3, 12, 14, 11]}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 344, "start_state": {"Stack1": [5, 8, 15, 12], "Stack2": [1, 3, 13, 2, 4], "Stack3": [7], "Stack4": [14, 10, 11], "Stack5": [9, 6]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack3", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 345, "start_state": {"Stack1": [8, 11], "Stack2": [12], "Stack3": [7, 9, 15, 5, 13], "Stack4": [3, 10, 2, 1], "Stack5": [6, 4, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [2, 7, 15, 5, 4, 3, 13, 9, 6, 12, 14, 8, 11, 1, 10]}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 346, "start_state": {"Stack1": [4, 8, 15], "Stack2": [9, 5, 7], "Stack3": [14, 10], "Stack4": [6, 11, 2], "Stack5": [3, 13, 12, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack1", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 347, "start_state": {"Stack1": [6, 14, 9, 8], "Stack2": [2, 1, 7, 5], "Stack3": [3, 4, 13], "Stack4": [15, 10, 12], "Stack5": [11]}, "goal_state": {"Stack1": [], "Stack2": [14, 11, 12, 15, 10, 9, 2, 8, 6, 4, 3, 5, 1, 7, 13], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 348, "start_state": {"Stack1": [10, 5, 14, 8], "Stack2": [6, 15], "Stack3": [3, 13, 9], "Stack4": [11, 4, 1, 7], "Stack5": [2, 12]}, "goal_state": {"Stack1": [14, 6, 4], "Stack2": [7, 11, 15, 10, 3], "Stack3": [9, 2], "Stack4": [1, 5], "Stack5": [13, 12, 8]}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 349, "start_state": {"Stack1": [12, 15], "Stack2": [10, 7, 3, 2, 1, 9], "Stack3": [], "Stack4": [8, 4, 6, 14], "Stack5": [11, 5, 13]}, "goal_state": {"Stack1": [15, 6], "Stack2": [5, 7, 9, 8], "Stack3": [14, 11, 4], "Stack4": [1, 13, 3, 10], "Stack5": [12, 2]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 350, "start_state": {"Stack1": [14], "Stack2": [], "Stack3": [13, 3, 12, 15, 1, 8, 5], "Stack4": [6, 11, 9, 4], "Stack5": [7, 2, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [2, 6, 5, 7, 4, 1, 14, 8, 3, 13, 15, 10, 11, 9, 12]}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 351, "start_state": {"Stack1": [], "Stack2": [8, 7, 3, 6], "Stack3": [2, 11, 12], "Stack4": [5, 4], "Stack5": [1, 14, 15, 13, 9, 10]}, "goal_state": {"Stack1": [13, 2], "Stack2": [7, 5], "Stack3": [1, 15, 11, 14], "Stack4": [3, 8, 10], "Stack5": [9, 6, 4, 12]}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 352, "start_state": {"Stack1": [14, 9, 5, 2, 1], "Stack2": [11, 8], "Stack3": [12, 4], "Stack4": [3, 15, 13, 7, 10], "Stack5": [6]}, "goal_state": {"Stack1": [8], "Stack2": [10, 15, 13, 9], "Stack3": [4, 1, 7, 14], "Stack4": [6, 3], "Stack5": [11, 12, 2, 5]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 353, "start_state": {"Stack1": [7, 8], "Stack2": [2, 6, 10, 3, 4, 14], "Stack3": [11], "Stack4": [12, 15, 9], "Stack5": [5, 13, 1]}, "goal_state": {"Stack1": [14, 3, 6, 12, 15, 9, 2, 8, 13, 10, 7, 5, 11, 4, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 354, "start_state": {"Stack1": [9], "Stack2": [6, 5, 13, 14], "Stack3": [4], "Stack4": [15, 3, 12, 7, 10], "Stack5": [11, 2, 8, 1]}, "goal_state": {"Stack1": [14, 10, 7], "Stack2": [8], "Stack3": [3, 12, 4], "Stack4": [2, 9, 13, 5, 15, 6], "Stack5": [1, 11]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 355, "start_state": {"Stack1": [13, 11, 7, 8, 14], "Stack2": [1, 10, 5], "Stack3": [9, 12, 4], "Stack4": [6], "Stack5": [2, 3, 15]}, "goal_state": {"Stack1": [], "Stack2": [10, 11, 8, 6, 2, 1, 9, 12, 13, 15, 4, 3, 5, 7, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 356, "start_state": {"Stack1": [2, 3], "Stack2": [8, 14, 15, 9, 13], "Stack3": [5, 10, 1], "Stack4": [11], "Stack5": [6, 12, 4, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack1", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 357, "start_state": {"Stack1": [13, 1, 2], "Stack2": [9, 12], "Stack3": [8], "Stack4": [7, 5, 10, 4, 3], "Stack5": [11, 6, 15, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 358, "start_state": {"Stack1": [14, 7], "Stack2": [12, 6, 4, 8], "Stack3": [5, 13, 10, 9], "Stack4": [11, 2, 1, 3], "Stack5": [15]}, "goal_state": {"Stack1": [11, 6, 12, 4], "Stack2": [14, 10, 7], "Stack3": [9, 5, 8, 1], "Stack4": [2, 13, 3], "Stack5": [15]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 359, "start_state": {"Stack1": [5, 10, 7, 3, 13], "Stack2": [4, 11], "Stack3": [12, 8, 6], "Stack4": [9, 14], "Stack5": [15, 2, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 360, "start_state": {"Stack1": [14, 9, 13], "Stack2": [6, 15, 5, 10], "Stack3": [3, 4, 8, 1], "Stack4": [11, 12, 2], "Stack5": [7]}, "goal_state": {"Stack1": [11, 5, 13, 3], "Stack2": [6, 8, 14, 7], "Stack3": [1, 12], "Stack4": [2, 15, 10, 4], "Stack5": [9]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 361, "start_state": {"Stack1": [1, 9, 14], "Stack2": [15], "Stack3": [7, 2, 12, 5, 8], "Stack4": [4, 3, 6, 11], "Stack5": [13, 10]}, "goal_state": {"Stack1": [1, 8, 9], "Stack2": [11, 12, 14], "Stack3": [10, 2, 3], "Stack4": [15, 5, 4], "Stack5": [7, 6, 13]}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack1", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 362, "start_state": {"Stack1": [], "Stack2": [11, 2, 10, 6, 8], "Stack3": [13, 1, 3], "Stack4": [5, 4, 14], "Stack5": [12, 9, 15, 7]}, "goal_state": {"Stack1": [10, 4, 14, 15], "Stack2": [5, 11, 7, 2], "Stack3": [8, 9, 3, 1], "Stack4": [6], "Stack5": [13, 12]}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 363, "start_state": {"Stack1": [6, 8, 1], "Stack2": [12, 13], "Stack3": [11, 4], "Stack4": [7, 2], "Stack5": [5, 14, 9, 3, 15, 10]}, "goal_state": {"Stack1": [13, 12], "Stack2": [2, 10, 3, 6, 1, 5], "Stack3": [4, 9, 8], "Stack4": [11, 14, 7], "Stack5": [15]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 364, "start_state": {"Stack1": [7, 5, 3], "Stack2": [13, 12, 8], "Stack3": [11, 14, 2], "Stack4": [1, 4, 6, 15], "Stack5": [10, 9]}, "goal_state": {"Stack1": [12, 3, 14], "Stack2": [2, 8, 7], "Stack3": [15, 6, 5], "Stack4": [11, 4, 13, 1], "Stack5": [10, 9]}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack2", "Stack3"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 365, "start_state": {"Stack1": [2, 13], "Stack2": [12, 1, 7], "Stack3": [6, 3, 5], "Stack4": [11, 10], "Stack5": [14, 15, 8, 9, 4]}, "goal_state": {"Stack1": [2, 6, 14], "Stack2": [3, 11, 15], "Stack3": [5, 9, 10], "Stack4": [1, 8, 12], "Stack5": [4, 7, 13]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack1", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 366, "start_state": {"Stack1": [12, 7, 4, 10, 8], "Stack2": [1, 3, 11], "Stack3": [9, 15], "Stack4": [5, 14, 6, 13], "Stack5": [2]}, "goal_state": {"Stack1": [4, 6, 13], "Stack2": [2, 3, 15], "Stack3": [8, 10, 14], "Stack4": [5, 11, 12], "Stack5": [1, 7, 9]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack3", "Stack4"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 367, "start_state": {"Stack1": [15, 7, 11], "Stack2": [5, 3, 12, 1], "Stack3": [13, 9, 8, 4], "Stack4": [10, 14], "Stack5": [6, 2]}, "goal_state": {"Stack1": [9, 12, 5], "Stack2": [6, 4, 14], "Stack3": [7, 8, 3, 2], "Stack4": [13, 15, 1], "Stack5": [11, 10]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 368, "start_state": {"Stack1": [1, 8, 10], "Stack2": [6, 14, 3, 15], "Stack3": [9], "Stack4": [4, 7, 11, 2], "Stack5": [5, 12, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 369, "start_state": {"Stack1": [5, 7, 6], "Stack2": [11, 15], "Stack3": [2], "Stack4": [12, 8, 14], "Stack5": [3, 9, 10, 13, 1, 4]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 370, "start_state": {"Stack1": [12, 3, 7, 9], "Stack2": [], "Stack3": [15, 8, 6, 2, 1, 5, 4], "Stack4": [11, 10, 14], "Stack5": [13]}, "goal_state": {"Stack1": [4, 11, 9], "Stack2": [8, 7, 6, 10], "Stack3": [15, 3, 2], "Stack4": [5], "Stack5": [13, 1, 14, 12]}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 371, "start_state": {"Stack1": [6, 10, 3, 14, 12, 11], "Stack2": [13, 15], "Stack3": [7, 9], "Stack4": [4, 1, 2], "Stack5": [8, 5]}, "goal_state": {"Stack1": [5, 7, 9], "Stack2": [1, 8, 14], "Stack3": [10, 11, 12], "Stack4": [2, 3, 6], "Stack5": [4, 13, 15]}, "fix_order": ["Stack3", "Stack1", "Stack4", "Stack5", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 372, "start_state": {"Stack1": [4], "Stack2": [13, 14], "Stack3": [3, 1, 15, 5], "Stack4": [7, 2, 10, 11], "Stack5": [6, 12, 8, 9]}, "goal_state": {"Stack1": [11, 8, 6, 4, 7], "Stack2": [9, 13, 3, 2], "Stack3": [12, 5], "Stack4": [1, 15], "Stack5": [14, 10]}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 373, "start_state": {"Stack1": [12, 8], "Stack2": [13, 1, 5, 7], "Stack3": [11, 15, 3], "Stack4": [10, 14, 9, 2, 4, 6], "Stack5": []}, "goal_state": {"Stack1": [6, 4, 8], "Stack2": [10, 14], "Stack3": [7, 13, 1], "Stack4": [2, 11, 5, 15], "Stack5": [3, 9, 12]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 374, "start_state": {"Stack1": [9, 4, 8, 14], "Stack2": [12], "Stack3": [13, 1, 10, 15, 5], "Stack4": [7], "Stack5": [11, 6, 2, 3]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack1", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 375, "start_state": {"Stack1": [2, 9], "Stack2": [5], "Stack3": [13, 4, 10, 1], "Stack4": [14, 7, 8, 11], "Stack5": [15, 6, 12, 3]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 376, "start_state": {"Stack1": [3, 7, 14], "Stack2": [2, 10, 9, 4, 13, 8], "Stack3": [], "Stack4": [12, 11, 1, 6], "Stack5": [5, 15]}, "goal_state": {"Stack1": [4, 6, 9, 11, 3, 13, 12, 8, 14, 2, 7, 10, 15, 5, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 377, "start_state": {"Stack1": [2, 12, 8], "Stack2": [11, 5], "Stack3": [4, 14, 15], "Stack4": [7, 3], "Stack5": [6, 13, 10, 9, 1]}, "goal_state": {"Stack1": [9, 7, 12, 3], "Stack2": [8, 15, 13, 4], "Stack3": [10, 5, 11], "Stack4": [14, 2], "Stack5": [6, 1]}, "fix_order": ["Stack5", "Stack1", "Stack3", "Stack2", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 378, "start_state": {"Stack1": [10, 4], "Stack2": [9, 12, 5, 11], "Stack3": [6, 14], "Stack4": [13, 2], "Stack5": [15, 1, 7, 8, 3]}, "goal_state": {"Stack1": [], "Stack2": [1, 6, 13, 3, 12, 5, 2, 7, 8, 4, 15, 11, 9, 10, 14], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack3", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 379, "start_state": {"Stack1": [6, 5], "Stack2": [10, 11, 8, 9, 14], "Stack3": [], "Stack4": [3, 15], "Stack5": [13, 1, 12, 2, 7, 4]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 380, "start_state": {"Stack1": [13, 7, 15, 5], "Stack2": [12, 3], "Stack3": [1, 9, 6], "Stack4": [14, 2, 11], "Stack5": [8, 10, 4]}, "goal_state": {"Stack1": [7, 9, 14, 15], "Stack2": [3, 1], "Stack3": [4, 12, 13], "Stack4": [10, 8, 6], "Stack5": [5, 2, 11]}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 381, "start_state": {"Stack1": [14, 2, 5], "Stack2": [6, 4], "Stack3": [7, 3], "Stack4": [15, 12, 9, 13], "Stack5": [1, 8, 10, 11]}, "goal_state": {"Stack1": [9, 11], "Stack2": [14, 3, 13, 10], "Stack3": [2, 1], "Stack4": [12, 7, 4, 15], "Stack5": [8, 6, 5]}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 382, "start_state": {"Stack1": [10, 11], "Stack2": [15, 2, 6, 1], "Stack3": [7], "Stack4": [9, 12], "Stack5": [4, 8, 13, 14, 3, 5]}, "goal_state": {"Stack1": [6], "Stack2": [8, 11, 15, 2, 13], "Stack3": [4], "Stack4": [3, 12, 9, 1], "Stack5": [14, 10, 7, 5]}, "fix_order": ["Stack3", "Stack5", "Stack2", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 383, "start_state": {"Stack1": [6, 5, 15, 11], "Stack2": [1, 4, 12], "Stack3": [8, 10, 7], "Stack4": [13, 9, 2, 3], "Stack5": [14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [12, 10, 4, 11, 9, 2, 8, 7, 15, 1, 5, 14, 13, 6, 3], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack5", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 384, "start_state": {"Stack1": [7, 2], "Stack2": [3, 10, 15], "Stack3": [14, 13, 9, 8, 11], "Stack4": [12], "Stack5": [4, 1, 6, 5]}, "goal_state": {"Stack1": [7, 9, 15], "Stack2": [3, 8, 13], "Stack3": [2, 6, 10], "Stack4": [4, 11, 12], "Stack5": [1, 5, 14]}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack2", "Stack4"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 385, "start_state": {"Stack1": [14, 9, 8, 10], "Stack2": [4, 6, 13, 5], "Stack3": [1], "Stack4": [3, 7, 11, 2], "Stack5": [12, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [6, 4, 3, 5, 14, 12, 15, 10, 1, 11, 13, 7, 9, 8, 2], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 386, "start_state": {"Stack1": [2, 3, 1, 9], "Stack2": [15, 4], "Stack3": [8, 7, 10], "Stack4": [5, 13], "Stack5": [12, 11, 6, 14]}, "goal_state": {"Stack1": [3], "Stack2": [1, 15, 14, 5, 10, 11], "Stack3": [6], "Stack4": [8, 12], "Stack5": [4, 7, 9, 2, 13]}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 387, "start_state": {"Stack1": [8, 9, 11, 7, 14], "Stack2": [3, 6], "Stack3": [4, 10], "Stack4": [12, 13, 1, 15, 5], "Stack5": [2]}, "goal_state": {"Stack1": [3, 13, 7, 1], "Stack2": [2, 12, 6, 14], "Stack3": [8, 5, 9, 15, 4], "Stack4": [11], "Stack5": [10]}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 388, "start_state": {"Stack1": [2, 11], "Stack2": [1, 5, 7, 8], "Stack3": [6, 10], "Stack4": [13, 4, 15], "Stack5": [12, 14, 3, 9]}, "goal_state": {"Stack1": [7, 14, 10], "Stack2": [9, 12], "Stack3": [3, 15, 1, 11], "Stack4": [5, 6], "Stack5": [13, 8, 4, 2]}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 389, "start_state": {"Stack1": [8, 5], "Stack2": [2, 3, 13], "Stack3": [15, 9, 7], "Stack4": [4], "Stack5": [12, 6, 1, 11, 14, 10]}, "goal_state": {"Stack1": [9, 5, 2], "Stack2": [8, 7, 15], "Stack3": [13], "Stack4": [1, 11, 12, 14, 3, 4], "Stack5": [6, 10]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 390, "start_state": {"Stack1": [15, 12], "Stack2": [1, 11], "Stack3": [3, 13, 10], "Stack4": [8, 5, 6], "Stack5": [7, 2, 14, 4, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 391, "start_state": {"Stack1": [9], "Stack2": [15, 1, 14, 6], "Stack3": [3, 13, 5, 4, 10], "Stack4": [2], "Stack5": [12, 8, 11, 7]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack4", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 392, "start_state": {"Stack1": [2, 5, 8, 14, 12, 7, 15], "Stack2": [3], "Stack3": [6, 11, 1], "Stack4": [13], "Stack5": [4, 10, 9]}, "goal_state": {"Stack1": [4, 10, 11], "Stack2": [5, 13, 15], "Stack3": [3, 7, 9], "Stack4": [1, 6, 8], "Stack5": [2, 12, 14]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 393, "start_state": {"Stack1": [9, 8, 11], "Stack2": [6, 2, 3, 4], "Stack3": [12], "Stack4": [7, 14, 1, 13, 5], "Stack5": [15, 10]}, "goal_state": {"Stack1": [3, 4, 14], "Stack2": [6, 8, 13], "Stack3": [9, 11, 12], "Stack4": [1, 5, 7], "Stack5": [2, 10, 15]}, "fix_order": ["Stack5", "Stack3", "Stack4", "Stack2", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 394, "start_state": {"Stack1": [11, 5, 1], "Stack2": [14, 13, 8, 6], "Stack3": [7], "Stack4": [9, 10, 4, 3], "Stack5": [15, 2, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 395, "start_state": {"Stack1": [4, 13, 3, 9], "Stack2": [1, 12], "Stack3": [6, 10, 7, 5], "Stack4": [8, 14, 11, 2], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 396, "start_state": {"Stack1": [2], "Stack2": [7, 11, 13, 1, 15], "Stack3": [9], "Stack4": [10, 12, 8], "Stack5": [5, 4, 6, 14, 3]}, "goal_state": {"Stack1": [2, 3, 9], "Stack2": [5, 12, 14], "Stack3": [8, 11, 13], "Stack4": [4, 10, 15], "Stack5": [1, 6, 7]}, "fix_order": ["Stack2", "Stack1", "Stack3", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 397, "start_state": {"Stack1": [13, 3, 8], "Stack2": [5, 11, 12], "Stack3": [6, 2, 1, 9, 14], "Stack4": [10, 4, 7], "Stack5": [15]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack1", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 398, "start_state": {"Stack1": [15, 12], "Stack2": [13, 11, 2, 10], "Stack3": [3, 14, 7, 6], "Stack4": [9], "Stack5": [4, 5, 1, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 399, "start_state": {"Stack1": [12, 5], "Stack2": [3, 14, 13], "Stack3": [8], "Stack4": [2, 10, 4, 7, 11, 6], "Stack5": [15, 1, 9]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack5", "Stack4"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 400, "start_state": {"Stack1": [2, 3, 9], "Stack2": [5, 13], "Stack3": [1, 11, 8, 7], "Stack4": [6, 15, 10], "Stack5": [12, 4, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [13, 4, 12, 9, 8, 6, 7, 15, 5, 3, 2, 11, 14, 1, 10], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 401, "start_state": {"Stack1": [8, 15], "Stack2": [9, 13, 5, 4], "Stack3": [7, 12, 14, 6], "Stack4": [11, 3], "Stack5": [1, 2, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [2, 8, 9, 1, 13, 12, 5, 6, 11, 4, 14, 7, 15, 10, 3], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 402, "start_state": {"Stack1": [13, 15, 7, 5], "Stack2": [2, 4, 1], "Stack3": [10, 3], "Stack4": [9, 11], "Stack5": [14, 6, 12, 8]}, "goal_state": {"Stack1": [13, 5, 12], "Stack2": [7, 6, 9, 4], "Stack3": [14, 3, 8, 15, 10], "Stack4": [11, 1], "Stack5": [2]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack2", "Stack3"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 403, "start_state": {"Stack1": [11, 13, 7, 12], "Stack2": [5], "Stack3": [15, 3], "Stack4": [10, 1, 9], "Stack5": [6, 8, 4, 2, 14]}, "goal_state": {"Stack1": [4, 7, 11], "Stack2": [9, 10, 12], "Stack3": [2, 6, 15], "Stack4": [1, 5, 8], "Stack5": [3, 13, 14]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 404, "start_state": {"Stack1": [], "Stack2": [10, 1], "Stack3": [13, 4, 2, 11, 8, 9, 7], "Stack4": [3], "Stack5": [14, 5, 12, 15, 6]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 405, "start_state": {"Stack1": [10, 9, 8, 12], "Stack2": [4, 2, 1], "Stack3": [11, 7, 6, 5], "Stack4": [3, 14], "Stack5": [13, 15]}, "goal_state": {"Stack1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 406, "start_state": {"Stack1": [2, 3, 7], "Stack2": [1, 5, 15, 11, 12, 14], "Stack3": [13, 9, 4], "Stack4": [], "Stack5": [6, 8, 10]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 407, "start_state": {"Stack1": [2, 15], "Stack2": [10, 8, 13], "Stack3": [7, 12], "Stack4": [9, 3, 4, 1, 11], "Stack5": [6, 5, 14]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack5", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 408, "start_state": {"Stack1": [7, 3, 14, 5], "Stack2": [4, 15], "Stack3": [], "Stack4": [1, 10, 8, 12, 6], "Stack5": [11, 13, 2, 9]}, "goal_state": {"Stack1": [9, 4], "Stack2": [5, 13, 12], "Stack3": [6, 8], "Stack4": [15, 14, 11], "Stack5": [10, 1, 7, 3, 2]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 409, "start_state": {"Stack1": [9], "Stack2": [12, 7, 10], "Stack3": [14, 13, 11, 6], "Stack4": [8, 1, 2, 5, 4], "Stack5": [15, 3]}, "goal_state": {"Stack1": [6, 9, 13], "Stack2": [4, 5, 7], "Stack3": [1, 14, 15], "Stack4": [8, 10, 11], "Stack5": [2, 3, 12]}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack3", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 410, "start_state": {"Stack1": [10, 3, 5, 4, 2], "Stack2": [1, 6, 15], "Stack3": [7, 12], "Stack4": [14, 9], "Stack5": [11, 8, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack3", "Stack1", "Stack2", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 411, "start_state": {"Stack1": [8, 2, 6, 14], "Stack2": [7, 5, 12], "Stack3": [1, 4, 9, 15], "Stack4": [10, 13], "Stack5": [3, 11]}, "goal_state": {"Stack1": [4, 13, 2], "Stack2": [15, 6, 1], "Stack3": [5, 11], "Stack4": [9, 12, 14], "Stack5": [7, 8, 3, 10]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 412, "start_state": {"Stack1": [11, 6, 14, 9, 12, 3, 2], "Stack2": [8, 15, 10], "Stack3": [], "Stack4": [4, 5, 1, 7], "Stack5": [13]}, "goal_state": {"Stack1": [11, 1], "Stack2": [4, 2, 3, 12], "Stack3": [13, 6], "Stack4": [15, 14, 9, 10], "Stack5": [5, 7, 8]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 413, "start_state": {"Stack1": [12, 1, 5], "Stack2": [10, 8, 14], "Stack3": [4, 15, 7], "Stack4": [6, 9], "Stack5": [13, 11, 2, 3]}, "goal_state": {"Stack1": [], "Stack2": [7, 5, 9, 6, 3, 15, 11, 14, 4, 2, 1, 8, 10, 12, 13], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 414, "start_state": {"Stack1": [9, 4, 7], "Stack2": [10, 6, 3], "Stack3": [11, 1], "Stack4": [12, 8, 14], "Stack5": [15, 2, 13, 5]}, "goal_state": {"Stack1": [13, 6, 2, 12, 5, 8, 11, 9, 3, 4, 14, 1, 10, 15, 7], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack1", "Stack5", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 415, "start_state": {"Stack1": [3], "Stack2": [14, 12], "Stack3": [7, 8, 13, 11, 15, 1, 4], "Stack4": [6, 9, 5], "Stack5": [10, 2]}, "goal_state": {"Stack1": [11, 4, 15, 6, 10, 12, 9, 2, 1, 7, 8, 5, 13, 14, 3], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 416, "start_state": {"Stack1": [6, 10, 12, 13, 7, 2], "Stack2": [14, 4], "Stack3": [3, 5], "Stack4": [1, 8], "Stack5": [11, 15, 9]}, "goal_state": {"Stack1": [10, 1, 2, 8], "Stack2": [13, 6, 14, 4], "Stack3": [9, 11], "Stack4": [5, 15, 12], "Stack5": [3, 7]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 417, "start_state": {"Stack1": [10, 7], "Stack2": [3, 6, 5], "Stack3": [9, 14], "Stack4": [13, 11, 2, 1, 15], "Stack5": [4, 12, 8]}, "goal_state": {"Stack1": [13, 4, 5, 10, 11, 14], "Stack2": [15], "Stack3": [2], "Stack4": [9, 1, 12, 3], "Stack5": [6, 7, 8]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 418, "start_state": {"Stack1": [8, 10], "Stack2": [4], "Stack3": [6, 3], "Stack4": [5, 15, 9], "Stack5": [14, 12, 7, 11, 1, 2, 13]}, "goal_state": {"Stack1": [12, 5], "Stack2": [3, 15, 10, 8], "Stack3": [13, 1, 11], "Stack4": [9, 4, 14, 7], "Stack5": [6, 2]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 419, "start_state": {"Stack1": [7, 13, 11, 9, 12], "Stack2": [6, 3, 1, 10], "Stack3": [], "Stack4": [8, 4, 5, 15], "Stack5": [2, 14]}, "goal_state": {"Stack1": [7, 3, 9, 15, 11], "Stack2": [6], "Stack3": [12, 1], "Stack4": [4, 8], "Stack5": [10, 2, 5, 13, 14]}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 420, "start_state": {"Stack1": [12, 13, 5, 2], "Stack2": [1, 14, 3], "Stack3": [7, 6], "Stack4": [4, 15, 8, 9], "Stack5": [10, 11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack3", "Stack1", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 421, "start_state": {"Stack1": [7, 4, 5, 11, 3], "Stack2": [14], "Stack3": [2, 13, 10, 15], "Stack4": [12, 9], "Stack5": [1, 6, 8]}, "goal_state": {"Stack1": [14, 2, 1, 7, 10, 6, 13, 12, 5, 15, 3, 4, 11, 9, 8], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 422, "start_state": {"Stack1": [3, 13, 15], "Stack2": [4, 9, 10, 1], "Stack3": [14, 5, 11], "Stack4": [8, 2, 7], "Stack5": [6, 12]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack4", "Stack5", "Stack1", "Stack2", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 423, "start_state": {"Stack1": [11, 10, 2, 13, 4, 14], "Stack2": [12, 9], "Stack3": [1, 5], "Stack4": [15], "Stack5": [6, 8, 3, 7]}, "goal_state": {"Stack1": [5, 10, 13], "Stack2": [1, 6, 15], "Stack3": [8, 9, 12], "Stack4": [2, 4, 7], "Stack5": [3, 11, 14]}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 424, "start_state": {"Stack1": [10, 4, 3, 2], "Stack2": [12], "Stack3": [13, 14, 11], "Stack4": [5, 6, 7, 15, 9], "Stack5": [8, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 425, "start_state": {"Stack1": [6], "Stack2": [2, 10, 9], "Stack3": [4, 14], "Stack4": [13, 3, 7], "Stack5": [5, 11, 8, 1, 12, 15]}, "goal_state": {"Stack1": [2, 4, 11], "Stack2": [1, 3, 6], "Stack3": [13, 14, 15], "Stack4": [5, 8, 9], "Stack5": [7, 10, 12]}, "fix_order": ["Stack2", "Stack5", "Stack4", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 426, "start_state": {"Stack1": [7, 6, 4], "Stack2": [2, 11, 3], "Stack3": [12], "Stack4": [5, 9, 14, 15, 10], "Stack5": [13, 8, 1]}, "goal_state": {"Stack1": [13, 14, 15], "Stack2": [7, 8, 11], "Stack3": [1, 4, 12], "Stack4": [5, 6, 10], "Stack5": [2, 3, 9]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 427, "start_state": {"Stack1": [2, 7], "Stack2": [15], "Stack3": [10, 14, 6, 4, 13, 3], "Stack4": [8, 11, 9, 1], "Stack5": [12, 5]}, "goal_state": {"Stack1": [], "Stack2": [5, 11, 2, 8, 6, 14, 12, 15, 3, 7, 1, 10, 9, 13, 4], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 428, "start_state": {"Stack1": [2], "Stack2": [5, 3, 14, 6], "Stack3": [9, 12, 11], "Stack4": [8, 13, 15, 4, 10, 1], "Stack5": [7]}, "goal_state": {"Stack1": [10, 7], "Stack2": [13, 15, 2], "Stack3": [9], "Stack4": [1, 8, 11, 14, 5], "Stack5": [4, 12, 6, 3]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 429, "start_state": {"Stack1": [10, 8], "Stack2": [6, 11, 2, 7], "Stack3": [4, 12, 15], "Stack4": [9, 3, 1], "Stack5": [14, 5, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [10, 4, 7, 13, 1, 2, 8, 12, 9, 5, 15, 3, 11, 14, 6]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 430, "start_state": {"Stack1": [6], "Stack2": [4, 13, 15, 11, 1], "Stack3": [2], "Stack4": [9, 8, 12, 5, 7], "Stack5": [10, 3, 14]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 431, "start_state": {"Stack1": [12, 6], "Stack2": [11, 1, 2, 9], "Stack3": [4, 5, 3, 15], "Stack4": [10], "Stack5": [8, 7, 13, 14]}, "goal_state": {"Stack1": [10, 15], "Stack2": [8, 11], "Stack3": [3, 2, 1, 5, 4, 14, 6], "Stack4": [12, 9], "Stack5": [7, 13]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 432, "start_state": {"Stack1": [3], "Stack2": [15, 7], "Stack3": [4], "Stack4": [5, 2, 14], "Stack5": [11, 1, 13, 10, 8, 12, 6, 9]}, "goal_state": {"Stack1": [8, 11, 14], "Stack2": [2, 7, 15], "Stack3": [4, 5, 6], "Stack4": [1, 10, 12], "Stack5": [3, 9, 13]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 433, "start_state": {"Stack1": [5, 9, 13], "Stack2": [6, 11, 3, 7, 4], "Stack3": [], "Stack4": [14, 2, 1], "Stack5": [15, 8, 12, 10]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 434, "start_state": {"Stack1": [5, 6, 10, 1, 2, 3], "Stack2": [], "Stack3": [], "Stack4": [8, 7, 9, 12, 13], "Stack5": [11, 4, 14, 15]}, "goal_state": {"Stack1": [], "Stack2": [8, 9, 14, 10, 12, 15, 3, 11, 4, 1, 5, 2, 13, 7, 6], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack2", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 435, "start_state": {"Stack1": [3, 1, 14, 10], "Stack2": [], "Stack3": [13, 11, 15, 2], "Stack4": [9, 8, 7, 5, 6, 4], "Stack5": [12]}, "goal_state": {"Stack1": [4, 10, 12, 14, 3, 8, 13, 9, 15, 5, 6, 7, 11, 1, 2], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 436, "start_state": {"Stack1": [10], "Stack2": [15, 14], "Stack3": [3], "Stack4": [4, 12, 9, 8], "Stack5": [11, 2, 5, 7, 6, 1, 13]}, "goal_state": {"Stack1": [3, 13], "Stack2": [14, 2], "Stack3": [11, 6, 10], "Stack4": [1, 8, 12, 7, 9], "Stack5": [4, 5, 15]}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 437, "start_state": {"Stack1": [11, 8, 2], "Stack2": [5], "Stack3": [13, 1, 4, 9, 3], "Stack4": [10, 6, 12, 7], "Stack5": [15, 14]}, "goal_state": {"Stack1": [1, 4, 11], "Stack2": [6, 12, 14], "Stack3": [2, 9, 13], "Stack4": [7, 10, 15], "Stack5": [3, 5, 8]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 438, "start_state": {"Stack1": [7, 4, 13], "Stack2": [10, 3, 5], "Stack3": [11, 15, 12, 2], "Stack4": [1, 6], "Stack5": [9, 8, 14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [5, 13, 8, 4, 3, 6, 9, 7, 2, 11, 15, 10, 12, 1, 14], "Stack5": []}, "fix_order": ["Stack5", "Stack3", "Stack1", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 439, "start_state": {"Stack1": [6, 9, 5], "Stack2": [4, 15, 13, 11], "Stack3": [1, 14], "Stack4": [10, 12, 3], "Stack5": [7, 2, 8]}, "goal_state": {"Stack1": [14, 7, 4, 8, 10, 3, 5, 12, 2, 15, 11, 9, 6, 13, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack3", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 440, "start_state": {"Stack1": [3, 5, 7, 4], "Stack2": [], "Stack3": [15, 8, 1, 2], "Stack4": [10, 9, 13], "Stack5": [11, 6, 14, 12]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack5", "Stack4", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 441, "start_state": {"Stack1": [13, 4, 9, 3], "Stack2": [8, 1, 6], "Stack3": [11, 5, 10, 7], "Stack4": [2], "Stack5": [12, 15, 14]}, "goal_state": {"Stack1": [], "Stack2": [10, 9, 1, 7, 13, 15, 14, 12, 6, 4, 5, 8, 2, 3, 11], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 442, "start_state": {"Stack1": [4, 12], "Stack2": [13, 5, 6], "Stack3": [10, 11, 8, 14, 9], "Stack4": [2, 3, 1, 7], "Stack5": [15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 443, "start_state": {"Stack1": [10, 9, 8, 1, 11, 4, 2], "Stack2": [5], "Stack3": [14], "Stack4": [13, 3, 15], "Stack5": [12, 7, 6]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 444, "start_state": {"Stack1": [9, 4, 2, 3, 15, 11], "Stack2": [8, 10, 5, 1], "Stack3": [14, 12], "Stack4": [6, 7], "Stack5": [13]}, "goal_state": {"Stack1": [12, 9, 8, 6], "Stack2": [7, 13, 3], "Stack3": [14], "Stack4": [1, 10, 5, 4], "Stack5": [11, 15, 2]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 445, "start_state": {"Stack1": [14, 3, 5, 1, 15], "Stack2": [9, 6, 7], "Stack3": [12, 8, 4], "Stack4": [13], "Stack5": [10, 2, 11]}, "goal_state": {"Stack1": [1, 14, 4, 2, 10], "Stack2": [7, 8], "Stack3": [12, 3], "Stack4": [13, 6, 11, 5], "Stack5": [15, 9]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack1", "Stack5"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 446, "start_state": {"Stack1": [15, 10], "Stack2": [3, 8, 4, 9], "Stack3": [11, 13, 12, 1], "Stack4": [14, 7], "Stack5": [5, 6, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [15, 9, 2, 5, 10, 8, 11, 14, 4, 1, 12, 6, 3, 13, 7], "Stack5": []}, "fix_order": ["Stack5", "Stack1", "Stack2", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 447, "start_state": {"Stack1": [1, 10, 11, 12, 8, 5, 15], "Stack2": [4, 6, 7], "Stack3": [13, 14, 3], "Stack4": [2], "Stack5": [9]}, "goal_state": {"Stack1": [8, 13, 14], "Stack2": [6, 11, 12], "Stack3": [2, 3, 10], "Stack4": [1, 4, 5], "Stack5": [7, 9, 15]}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 448, "start_state": {"Stack1": [1, 14], "Stack2": [12, 2, 5, 13, 11, 15], "Stack3": [3, 10, 6], "Stack4": [4, 7, 9], "Stack5": [8]}, "goal_state": {"Stack1": [4, 3, 14], "Stack2": [15, 7, 9, 12, 1], "Stack3": [6, 8], "Stack4": [2, 13], "Stack5": [11, 10, 5]}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 449, "start_state": {"Stack1": [9, 7, 2], "Stack2": [5, 6], "Stack3": [1, 10], "Stack4": [14, 3, 15, 11], "Stack5": [8, 4, 13, 12]}, "goal_state": {"Stack1": [1, 13, 15], "Stack2": [5, 6, 11], "Stack3": [3, 8, 12], "Stack4": [2, 7, 9], "Stack5": [4, 10, 14]}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 450, "start_state": {"Stack1": [8, 5, 4], "Stack2": [2, 6, 3], "Stack3": [10, 13, 7, 15], "Stack4": [11, 9], "Stack5": [12, 14, 1]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 451, "start_state": {"Stack1": [8, 6, 4, 1], "Stack2": [2, 15, 7, 5], "Stack3": [14, 11, 13], "Stack4": [9, 3, 12], "Stack5": [10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack4", "Stack2", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 452, "start_state": {"Stack1": [5, 11, 10, 7], "Stack2": [4, 2, 8], "Stack3": [13, 6], "Stack4": [15, 3, 14], "Stack5": [1, 9, 12]}, "goal_state": {"Stack1": [4, 8, 12], "Stack2": [1, 6, 13], "Stack3": [3, 9, 15], "Stack4": [7, 10, 11], "Stack5": [2, 5, 14]}, "fix_order": ["Stack4", "Stack3", "Stack2", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 453, "start_state": {"Stack1": [3, 12], "Stack2": [7], "Stack3": [15, 5, 10, 11, 14, 4, 6], "Stack4": [1, 8, 13], "Stack5": [2, 9]}, "goal_state": {"Stack1": [3, 15, 5], "Stack2": [11, 14, 6, 10, 12], "Stack3": [13, 1, 7], "Stack4": [9, 2, 4], "Stack5": [8]}, "fix_order": ["Stack4", "Stack1", "Stack2", "Stack5", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 454, "start_state": {"Stack1": [10], "Stack2": [4, 15, 12, 1, 9, 11], "Stack3": [3, 8, 13, 2], "Stack4": [5], "Stack5": [7, 6, 14]}, "goal_state": {"Stack1": [], "Stack2": [5, 8, 13, 12, 7, 3, 2, 10, 4, 9, 14, 1, 11, 6, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 455, "start_state": {"Stack1": [], "Stack2": [4, 5], "Stack3": [15, 13, 3, 1, 9], "Stack4": [10, 12, 6, 8, 11, 14], "Stack5": [2, 7]}, "goal_state": {"Stack1": [6, 10, 11], "Stack2": [8, 12, 14], "Stack3": [1, 3, 5], "Stack4": [2, 4, 15], "Stack5": [7, 9, 13]}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 456, "start_state": {"Stack1": [7, 6], "Stack2": [4, 3, 5, 10], "Stack3": [13, 9, 14], "Stack4": [8, 11, 12, 2], "Stack5": [1, 15]}, "goal_state": {"Stack1": [9, 4, 5], "Stack2": [14, 12, 2, 3], "Stack3": [6, 13], "Stack4": [10, 11, 15, 7], "Stack5": [8, 1]}, "fix_order": ["Stack2", "Stack4", "Stack3", "Stack5", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 457, "start_state": {"Stack1": [6, 9], "Stack2": [14, 11, 5], "Stack3": [7, 1, 4, 2, 12], "Stack4": [15, 10, 3, 8], "Stack5": [13]}, "goal_state": {"Stack1": [12, 11], "Stack2": [14, 2, 13, 15], "Stack3": [5, 9], "Stack4": [4, 6, 7, 1, 3], "Stack5": [10, 8]}, "fix_order": ["Stack2", "Stack4", "Stack5", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 458, "start_state": {"Stack1": [14, 6, 11], "Stack2": [9, 4, 7, 1, 3], "Stack3": [10, 2, 15], "Stack4": [5, 8, 12], "Stack5": [13]}, "goal_state": {"Stack1": [15, 12], "Stack2": [3, 7, 13], "Stack3": [5, 14, 9], "Stack4": [6, 8, 2], "Stack5": [1, 11, 4, 10]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 459, "start_state": {"Stack1": [9], "Stack2": [11, 14, 13, 10, 6], "Stack3": [7, 2, 4], "Stack4": [15, 12, 3], "Stack5": [5, 1, 8]}, "goal_state": {"Stack1": [11, 5, 7, 8, 1, 9], "Stack2": [14, 12, 13], "Stack3": [3], "Stack4": [4, 2, 6], "Stack5": [10, 15]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 460, "start_state": {"Stack1": [11], "Stack2": [5, 7, 2, 3, 15, 13, 4, 9], "Stack3": [10, 12], "Stack4": [1, 8, 6], "Stack5": [14]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack4", "Stack2", "Stack1", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 461, "start_state": {"Stack1": [15, 6, 13, 3], "Stack2": [8, 7, 4], "Stack3": [5, 10, 9], "Stack4": [2, 1, 12, 11], "Stack5": [14]}, "goal_state": {"Stack1": [2, 13, 11], "Stack2": [9, 7, 15, 14], "Stack3": [10, 1], "Stack4": [3, 5, 6, 4], "Stack5": [12, 8]}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 462, "start_state": {"Stack1": [10, 11, 15, 12, 2], "Stack2": [], "Stack3": [14, 9, 13, 1, 5], "Stack4": [4, 8], "Stack5": [3, 6, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack1", "Stack4", "Stack5", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 463, "start_state": {"Stack1": [5, 15, 2, 14], "Stack2": [4, 9, 12, 1], "Stack3": [10], "Stack4": [7, 3, 6], "Stack5": [11, 8, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [6, 3, 5, 4, 12, 14, 2, 13, 9, 15, 1, 7, 8, 11, 10]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 464, "start_state": {"Stack1": [9, 14, 8], "Stack2": [2, 15, 5, 11], "Stack3": [10, 12, 13, 7], "Stack4": [1], "Stack5": [4, 6, 3]}, "goal_state": {"Stack1": [8, 12, 14], "Stack2": [5, 7, 9], "Stack3": [1, 2, 15], "Stack4": [3, 4, 6], "Stack5": [10, 11, 13]}, "fix_order": ["Stack1", "Stack5", "Stack2", "Stack4", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 465, "start_state": {"Stack1": [8], "Stack2": [10], "Stack3": [7, 2, 15], "Stack4": [6, 13, 4, 9, 12, 11, 5], "Stack5": [3, 14, 1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [13, 15, 10, 8, 2, 9, 3, 4, 7, 11, 14, 6, 5, 1, 12], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack4", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 466, "start_state": {"Stack1": [12, 4, 6], "Stack2": [9, 11, 13, 10], "Stack3": [1, 5, 14], "Stack4": [15, 8, 3], "Stack5": [7, 2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 8, 14, 7, 3, 15, 11, 13, 12, 9, 5, 10, 6, 2, 4], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack5", "Stack3", "Stack2"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 467, "start_state": {"Stack1": [15], "Stack2": [1, 5, 14], "Stack3": [7, 6, 13, 10, 11], "Stack4": [9, 2], "Stack5": [3, 8, 4, 12]}, "goal_state": {"Stack1": [2, 5, 4, 9, 10], "Stack2": [12, 13, 7, 6], "Stack3": [15], "Stack4": [14, 3, 1], "Stack5": [11, 8]}, "fix_order": ["Stack5", "Stack3", "Stack2", "Stack4", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 468, "start_state": {"Stack1": [13, 1, 5, 15], "Stack2": [8, 4, 9], "Stack3": [2, 10, 12], "Stack4": [3, 7], "Stack5": [11, 6, 14]}, "goal_state": {"Stack1": [5, 4, 2], "Stack2": [10, 3], "Stack3": [1, 11], "Stack4": [13, 8, 15], "Stack5": [7, 9, 14, 6, 12]}, "fix_order": ["Stack5", "Stack2", "Stack4", "Stack3", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 469, "start_state": {"Stack1": [1], "Stack2": [8, 11, 2, 15, 13, 10, 4], "Stack3": [12, 7, 14, 5], "Stack4": [3, 6, 9], "Stack5": []}, "goal_state": {"Stack1": [15, 4, 1], "Stack2": [14, 12, 5], "Stack3": [3, 9], "Stack4": [6, 7, 2, 11], "Stack5": [13, 10, 8]}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack1", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 470, "start_state": {"Stack1": [4], "Stack2": [2, 13, 12, 3, 14, 15, 1], "Stack3": [7, 9], "Stack4": [11, 8, 10], "Stack5": [6, 5]}, "goal_state": {"Stack1": [14, 4, 2, 3], "Stack2": [8], "Stack3": [5, 6], "Stack4": [13, 11, 15, 9, 10, 7, 1], "Stack5": [12]}, "fix_order": ["Stack1", "Stack3", "Stack5", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 471, "start_state": {"Stack1": [7, 15, 6, 13], "Stack2": [12, 9, 8, 2, 5], "Stack3": [11, 1], "Stack4": [10, 4], "Stack5": [3, 14]}, "goal_state": {"Stack1": [6, 15, 13], "Stack2": [7, 8], "Stack3": [2, 5, 14], "Stack4": [9, 10, 1, 11], "Stack5": [4, 3, 12]}, "fix_order": ["Stack4", "Stack5", "Stack2", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 472, "start_state": {"Stack1": [13], "Stack2": [8, 14, 9, 11], "Stack3": [2, 10, 15], "Stack4": [1, 5, 4], "Stack5": [7, 6, 12, 3]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [8, 6, 10, 4, 5, 7, 1, 9, 11, 12, 14, 3, 15, 2, 13]}, "fix_order": ["Stack1", "Stack5", "Stack4", "Stack3", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 473, "start_state": {"Stack1": [9, 10, 13], "Stack2": [6, 2], "Stack3": [5, 14, 8, 1], "Stack4": [12], "Stack5": [15, 3, 4, 7, 11]}, "goal_state": {"Stack1": [9, 2], "Stack2": [11, 12, 10, 3], "Stack3": [6, 5, 7], "Stack4": [14, 13, 4, 15], "Stack5": [1, 8]}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack1", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 474, "start_state": {"Stack1": [2, 3, 1], "Stack2": [10, 14], "Stack3": [6, 12, 13], "Stack4": [15, 9, 5, 4], "Stack5": [7, 11, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 5, 15, 8, 9, 2, 10, 6, 14, 4, 11, 13, 7, 3, 12]}, "fix_order": ["Stack2", "Stack5", "Stack1", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 475, "start_state": {"Stack1": [4, 13, 2, 10], "Stack2": [9, 11, 3, 6], "Stack3": [14, 12, 7], "Stack4": [1], "Stack5": [8, 5, 15]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [3, 5, 1, 9, 2, 6, 8, 15, 14, 4, 7, 10, 12, 11, 13], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 476, "start_state": {"Stack1": [12, 13, 14, 10, 11], "Stack2": [8, 15, 1, 9, 5], "Stack3": [3, 6, 2], "Stack4": [], "Stack5": [4, 7]}, "goal_state": {"Stack1": [1, 8, 14], "Stack2": [4, 10, 13], "Stack3": [5, 7, 11], "Stack4": [3, 6, 9], "Stack5": [2, 12, 15]}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack1", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 477, "start_state": {"Stack1": [], "Stack2": [11], "Stack3": [3, 15, 14, 5], "Stack4": [2, 13, 6, 7, 1, 9], "Stack5": [8, 4, 12, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack2", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 478, "start_state": {"Stack1": [8, 9, 2, 3], "Stack2": [5, 1], "Stack3": [4, 6, 15], "Stack4": [7, 11], "Stack5": [12, 13, 14, 10]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack1", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 479, "start_state": {"Stack1": [7, 6, 5, 3, 11], "Stack2": [14, 10, 8, 2, 9], "Stack3": [15, 13], "Stack4": [12], "Stack5": [1, 4]}, "goal_state": {"Stack1": [15, 8, 7, 10, 2, 12, 14, 1, 11, 9, 4, 5, 3, 13, 6], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 480, "start_state": {"Stack1": [6, 4, 9], "Stack2": [1, 10, 3, 7], "Stack3": [14], "Stack4": [11, 5, 8, 15, 12], "Stack5": [2, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [10, 8, 12, 6, 15, 7, 11, 9, 3, 4, 14, 1, 2, 13, 5]}, "fix_order": ["Stack5", "Stack1", "Stack4", "Stack2", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 481, "start_state": {"Stack1": [3, 11, 14], "Stack2": [2, 15, 1], "Stack3": [4], "Stack4": [12, 8, 9], "Stack5": [5, 10, 13, 7, 6]}, "goal_state": {"Stack1": [1, 5], "Stack2": [13, 11, 4, 8], "Stack3": [15, 6], "Stack4": [14, 7, 10, 2, 9], "Stack5": [3, 12]}, "fix_order": ["Stack2", "Stack1", "Stack4", "Stack3", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 482, "start_state": {"Stack1": [1, 13], "Stack2": [14, 5], "Stack3": [12, 9], "Stack4": [11, 15, 10, 8, 6], "Stack5": [2, 7, 4, 3]}, "goal_state": {"Stack1": [12, 2, 6, 3, 4, 11, 5, 9, 1, 14, 7, 15, 10, 8, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 483, "start_state": {"Stack1": [14, 2], "Stack2": [8], "Stack3": [3, 10, 5, 7], "Stack4": [13, 12], "Stack5": [15, 6, 11, 1, 4, 9]}, "goal_state": {"Stack1": [14, 6, 4, 5, 8, 2, 15, 1, 7, 9, 12, 11, 3, 10, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack5", "Stack1", "Stack4", "Stack2"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 484, "start_state": {"Stack1": [12, 3], "Stack2": [2, 8, 5], "Stack3": [13, 1], "Stack4": [11], "Stack5": [4, 6, 15, 14, 10, 9, 7]}, "goal_state": {"Stack1": [10, 11, 14], "Stack2": [3, 13, 15], "Stack3": [1, 4, 7], "Stack4": [2, 6, 9], "Stack5": [5, 8, 12]}, "fix_order": ["Stack3", "Stack4", "Stack5", "Stack2", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 485, "start_state": {"Stack1": [6, 11, 13], "Stack2": [10], "Stack3": [8], "Stack4": [15, 3, 1, 14, 12], "Stack5": [5, 9, 4, 2, 7]}, "goal_state": {"Stack1": [8, 1], "Stack2": [10, 4], "Stack3": [9, 6, 3, 15, 7], "Stack4": [5, 13, 11], "Stack5": [14, 2, 12]}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack4", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 486, "start_state": {"Stack1": [8, 9, 6], "Stack2": [2, 4, 15, 7, 1], "Stack3": [12, 13, 5, 14], "Stack4": [10, 3], "Stack5": [11]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [12, 14, 1, 2, 3, 15, 11, 4, 10, 6, 13, 9, 5, 7, 8], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack1", "Stack4", "Stack2", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 487, "start_state": {"Stack1": [7, 11], "Stack2": [12, 15, 1, 4, 5], "Stack3": [2], "Stack4": [9, 13, 10, 6, 3], "Stack5": [14, 8]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack1", "Stack2", "Stack4", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 488, "start_state": {"Stack1": [8, 14, 12, 10, 3], "Stack2": [], "Stack3": [4, 1, 5, 6, 9], "Stack4": [7, 2], "Stack5": [11, 15, 13]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack3", "Stack2", "Stack5", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 489, "start_state": {"Stack1": [9, 6], "Stack2": [15, 11, 4], "Stack3": [2, 7, 3, 12, 13], "Stack4": [10, 8], "Stack5": [1, 5, 14]}, "goal_state": {"Stack1": [3, 6, 10], "Stack2": [5, 8, 15], "Stack3": [7, 11, 12], "Stack4": [2, 9, 13], "Stack5": [1, 4, 14]}, "fix_order": ["Stack5", "Stack2", "Stack3", "Stack1", "Stack4"], "difficulty": 12, "num_stacks": 5, "num_blocks": 15}, {"id": 490, "start_state": {"Stack1": [7, 4], "Stack2": [10, 11, 2, 3], "Stack3": [12, 1, 8, 6, 14], "Stack4": [5, 13, 15], "Stack5": [9]}, "goal_state": {"Stack1": [11, 15], "Stack2": [12, 4, 9, 2], "Stack3": [1, 10], "Stack4": [5, 13, 3, 7], "Stack5": [14, 8, 6]}, "fix_order": ["Stack1", "Stack5", "Stack3", "Stack2", "Stack4"], "difficulty": 13, "num_stacks": 5, "num_blocks": 15}, {"id": 491, "start_state": {"Stack1": [13, 1, 6], "Stack2": [2, 8, 9], "Stack3": [10, 15], "Stack4": [4, 11], "Stack5": [3, 12, 5, 14, 7]}, "goal_state": {"Stack1": [], "Stack2": [1, 13, 6, 2, 4, 3, 10, 8, 12, 15, 9, 14, 7, 5, 11], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack5", "Stack3", "Stack4"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 492, "start_state": {"Stack1": [15, 10], "Stack2": [7, 4], "Stack3": [6, 2, 9, 13], "Stack4": [11, 1], "Stack5": [8, 5, 12, 14, 3]}, "goal_state": {"Stack1": [12, 10, 9, 2, 6, 1, 14, 11, 3, 8, 7, 4, 15, 5, 13], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack5", "Stack4", "Stack3", "Stack2", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 493, "start_state": {"Stack1": [15, 1, 4], "Stack2": [3, 2], "Stack3": [10, 12], "Stack4": [5, 14, 7, 13], "Stack5": [6, 11, 8, 9]}, "goal_state": {"Stack1": [], "Stack2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack4", "Stack2", "Stack5", "Stack1", "Stack3"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 494, "start_state": {"Stack1": [7, 11, 13], "Stack2": [3, 2, 6, 14], "Stack3": [1, 9, 12], "Stack4": [8], "Stack5": [5, 10, 4, 15]}, "goal_state": {"Stack1": [3, 13, 12], "Stack2": [2, 6, 14, 1], "Stack3": [15, 5, 9, 4], "Stack4": [8, 10, 7], "Stack5": [11]}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 495, "start_state": {"Stack1": [12, 11, 2], "Stack2": [4, 13], "Stack3": [10, 15, 5, 14, 8], "Stack4": [9, 6, 3, 7], "Stack5": [1]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack4", "Stack1", "Stack5", "Stack3", "Stack2"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 496, "start_state": {"Stack1": [9, 1, 2], "Stack2": [6, 14, 5], "Stack3": [10, 4], "Stack4": [8, 13, 11, 3], "Stack5": [12, 15, 7]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [8, 9, 13, 3, 4, 6, 15, 10, 1, 14, 7, 11, 12, 2, 5], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack4", "Stack2", "Stack5", "Stack3"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 497, "start_state": {"Stack1": [13, 15], "Stack2": [5, 7, 1], "Stack3": [12, 10, 9], "Stack4": [3, 14, 8, 4], "Stack5": [11, 2, 6]}, "goal_state": {"Stack1": [], "Stack2": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack2", "Stack5", "Stack3", "Stack4", "Stack1"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 498, "start_state": {"Stack1": [14, 11, 7], "Stack2": [6, 1, 8], "Stack3": [9, 13, 4, 12, 15], "Stack4": [3, 10, 5], "Stack5": [2]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]}, "fix_order": ["Stack3", "Stack2", "Stack4", "Stack1", "Stack5"], "difficulty": 15, "num_stacks": 5, "num_blocks": 15}, {"id": 499, "start_state": {"Stack1": [15], "Stack2": [8, 9, 10, 13], "Stack3": [6, 1, 14], "Stack4": [3, 11, 4, 12, 5], "Stack5": [2, 7]}, "goal_state": {"Stack1": [7, 13, 1], "Stack2": [3, 9, 14, 12, 4], "Stack3": [8, 6, 2, 10], "Stack4": [11, 5], "Stack5": [15]}, "fix_order": ["Stack4", "Stack2", "Stack3", "Stack5", "Stack1"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}, {"id": 500, "start_state": {"Stack1": [6, 14, 1], "Stack2": [9, 13, 3, 15], "Stack3": [10, 7, 5, 8], "Stack4": [2], "Stack5": [4, 12, 11]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack3", "Stack2", "Stack1", "Stack4", "Stack5"], "difficulty": 14, "num_stacks": 5, "num_blocks": 15}]