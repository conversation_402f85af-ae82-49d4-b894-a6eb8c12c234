#!/usr/bin/env python3
"""
CNN-Transformer模型指导A*测试脚本
测试训练好的CNN-Transformer模型在A*搜索中的指导效果
"""

import os
import sys
import time
import re
import ast

# --- 环境设置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

# --- 模块导入 ---
try:
    from pre_marshalling import GraphPlanningBlocksWorld
    print("成功导入 GraphPlanningBlocksWorld")
except ImportError as e:
    print(f"导入A*规划器失败: {e}")
    sys.exit(1)

try:
    from cnn_transformer_guidance import CNNTransformerGuidance
    CNN_GUIDANCE_AVAILABLE = True
    print("成功导入 CNNTransformerGuidance")
except ImportError as e:
    CNN_GUIDANCE_AVAILABLE = False
    print(f"警告: 导入CNN-Transformer指导模块失败: {e}")

# --- 辅助函数 ---
def parse_instance_file(file_path):
    """解析实例文件"""
    data = {}
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            start_state_match = re.search(r"Start State:\s*(\{.*\})", content)
            if start_state_match: 
                data['start_state'] = ast.literal_eval(start_state_match.group(1))
            g_canonical_match = re.search(r"G_canonical:\s*(\{.*\})", content)
            if g_canonical_match: 
                data['g_canonical'] = ast.literal_eval(g_canonical_match.group(1))
            fix_order_match = re.search(r"Fix Order:\s*(\[.*\])", content)
            if fix_order_match: 
                data['fix_order'] = ast.literal_eval(fix_order_match.group(1))
        return data
    except Exception as e:
        print(f"解析实例文件 {file_path} 失败: {e}")
        return None

def run_pure_astar_test(start_state, g_canonical, fix_order):
    """运行纯A*测试作为基准"""
    print("\n" + "=" * 60)
    print("纯A*测试 (基准)")
    print("=" * 60)
    
    log_file = os.path.join(SCRIPT_DIR, "pure_astar_test_log.txt")

    try:
        planner = GraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            log_file=log_file
        )
        print("成功创建规划器实例")
        
        print("开始运行纯A*搜索...")
        start_time = time.time()
        
        solution, nodes_count = planner.a_star_search(llm=None)
        
        duration = time.time() - start_time
        
        print("-" * 60)
        print("纯A*测试结果:")
        print("-" * 60)
        
        if solution:
            print(f"✓ 找到解决方案! (路径长度: {len(solution)} 步)")
        else:
            print("✗ 未找到解决方案")
        
        print(f"  - 搜索节点数: {nodes_count}")
        print(f"  - 耗时: {duration:.4f} 秒")
        
        return solution, nodes_count, duration
        
    except Exception as e:
        print(f"纯A*测试执行失败: {e}")
        return None, 0, 0

def run_cnn_transformer_guided_test(start_state, g_canonical, fix_order, model_path):
    """运行CNN-Transformer指导的A*测试"""
    if not CNN_GUIDANCE_AVAILABLE:
        print("\nCNN-Transformer指导模块不可用，跳过测试。")
        return None, 0, 0
        
    print("\n" + "=" * 60)
    print("CNN-Transformer指导A*测试")
    print("=" * 60)
    
    log_file = os.path.join(SCRIPT_DIR, "cnn_transformer_guided_test_log.txt")

    try:
        # 创建CNN-Transformer指导实例
        guidance_instance = CNNTransformerGuidance(model_path)
        print("成功创建CNN-Transformer指导实例")
        
        planner = GraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=g_canonical,
            fix_order=fix_order,
            log_file=log_file
        )
        print("成功创建规划器实例")
        
        print("开始运行CNN-Transformer指导的A*搜索...")
        start_time = time.time()
        
        solution, nodes_count = planner.a_star_search(llm=guidance_instance)
        
        duration = time.time() - start_time
        
        print("-" * 60)
        print("CNN-Transformer指导测试结果:")
        print("-" * 60)
        
        if solution:
            print(f"✓ 找到解决方案! (路径长度: {len(solution)} 步)")
        else:
            print("✗ 未找到解决方案")
            
        print(f"  - 搜索节点数: {nodes_count}")
        print(f"  - 耗时: {duration:.4f} 秒")
        
        return solution, nodes_count, duration
        
    except Exception as e:
        print(f"CNN-Transformer指导测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0, 0

def compare_results(pure_result, guided_result):
    """比较两种方法的结果"""
    print("\n" + "=" * 60)
    print("性能对比")
    print("=" * 60)
    
    pure_solution, pure_nodes, pure_time = pure_result
    guided_solution, guided_nodes, guided_time = guided_result
    
    print(f"{'指标':<20} {'纯A*':<15} {'CNN-Transformer指导':<20} {'改进':<15}")
    print("-" * 70)
    
    # 解决方案长度对比
    if pure_solution and guided_solution:
        solution_improvement = len(pure_solution) - len(guided_solution)
        print(f"{'解决方案长度':<20} {len(pure_solution):<15} {len(guided_solution):<20} {solution_improvement:+d}")
    elif pure_solution:
        print(f"{'解决方案长度':<20} {len(pure_solution):<15} {'未找到':<20} {'N/A':<15}")
    elif guided_solution:
        print(f"{'解决方案长度':<20} {'未找到':<15} {len(guided_solution):<20} {'找到解!':<15}")
    else:
        print(f"{'解决方案长度':<20} {'未找到':<15} {'未找到':<20} {'N/A':<15}")
    
    # 搜索节点数对比
    if pure_nodes > 0 and guided_nodes > 0:
        nodes_improvement = pure_nodes - guided_nodes
        nodes_ratio = guided_nodes / pure_nodes
        print(f"{'搜索节点数':<20} {pure_nodes:<15} {guided_nodes:<20} {nodes_improvement:+d} ({nodes_ratio:.2%})")
    else:
        print(f"{'搜索节点数':<20} {pure_nodes:<15} {guided_nodes:<20} {'N/A':<15}")
    
    # 时间对比
    if pure_time > 0 and guided_time > 0:
        time_improvement = pure_time - guided_time
        time_ratio = guided_time / pure_time
        print(f"{'耗时 (秒)':<20} {pure_time:.4f:<15} {guided_time:.4f:<20} {time_improvement:+.4f} ({time_ratio:.2%})")
    else:
        print(f"{'耗时 (秒)':<20} {pure_time:.4f:<15} {guided_time:.4f:<20} {'N/A':<15}")

if __name__ == "__main__":
    # --- 配置区 ---
    instance_path = os.path.join(SCRIPT_DIR, "instance_28.txt")
    model_path = os.path.join(SCRIPT_DIR, "models", "cnn_transformer_guidance.pth")
    
    # 测试控制
    RUN_PURE_ASTAR = False      # 是否运行纯A*基准测试
    RUN_CNN_GUIDED = True      # 是否运行CNN-Transformer指导测试
    RUN_COMPARISON = True      # 是否进行性能对比
    
    # --- 执行区 ---
    print("=" * 80)
    print("CNN-Transformer模型指导A*测试")
    print(f"测试实例: {os.path.basename(instance_path)}")
    print(f"模型路径: {model_path}")
    
    test_modes = []
    if RUN_PURE_ASTAR: test_modes.append("纯A*")
    if RUN_CNN_GUIDED: test_modes.append("CNN-Transformer指导")
    print(f"运行模式: {' + '.join(test_modes) if test_modes else '无测试'}")
    print("=" * 80)

    # 检查文件是否存在
    if not os.path.exists(instance_path):
        print(f"错误: 实例文件不存在: {instance_path}")
        sys.exit(1)
        
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        sys.exit(1)

    instance_data = parse_instance_file(instance_path)
    
    if instance_data:
        start_state = instance_data['start_state']
        g_canonical = instance_data['g_canonical']
        fix_order = instance_data['fix_order']

        pure_result = (None, 0, 0)
        guided_result = (None, 0, 0)
        
        # 运行测试
        if RUN_PURE_ASTAR:
            pure_result = run_pure_astar_test(start_state, g_canonical, fix_order)
        
        if RUN_CNN_GUIDED:
            guided_result = run_cnn_transformer_guided_test(start_state, g_canonical, fix_order, model_path)
            
        # 性能对比
        if RUN_COMPARISON and RUN_PURE_ASTAR and RUN_CNN_GUIDED:
            compare_results(pure_result, guided_result)
            
        if not RUN_PURE_ASTAR and not RUN_CNN_GUIDED:
            print("未选择任何测试模式，程序结束。")

    else:
        print("无法加载实例数据，测试终止。")