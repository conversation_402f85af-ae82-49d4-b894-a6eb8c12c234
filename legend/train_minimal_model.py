import os
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from sklearn.utils.class_weight import compute_class_weight

from cnn_transformer_minimal import BlocksWorldDataset, MinimalCNNClassifier, SimpleCNNClassifier

def compute_class_weights(labels, n_classes):
    """计算类别权重来处理数据不平衡"""
    valid_labels = labels[labels != -1]
    
    if len(valid_labels) == 0:
        return torch.ones(n_classes)
    
    unique_classes = np.unique(valid_labels)
    class_weights = compute_class_weight(
        'balanced', 
        classes=unique_classes, 
        y=valid_labels
    )
    
    weights = torch.ones(n_classes)
    for i, cls in enumerate(unique_classes):
        weights[cls] = class_weights[i]
    
    return weights

def infer_shape_from_csv_header(header):
    """从CSV头部推断数据形状"""
    print(f"从优化列名推断形状: {header[0]}")
    
    parts = header[0].split('_')
    if len(parts) == 3:
        try:
            n_layers = int(parts[0]) + 1
            n_rows = int(parts[1]) + 1
            n_cols = int(parts[2]) + 1
            return n_layers, n_rows, n_cols
        except ValueError:
            pass
    
    return 22, 15, 6

def create_sequential_splits(dataset_size, val_ratio=0.2, test_ratio=0.1):
    """创建序列感知的数据分割"""
    indices = list(range(dataset_size))
    
    test_size = int(dataset_size * test_ratio)
    val_size = int(dataset_size * val_ratio)
    train_size = dataset_size - val_size - test_size
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size + val_size]
    test_indices = indices[train_size + val_size:]
    
    print(f"序列分割: 训练集={len(train_indices)}, 验证集={len(val_indices)}, 测试集={len(test_indices)}")
    return train_indices, val_indices, test_indices

def train_one_epoch(model, loader, optimizer, criterion_best, criterion_worst, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_batches = 0
    
    for batch_matrix, batch_labels in loader:
        batch_matrix = batch_matrix.to(device)
        batch_labels = batch_labels.to(device)
        
        optimizer.zero_grad()
        best_logits, worst_logits = model(batch_matrix)
        
        best_mask = batch_labels[:, 0] != -1
        worst_mask = batch_labels[:, 1] != -1
        
        loss = 0
        current_loss_value = 0
        
        if best_mask.any():
            loss_best = criterion_best(best_logits[best_mask], batch_labels[best_mask, 0])
            loss += loss_best
            current_loss_value += loss_best.item()
        
        if worst_mask.any():
            loss_worst = criterion_worst(worst_logits[worst_mask], batch_labels[worst_mask, 1])
            loss += loss_worst
            current_loss_value += loss_worst.item()
        
        if loss > 0:
            loss.backward()
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += current_loss_value
            total_batches += 1
    
    return total_loss / max(total_batches, 1)

def evaluate_model(model, loader, criterion_best, criterion_worst, device):
    """评估模型"""
    model.eval()
    total_loss = 0.0
    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0
    
    with torch.no_grad():
        for batch_matrix, batch_labels in loader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)
            
            best_logits, worst_logits = model(batch_matrix)
            
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1
            
            current_loss_value = 0
            
            if best_mask.any():
                loss_best = criterion_best(best_logits[best_mask], batch_labels[best_mask, 0])
                current_loss_value += loss_best.item()
                
                pred_best = best_logits[best_mask].argmax(dim=1)
                correct_best += (pred_best == batch_labels[best_mask, 0]).sum().item()
                total_best += best_mask.sum().item()
            
            if worst_mask.any():
                loss_worst = criterion_worst(worst_logits[worst_mask], batch_labels[worst_mask, 1])
                current_loss_value += loss_worst.item()
                
                pred_worst = worst_logits[worst_mask].argmax(dim=1)
                correct_worst += (pred_worst == batch_labels[worst_mask, 1]).sum().item()
                total_worst += worst_mask.sum().item()
            
            total_loss += current_loss_value
    
    avg_loss = total_loss / len(loader)
    acc_best = correct_best / max(total_best, 1)
    acc_worst = correct_worst / max(total_worst, 1)
    
    return avg_loss, acc_best, acc_worst

def train_minimal_model(
    matrix_file, label_file, orig_shape, new_shape,
    model_type="minimal",  # "minimal" or "simple"
    epochs=500, batch_size=8, lr=5e-4, weight_decay=1e-3,
    val_ratio=0.2, test_ratio=0.1, patience=30,
    dropout_rate=0.3, model_path="models/minimal_model.pth"
):
    """训练极简版模型"""
    
    # 创建数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape, new_shape)
    
    # 计算类别权重
    labels = dataset.labels
    n_classes = 20
    
    print("计算类别权重...")
    best_weights = compute_class_weights(labels[:, 0], n_classes)
    worst_weights = compute_class_weights(labels[:, 1], n_classes)
    
    print(f"Best action权重范围: {best_weights.min():.3f} - {best_weights.max():.3f}")
    print(f"Worst action权重范围: {worst_weights.min():.3f} - {worst_weights.max():.3f}")
    
    # 数据分割
    train_indices, val_indices, test_indices = create_sequential_splits(
        len(dataset), val_ratio, test_ratio
    )
    
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)
    test_dataset = Subset(dataset, test_indices)
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"训练集大小: {len(train_dataset)}, 验证集大小: {len(val_dataset)}, 测试集大小: {len(test_dataset)}")
    
    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 模型
    if model_type == "minimal":
        model = MinimalCNNClassifier(
            n_layers=orig_shape[0],
            n_stacks=5,
            max_blocks=15,
            n_classes=n_classes,
            dropout_rate=dropout_rate,
        ).to(device)
        print("使用MinimalCNN模型")
    else:
        model = SimpleCNNClassifier(
            n_layers=orig_shape[0],
            n_stacks=5,
            max_blocks=15,
            n_classes=n_classes,
            dropout_rate=dropout_rate,
        ).to(device)
        print("使用SimpleCNN模型")
    
    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")
    
    # 损失函数（使用类别权重）
    criterion_best = nn.CrossEntropyLoss(weight=best_weights.to(device))
    criterion_worst = nn.CrossEntropyLoss(weight=worst_weights.to(device))
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=15, verbose=True, min_lr=1e-6
    )
    
    # 训练循环
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"开始训练{model_type}模型...")
    
    for epoch in range(epochs):
        # 训练
        train_loss = train_one_epoch(model, train_loader, optimizer, criterion_best, criterion_worst, device)
        
        # 验证
        val_loss, val_acc_best, val_acc_worst = evaluate_model(model, val_loader, criterion_best, criterion_worst, device)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 打印进度
        if epoch % 10 == 0 or epoch < 20:
            print(f"Epoch {epoch+1}/{epochs} | train_loss={train_loss:.4f} | val_loss={val_loss:.4f} | "
                  f"acc_best={val_acc_best:.4f} | acc_worst={val_acc_worst:.4f}")
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            torch.save(model.state_dict(), model_path)
            if epoch % 10 == 0 or epoch < 20:
                print("Validation loss improved. Saving model state.")
        else:
            patience_counter += 1
            if epoch % 10 == 0 or epoch < 20:
                print(f"Validation loss did not improve for {patience_counter} epoch(s).")
        
        if patience_counter >= patience:
            print(f"Early stopping triggered after {epoch+1} epochs.")
            break
    
    # 加载最佳模型进行测试
    model.load_state_dict(torch.load(model_path, weights_only=True))
    test_loss, test_acc_best, test_acc_worst = evaluate_model(model, test_loader, criterion_best, criterion_worst, device)
    
    print("\n=== 测试集结果（真实泛化性能）===")
    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Best Action Accuracy: {test_acc_best:.4f}")
    print(f"Test Worst Action Accuracy: {test_acc_worst:.4f}")
    
    # 训练集性能检查
    train_loss_final, train_acc_best, train_acc_worst = evaluate_model(model, train_loader, criterion_best, criterion_worst, device)
    print("\n=== 训练集结果（过拟合检查）===")
    print(f"Train Loss: {train_loss_final:.4f}")
    print(f"Train Best Action Accuracy: {train_acc_best:.4f}")
    print(f"Train Worst Action Accuracy: {train_acc_worst:.4f}")
    
    return model

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--matrix_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_matrix.csv"))
    parser.add_argument("--labels_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_labels.csv"))
    parser.add_argument("--model_type", choices=["minimal", "simple"], default="minimal")
    parser.add_argument("--epochs", type=int, default=500)
    parser.add_argument("--batch_size", type=int, default=8)
    parser.add_argument("--lr", type=float, default=5e-4)
    parser.add_argument("--weight_decay", type=float, default=1e-3)
    parser.add_argument("--val_ratio", type=float, default=0.2)
    parser.add_argument("--test_ratio", type=float, default=0.1)
    parser.add_argument("--patience", type=int, default=30)
    parser.add_argument("--dropout", type=float, default=0.3)
    parser.add_argument("--model_out", default=os.path.join(os.path.dirname(__file__), "models", "minimal_model.pth"))
    args = parser.parse_args()

    # 推断数据形状
    header = pd.read_csv(args.matrix_csv, nrows=0).columns.tolist()
    n_layers, n_rows, n_cols = infer_shape_from_csv_header(header)
    print(f"Inferred shape: n_layers={n_layers}, n_rows={n_rows}, n_cols={n_cols}")

    orig_shape = (n_layers, n_rows, n_cols)
    new_shape = orig_shape

    # 训练模型
    model = train_minimal_model(
        matrix_file=args.matrix_csv,
        label_file=args.labels_csv,
        orig_shape=orig_shape,
        new_shape=new_shape,
        model_type=args.model_type,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        weight_decay=args.weight_decay,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        patience=args.patience,
        dropout_rate=args.dropout,
        model_path=args.model_out
    )