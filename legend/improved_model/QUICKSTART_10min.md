# Improved Model 快速上手（10 分钟）

本指南帮助你在 10 分钟内跑通改进版 Blocks World 指导系统的完整流程：
- 生成随机算例
- 批量收集 LLM 指导日志（可选）
- 从日志构建训练数据
- 训练改进版 CNN+Transformer 模型
- 用模型+通用规则 引导 A* 搜索
- 渲染解决方案 GIF

先决条件：
- Python 3.9+，已安装 PyTorch、matplotlib（训练/可视化用）
- 若要用 LLM 收集日志，请在 improved_model/llm_guidance.py 配置 BASE_URL 与 API_KEYS

## 1. 进入目录

```bash
cd legend/improved_model
```

## 2. 生成随机算例（可选）

```bash
python generate_random_instances.py --output_dir legend/improved_model/data/instances
# 输出：legend/improved_model/data/instances/random_instances.json 与 instance_*.txt
```

## 3. 批量收集 LLM 指导日志（可选）

确保 improved_model/llm_guidance.py 可用（本地/远程大模型）
```bash
python batch_collect_llm_data.py \
  --instances_file legend/improved_model/data/instances/random_instances.json \
  --output_dir legend/improved_model/data/logs/llm \
  --results_file legend/improved_model/data/logs/llm/batch_results.json
# 输出：legend/improved_model/data/logs/llm/*.txt 与 batch_results.json
```

也可跳过此步，直接使用已有示例日志：improved_model/llm_guided_test_log.txt

## 4. 从日志构建训练数据

```bash
# 使用 data/logs/llm 目录中的日志批量构建（Node 风格）
python build_dataset.py \
  --input_dir legend/improved_model/data/logs/llm \
  --output_prefix legend/improved_model/data/datasets/dataset_from_logs \
  --save_raw_json
# 产出：legend/improved_model/data/datasets/dataset_from_logs_{matrices,labels}.npy 与 metadata.json
```

## 5. 训练模型

```bash
python train_improved_model.py \
  --data_prefix legend/improved_model/data/datasets/dataset_from_logs \
  --epochs 80 --batch_size 16 \
  --lr 3e-4 --weight_decay 1e-4 \
  --label_smoothing 0.05 --worst_loss_coef 0.10 \
  --patience 40 --embed_dim 128 --n_heads 8 --dropout 0.1 \
  --model_out legend/improved_model/data/models/improved_model_best.pth
```

## 6. 引导搜索（含通用规则）

```bash
# 使用示例实例与训练好的模型（NN+规则）
python test_guided_astar_final.py \
  --instance legend/improved_model/data/instances/instance_28.txt \
  --model legend/improved_model/data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --use_rule \
  --alpha 0.5 \
  --solution_out legend/improved_model/data/solutions/instance_28_solution_rule.json \
  --log legend/improved_model/data/logs/search/instance_28_guided_rule.txt

# NN+规则 + 动态权重（一致性感知，含算法侧加速）
python test_guided_astar_final.py \
  --instance legend/improved_model/data/instances/instance_28.txt \
  --model legend/improved_model/data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --use_rule \
  --alpha 0.5 \
  --use_dynamic_weights \
  --solution_out legend/improved_model/data/solutions/instance_28_solution_rule_dyn_cached.json \
  --log legend/improved_model/data/logs/search/instance_28_guided_rule_dyn_cached.txt

# Quick compare 1：NN 无规则（基线）
python test_guided_astar_final.py \
  --instance legend/improved_model/data/instances/instance_28.txt \
  --model legend/improved_model/data/models/improved_model_best.pth \
  --max_nodes 50000 --alpha 0.5 \
  --no_rule \
  --solution_out legend/improved_model/data/solutions/instance_28_solution_no_rule.json \
  --log legend/improved_model/data/logs/search/instance_28_no_rule.txt

# Quick compare 2：仅规则（无NN评分）
python test_guided_astar_final.py \
  --instance legend/improved_model/data/instances/instance_28.txt \
  --model legend/improved_model/data/models/improved_model_best.pth \
  --max_nodes 50000 --alpha 0.5 \
  --use_rule --rule_only \
  --solution_out legend/improved_model/data/solutions/instance_28_solution_rule_only.json \
  --log legend/improved_model/data/logs/search/instance_28_rule_only.txt
```

输出示例：
- 搜索是否成功、路径长度、扩展节点数、规则触发次数
- 若导出 JSON，可用于渲染 GIF

## 7. 渲染 GIF

```bash
python render_solution_gif.py \
  --input legend/improved_model/data/solutions/instance_28_solution_rule.json \
  --output legend/improved_model/data/gifs/instance_28_solution_rule.gif \
  --fps 2
```

## 常见问题（FAQ）

- Q: llm_guidance.py 报 API 错误？
  - A: 检查 BASE_URL、API_KEYS，或临时跳过第 3 步，直接使用示例日志。
- Q: limited_astar_guidance_local 未找到？
  - A: 确保在 improved_model 目录内运行，或将其加入 PYTHONPATH。
- Q: 训练集样本很少，效果一般？
  - A: 通过第 2 步与第 3 步扩大数据，或合并多份日志再构建数据集；模型表现将随覆盖度提升而改善。

