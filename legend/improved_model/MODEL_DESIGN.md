# 模型设计与通用规则（Universal Rule）说明

本说明基于以下文件与数据，系统阐述改进版 Blocks World 引导模型的设计、优势，以及通用规则的必要性与集成方式：

- 模型与编码器：`legend/improved_model/improved_cnn_model.py`
- 训练管线：`legend/improved_model/train_improved_model.py`
- 引导搜索：`legend/improved_model/test_guided_astar_final.py`
- 数据与元信息：`legend/improved_model/data/datasets/dataset_from_logs_*.npy/json`

---

## 一、目标与数据规格

- 目标：利用深度模型（CNN + Transformer）引导 A* 搜索，高效求解 Blocks World（多栈堆叠）问题；在数据稀缺场景下引入“通用规则（Universal Rule）”增强稳健性与可解释性。
- 训练数据规格（见 `dataset_from_log_train_metadata.json`）：
  - 输入张量形状：`(N, 22, 15, 6)`
    - 22 层：1×目标状态 + 1×当前状态 + 最多 20×后继状态
    - 15 块（n_blocks=15），6 栈（n_stacks=6）
  - 标签张量形状：`(N, 2)`（同时监督 best 与 worst 动作）
  - 动作类别数：`20`
    - 设计要点：6 个总栈中保留 1 个“缓冲栈（buffer）”，动作空间仅在其余 5 个“有效栈”之间流动 → `5 × 4 = 20`
  - 标签覆盖率：best/worst 覆盖均为 1.0

---

## 二、状态/动作编码（BlocksWorldEncoder）

- 单通道“自顶向下”编码：将状态编码为 `(n_blocks, n_stacks)` 的矩阵，值含义：
  - `0`：该块不在该栈
  - `1`：该块位于该栈“栈顶”
  - `n>1`：该块距离该栈顶的层数（越大越靠下）
- 优势：
  - 精准表达“可立即移动的块”（值为 1 的位置），天然契合“只能操作栈顶”的规则。
  - 相比 one-hot/多通道“上下文稀释”，本编码更直接反映“层级/可达性”。
- 动作空间与缓冲栈：
  - 将 6 栈划分为 5 个“有效栈” + 1 个“缓冲栈”；训练分类仅覆盖有效栈之间的移动（20 类）。
  - 缓冲栈可作为中间过渡，不计入分类空间，使学习目标更稳定、清晰。
- 附加能力：
  - `get_movable_blocks()` 快速枚举可移动块（矩阵中值=1 的位置）。
  - `encode_action`/`decode_action` 在“20 类动作空间”与 `(from_stack, to_stack)` 间映射，统一训练与搜索接口。

---

## 三、改进的 CNN + Transformer 架构（ImprovedBlocksWorldCNN）

核心思想：分解“栈内关系”“栈间关系”“全局整合”三级结构信息；避免早期过度池化导致的位置丢失；以注意力/Transformer 捕获高阶依赖与策略模式。

- 输入张量：`(batch, n_layers=22, n_blocks=15, n_stacks=6)`
- 三阶段卷积特征提取：
  1) 栈内特征（垂直卷积核）
     - 5×1、3×1 卷积序列（含 BN、Dropout），专注同栈上下层关系与“自顶向下”的结构。
  2) 栈间特征（水平卷积核）
     - 1×3 卷积（含 BN、Dropout），建模相邻栈之间的联动与迁移模式。
  3) 多尺度整合 + 融合
     - 空洞卷积（3×3, dilation=2）扩大感受野，兼顾局部与全局模式；
     - 1×1 卷积融合至 `embed_dim`。
- 空间注意力（SpatialAttention）：
  - 两层 1×1 卷积获得空间注意力权重，聚焦“关键区域”（如活跃栈顶、目标兼容栈）。
- Transformer 编码器：
  - 将 CNN 输出展平为 `n_blocks × n_stacks` 的位置序列，加可学习位置编码；
  - `nn.TransformerEncoder` 建模跨块/跨栈的长程依赖，捕获更高层的策略结构。
- 双头多任务分类：
  - `fc_best` 与 `fc_worst` 同时预测“最优动作 / 最差动作”，增强对“应该做/不该做”的辨别。
- 训练要点（`train_improved_model.py`）：
  - 损失：交叉熵（可用 Label Smoothing），`worst_loss_coef` 可调；
  - 早停 +（可选）MultiStepLR；Dropout 抑制过拟合；
  - 验证指标：`val_loss`、best/worst 准确率；设备优先使用 GPU。

### 主要优势

- 结构性先验强：垂直/水平卷积匹配“栈内/栈间”语义。
- 位置精度高：不依赖大步长池化，保留“顶/底/层级”的关键差异。
- 注意力聚焦：空间注意力放大关键区域特征。
- 全局关系：Transformer 捕获跨栈/跨层的长程依赖与策略模式。
- 多任务学习：best/worst 双头约束，信号更丰富、泛化更稳健。
- 动作空间解耦：有效栈 + 缓冲栈设计，使 20 类动作分类更稳定可控。

---

## 四、通用规则（Universal Rule）：动机、设计与价值

### 动机（来自日志与实验）

- 样本量有限（~109 条），且分布不均（例如“清空后重建栈”的样本稀缺）。
- 纯模型引导在关键稀疏场景下容易“犹豫/漂移”，甚至陷入局部循环导致节点扩展耗尽。

### 规则核心思想

当“某个目标栈”当前状态与“目标前缀”兼容（空栈或底部序列与目标一致），且“该栈所需的下一目标块”恰好在“其它任一栈的栈顶”可取时：

- 立即选择将该块“直接移动到该目标栈”的动作。

自然语言描述：如果有机会“立刻做对的事”（把正确的下一块放到正确的栈）——优先这么做。

### 触发条件（抽象）

1. 目标栈兼容：空栈，或已构建的前缀与目标一致；
2. 下一目标块可用：在其他栈的栈顶；
3. 动作合法：转移不会违反基本约束（如移非顶块、越界等）。

### 在搜索中的集成（`test_guided_astar_final.py`）

- 优先检查通用规则；若触发，直接选用规则动作；
- 否则回退至模型预测（可结合 Top-k 过滤、成本塑形 `alpha`、节点扩展上限 `max_nodes`）。

### 价值与优势

- 数据稀缺补齐：在训练覆盖薄弱的关键情景下提供强先验。
- 搜索效率：减少无效扩展/循环，显著降低节点数与失败率。
- 稳健与可解释：触发条件清晰、可审计，与模型预测互补。
- 易扩展：可逐步加入更多“低风险、强语义”的领域规则（如“避免压住未来目标块”等）。

### 边界与注意

- 规则需确保条件正确、无矛盾，避免与模型强冲突。
- 规则只覆盖关键场景，不能替代模型的广泛学习能力。
- 需通过日志与 A/B 对比持续验证规则优先级与触发阈值。

---

## 五、现有实证要点（摘要）

- 训练结果（本次，data/datasets/dataset_from_logs）：
  - 最佳验证损失 3.1467；测试集准确率：best 38.5%，worst 69.2%。
- 搜索对比：
  - 纯模型引导：容易耗尽节点上限或陷入局部。
  - 加入通用规则：显著减少节点扩展，稳定性与解质量更好。

> 注：瓶颈主要来自数据量与分布；通用规则在数据稀缺阶段提供了有效增益。

---

## 六、使用概览

- 构建数据：
  - 见 `build_dataset.py`（将 Node 风格 LLM 引导日志转为 `(22, 15, 6)` 的训练张量与标签）。
- 训练模型：
  - `python train_improved_model.py --data_prefix dataset_from_log_train --epochs 50 --batch_size 32 --lr 5e-4 --patience 20 --label_smoothing 0.1`
  - 自动使用 GPU（如可用），保存最佳权重至 `improved_model_best.pth`。
- 引导搜索（含通用规则）：
  - `python test_guided_astar_final.py --instance instance_28.txt --model improved_model_best.pth --max_nodes 10000 --enable_rule`

---

## 七、主要贡献与创新点（论文式描述）

本工作针对 Blocks World 规划中的“样本稀缺+搜索不稳”的难题，提出一个“学习-规则混合”的引导搜索体系，贡献如下：

1) 面向 Blocks World 的层级感知编码与 CNN-Transformer 混合网络

- 我们提出自顶向下的层级编码：顶层值=1，向下逐层递增，使“可移动块”在编码空间中自然凸显；
- 以“垂直卷积(栈内)+水平卷积(栈间)+空洞多尺度卷积”三级结构抽取空间关系，同时引入空间注意力聚焦“关键区域”（活跃栈顶、目标兼容栈）；
- 使用 Transformer 编码器对“块×栈”位置序列施加全局建模，捕获跨栈/跨层的长程依赖与策略模式；
- 采用双头多任务学习（best/worst 同时预测），提供“做/不做”的双重监督信号，增强稳定性。

2) 有效栈 vs 缓冲栈的动作空间解耦与对齐

- Blocks World 的 6 栈结构中，我们将其中 1 栈视为缓冲栈，仅作为中间过渡，不纳入 20 类动作分类，
  其余 5 栈之间的迁移形成固定的 20 类动作空间（5×4），与“22 层输入（目标/当前/20 个后继）”严格对齐；
- 这一设计显著简化了分类目标，使学习在有限数据下更稳健，并自然对齐搜索时的候选后继编码。

3) 通用规则（Universal Rule）与学习的冲突仲裁机制

- 提出并实现“下一目标块在栈顶时直接搬运”的最优规则，以及“从已正确前缀栈移出为最差”的最差规则；
- 在搜索时，优先检测规则，并在规则与 LLM/NN 指导发生冲突时，采用“规则优先”的仲裁：
  - 若 LLM/NN 的“最佳”与规则“最差”冲突，取消该最佳的正偏置；
  - 若 LLM/NN 的“最差”与规则“最佳”冲突，取消该最差的负偏置；
    随后对规则命中动作再施加强/弱偏置（最优：g-=2.0、h*=0.5；最差：h*=2.0），显著减少“相互抵消导致停滞”的风险。

4) 一致性感知的动态权重（重要创新）

- 动机：固定缩放系数在复杂实例上易“权重失当”，导致搜索路径劣化；而完全放开会破坏 A* 的一致性假设。
- 我们提出一致性感知的动态权重：对每个候选子节点估计一致性区间 [k_min, k_max]（基于 h_parent, h_child, 以及子节点的后继启发），
  - 低置信度：k 落在区间内，并贴近边界留出小 margin，既安全又保留引导性；
  - 高置信度：允许突破一致性约束（更强的收缩/放大），以充分利用可靠指导信息；
  - 通用规则：视为置信度=1，始终允许突破一致性，确保“机会性正确动作”优先推进。
- 该设计实现了“充分利用指导”与“避免权重失当”的兼顾，在多实例上保持或提升收敛效率。

5) 算法侧加速（Top-K 一致性估计 + 子后继启发缓存）

- 仅对重要候选（模型 top-k 权重命中、best_action、worst_action）估计动态区间，其它候选用固定系数；
- 对子状态的后继启发值进行缓存，避免重复展开和重复 heuristic 计算；
- 显著降低一致性估计的额外开销，在保持搜索质量的前提下提升整体速度。

6) 一步式数据管线与多日志聚合构建

- 提出并实现从“Node 风格 LLM 日志”直接一步生成训练集的管线（build_dataset.py），免去多脚本来回转换；
- 扩展支持从目录/多文件同时解析与聚合，自动过滤“动作不在可训练空间”的样本（如涉及缓冲栈）， 以保证数据质量并最大化利用有效监督样本。

7) 统一的实验对齐与工程化自包含

- 引入 data/ 目录规范，统一存放实例、日志、数据集、模型、解与可视化产物， 并在 README/Quickstart 中统一路径示例与命令，保证可复现性与最小上手成本；
- 将 A* 规划与规则逻辑封装为本地 limited_astar_guidance_local.py，消除跨目录依赖，实现工程自包含。

8) 实证结果与效果

- instance_28：路径 34、节点 122（动/不动权重均一致）
- instance_29：动态权重+加速 vs 动态权重（未加速）用时显著降低（~24.7s vs ~32.8s），节点数基本一致（4920 vs 4921）；均成功收敛

9) 可解释性与可扩展性

- 规则触发与仲裁过程均有详细日志与节点级记录；
- 动态权重可继续扩展（如更细的置信度到 k 的映射、对一致性边界的自适应 margin）；
- 框架可逐步加入更多“低风险、强语义”的规则，并与更强策略（束搜索/MCTS）和更大规模数据协同演进。

---

## 八、展望与建议

- 数据增强：系统生成“空栈重建/目标前缀扩展”等关键稀疏样本；平衡类别。
- 规则迭代：增加“避免阻挡未来目标块”“优先利用空栈”等可解释、低风险的规则。
- 可视化诊断：加入空间注意力热力图/中间特征可视化定位薄弱点。
- 搜索策略：调参 Top-k、`alpha`、`max_nodes`；探索束搜索/MCTS 等更强策略。
- 工程集成：在不修改主工程代码的前提下，提供轻量适配，使本模型可作为通用“动作评估器”被外部调用。

---

## 附：与传统实现的对比（简述）

- 传统 CNN+Transformer（对称卷积 + 池化 + Transformer）：

### 最新对比与改动（2025-08）

- 新增通用“最差”规则：若某栈与目标前缀完全一致（无冲突），则将该栈顶块移走视为最差动作（在动作选择中被强烈打压）。
- 规则加权：当最优/最差由通用规则命中时，分别施加更强的正/负向偏置（不影响模型给出的最优/最差）：

  - 最优（规则）：g -= 2.0，h *= 0.50
  - 最差（规则）：h *= 2.00
- 结果对比（instance_28，max_nodes=50k，alpha=0.5）：

  - NN 指导 + 通用规则：找到解，路径 34，扩展节点 122；解 data/solutions/instance_28_solution_rule.json
  - NN 指导（无规则）：找到解，路径 36，扩展节点 144；解 data/solutions/instance_28_solution_no_rule.json
  - 仅通用规则（rule_only）：50k 节点未收敛；日志 data/logs/search/instance_28_rule_only.txt
- 结论：在现有数据规模与模型能力下，“规则+学习”显著优于“仅规则”；而对通用规则进行更强的定向加权，对收敛效率提升明显。

  - 优点：结构清晰、实现成熟；
  - 局限：可能在“精确位置/层级”表征上受池化影响。
- 本改进版：

  - 垂直/水平卷积显式分解“栈内/栈间”关系；
  - 空洞卷积 + 空间注意力提升对关键区域与多尺度模式的敏感度；
  - 有效栈 + 缓冲栈设计让 20 类动作更贴近求解策略；
  - 与通用规则形成“学习 + 规则”的混合智能，显著改善数据稀缺下的搜索效果。

---

## 八、精确实现要点与代码引用

以下摘录来自源码，突出关键实现选择与其含义（仅节选，完整代码见对应文件）。

### 1) 状态编码（BlocksWorldEncoder.encode_state）

来源：legend/improved_model/improved_cnn_model.py

```python
# n_blocks×n_stacks，值=距栈顶距离（顶=1）
for stack_idx, stack_name in enumerate(sorted(state.keys())):
    blocks = state[stack_name]
    h = len(blocks)
    for pos, block in enumerate(blocks):
        bidx = self.block_names.index(block)
        matrix[bidx, stack_idx] = h - pos
```

要点：顶层=1、下方逐步增大，天然标识“可移动块”。

### 2) 栈内/栈间卷积核与多尺度整合

来源：legend/improved_model/improved_cnn_model.py

```python
# 栈内（垂直）
self.stack_conv1 = nn.Conv2d(n_layers, 64, kernel_size=(5,1), padding=(2,0))
self.stack_conv2 = nn.Conv2d(64, 128, kernel_size=(3,1), padding=(1,0))
# 栈间（水平）
self.cross_stack_conv = nn.Conv2d(128, 128, kernel_size=(1,3), padding=(0,1))
# 多尺度+融合（空洞+1×1）
self.multi_scale_conv1 = nn.Conv2d(128, 256, kernel_size=3, padding=2, dilation=2)
self.fusion_conv = nn.Conv2d(256, embed_dim, kernel_size=1)
```

要点：显式拆分“垂直/水平”关系；空洞卷积扩大感受野，1×1 做通道融合。

### 3) 空间注意力与位置编码 + Transformer

来源：legend/improved_model/improved_cnn_model.py

```python
# 空间注意力（1×1→1×1）
self.conv1 = nn.Conv2d(in_channels, in_channels // 8, kernel_size=1)
self.conv2 = nn.Conv2d(in_channels // 8, 1, kernel_size=1)
# 位置编码（n_blocks*n_stacks, embed_dim）
self.pos_embedding = nn.Parameter(torch.zeros(1, n_blocks * n_stacks, embed_dim))
```

要点：注意力聚焦关键区域；位置编码保障 Transformer 的空间可感知性。

### 4) 输入 22 层的构造（目标/当前/20 个后继）

来源：legend/improved_model/test_guided_astar_final.py

```python
succs = []
for fs in range(5):
    for ts in range(5):
        if fs == ts: continue
        succs.append(self.encoder.apply_action(cur_m, fs, ts))
mat = np.zeros((22, self.encoder.n_blocks, self.encoder.n_stacks), dtype=np.float32)
mat[0] = goal_m; mat[1] = cur_m
for i, s in enumerate(succs):
    mat[2 + i] = s
```

要点：固定 20 个候选后继（5 有效栈间两两迁移），与 20 类动作空间对齐。

### 5) 动作类别映射（20 类）

来源：legend/improved_model/test_guided_astar_final.py

```python
fs = int(fs1) - 1; ts = int(ts1) - 1
if fs == ts or fs < 0 or fs >= 5 or ts < 0 or ts >= 5: continue
adjusted_to = ts - 1 if ts > fs else ts
class_id = fs * 4 + adjusted_to
```

要点：有效栈索引 0..4；同栈无效；目标索引跨过自身后左移 1，形成紧凑 4 类，合计 5×4=20。

### 6) 通用规则（Universal Rule）核心逻辑

来源：legend/improved_model/test_guided_astar_final.py

```python
# 目标栈前缀兼容（空或前缀匹配）时，取下一目标块 next_block
next_idx = len(current_stack)
if next_idx < len(goal_stack):
    next_block = goal_stack[next_idx]
    # 若 next_block 在其它某栈顶，则直接选该动作
    if source_stack and source_stack[-1] == next_block:
        return {"best_action": i, ...}
```

要点：当“正确下一步”处于可立即执行状态，优先执行该动作，减少无效探索。

---

## 九、与 A* 引导的接口约定（适配器）

- 适配器类：`ImprovedModelGuidanceAdapter`（`test_guided_astar_final.py`）
  - `evaluate_actions(current_state, goal_state, planner, successors)`：首先尝试通用规则；若未命中，则编码 22 层输入，计算 best/worst 概率并打分（`score = best_p - alpha * worst_p`），映射回合法后继，返回最佳与最差动作索引及排序信息。
  - `alpha`：成本塑形系数，平衡“做最好”与“避最差”。
  - `rule_applications`：统计通用规则触发次数，便于诊断其贡献度。

以上细节直接对应代码实现，确保设计说明与源码一致。
