#!/usr/bin/env python3
"""
本地版带节点限制的A*搜索（Blocks World），无需依赖外部目录。
- 状态格式：{"Stack1": [...], "Stack2": [...], ...}，列表从底->顶
- 动作格式：(i, j)（1-based），表示将第 i 个栈顶块移动到第 j 个栈顶
- 修复顺序：fix_order = ["Stack1", "Stack2", ...]；根据与目标的前缀匹配更新当前修复栈
- 指导接口：llm.evaluate_actions(current_state: dict, goal_state: dict, planner, successors)
  其中 successors 为 List[ (next_state_dict, (i,j)) ]
"""
import heapq
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class State:
    def __init__(self, stacks: Dict[str, List], current_fix_stack: Optional[str], g=0, h=0, parent=None, action=None):
        self.stacks = stacks  # {StackX: [..]}
        self.current_fix_stack = current_fix_stack
        self.g = g
        self.h = h
        self.cost = g + h
        self.parent = parent
        self.action = action
        self.h_adjusted = None

    def __lt__(self, other):
        return self.cost < other.cost

    def __eq__(self, other):
        if not isinstance(other, State):
            return False
        return tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())) == \
               tuple(sorted((k, tuple(v)) for k, v in other.stacks.items()))

    def __hash__(self):
        return hash(tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())))

    def __str__(self):
        return str(self.stacks)


class GraphPlanningBlocksWorld:
    def __init__(self, start_state: Dict[str, List], goal_state: Dict[str, List], fix_order: List[str], log_file: str = "astar_log.txt"):
        self.state = State(start_state, fix_order[0] if fix_order else None)
        self.goal = State(goal_state, None)
        self.log_file = log_file
        self.fix_order = fix_order[:] if fix_order else []
        self.check = []

    # ---------- 通用规则（最佳/最差） ----------
    @staticmethod
    def _universal_rule_indices(current_stacks: Dict[str, List], goal_stacks: Dict[str, List], successors: List[Tuple[State, Tuple[int,int]]]):
        """返回(rule_best_idx, rule_worst_idx)，均为基于0的successors索引；无则为-1。
        规则：
        - 最佳：若某目标栈当前前缀与目标一致，则寻找该栈所需的下一块是否在其他某栈顶，若是，(source->target) 为最佳。
        - 最差：若某目标栈当前前缀与目标一致且非空，则从该栈移出其顶块到任意其他栈为最差。
        """
        # 目标/当前栈名排序保证与 successors 使用的列表次序一致
        stack_names = list(current_stacks.keys())
        n = len(stack_names)
        rule_best = -1
        rule_worst = -1
        # 扫描每个可能的目标栈
        for t_idx in range(n):
            t_name = stack_names[t_idx]
            cur = current_stacks.get(t_name, [])
            goal = goal_stacks.get(t_name, [])
            if not goal:
                continue
            # 检查前缀一致性（无冲突）
            ok = True
            for i, b in enumerate(cur):
                if i >= len(goal) or b != goal[i]:
                    ok = False
                    break
            if not ok:
                continue
            # 规则-最差：从该无冲突栈移出其顶到任意其他栈都算最差（取第一个命中即可）
            if cur:
                for i, (_ns, (fs, ts)) in enumerate(successors):
                    if fs == (t_idx + 1) and ts != fs:
                        rule_worst = i
                        break
            # 规则-最佳：需要的下一块若在别的栈顶，则搬过去
            if len(cur) < len(goal):
                needed = goal[len(cur)]
                for s_idx in range(n):
                    if s_idx == t_idx:
                        continue
                    s_name = stack_names[s_idx]
                    s_stack = current_stacks.get(s_name, [])
                    if s_stack and s_stack[-1] == needed:
                        # 找到 (s_idx+1 -> t_idx+1) 的后继索引
                        for i, (_ns, (fs, ts)) in enumerate(successors):
                            if fs == (s_idx + 1) and ts == (t_idx + 1):
                                rule_best = i
                                break
                        break
            # 如果都找到了，就可以提前返回
            if rule_best != -1 or rule_worst != -1:
                # 不 break，允许后续栈可能给出更强的约束；但通常第一个即可
                pass
        return rule_best, rule_worst

    # ---------- 基础工具 ----------
    def is_goal(self, state: State) -> bool:
        return state.stacks == self.goal.stacks

    def is_stack_fixed(self, stack_name: str, current_stacks: Dict[str, List], goal_stacks: Dict[str, List]) -> bool:
        cur = current_stacks.get(stack_name, [])
        goal = goal_stacks.get(stack_name, [])
        if len(cur) < len(goal):
            return False
        for i in range(len(goal)):
            if cur[i] != goal[i]:
                return False
        return True

    def update_fix_stack(self, stacks: Dict[str, List]) -> Optional[str]:
        for name in self.fix_order:
            if not self.is_stack_fixed(name, stacks, self.goal.stacks):
                return name
        return None

    def get_successors(self) -> List[Tuple[State, Tuple[int, int]]]:
        successors: List[Tuple[State, Tuple[int, int]]] = []
        stack_names = list(self.state.stacks.keys())
        for i in range(len(stack_names)):
            for j in range(len(stack_names)):
                if i == j:
                    continue
                if not self.state.stacks[stack_names[i]]:
                    continue
                new_stacks = {k: v.copy() for k, v in self.state.stacks.items()}
                block = new_stacks[stack_names[i]].pop()
                new_stacks[stack_names[j]].append(block)
                new_fix_stack = self.update_fix_stack(new_stacks)
                new_state = State(new_stacks, new_fix_stack)
                action = (i + 1, j + 1)
                successors.append((new_state, action))
        return successors

    def heuristic(self, state: State) -> int:
        # 与外部版本一致：统计不在目标位置的块数量（按 on-block/on-table 映射）
        cost = 0
        goal_on = {}
        for sname, blocks in self.goal.stacks.items():
            for i in range(len(blocks)):
                goal_on[blocks[i]] = (sname if i == 0 else blocks[i-1])
        cur_on = {}
        for sname, blocks in state.stacks.items():
            for i in range(len(blocks)):
                cur_on[blocks[i]] = (sname if i == 0 else blocks[i-1])
        for block, target in goal_on.items():
            if cur_on.get(block) != target:
                cost += 1
        return cost

    def log(self, message: str):
        ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        line = f"[{ts}] {message}"
        print(line)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(line + '\n')

    def log_raw(self, message: str):
        # 兼容 legend/llm_guided_test_log.txt 的解析格式：不加时间戳，原样写入
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')


class LimitedGraphPlanningBlocksWorld(GraphPlanningBlocksWorld):
    def __init__(self, start_state, goal_state, fix_order, log_file="limited_astar_log.txt", max_nodes: int = 200000, use_dynamic_weights: bool = False, dynamic_k: int = 3):
        super().__init__(start_state, goal_state, fix_order, log_file)
        self.max_nodes = int(max_nodes)
        self.use_dynamic_weights = bool(use_dynamic_weights)
        self.dynamic_k = int(dynamic_k)
        # 缓存子后继的启发列表，减少重复计算
        self._succ_h_cache = {}

    def a_star_search(self, llm=None) -> Tuple[Optional[List[Tuple[int, int]]], int]:
        # 初始化
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h
        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []
        open(self.log_file, 'w', encoding='utf-8').close()

        # 兼容 Node 风格日志的起始行（示范文件样式）
        if llm is not None and hasattr(llm, 'model'):
            self.log_raw("")
            self.log_raw(f"Running A* with LLM {llm.model}:")
            self.log_raw("")

        self.log(f"运行带限制的A*搜索（max_nodes={self.max_nodes}，指导={'ON' if llm else 'OFF'}）")
        start_time = time.time()

        while queue and count < self.max_nodes:
            _, _, current_state, path = heapq.heappop(queue)
            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1
            if len(self.check) < count:
                self.check.append(True)

            if self.is_goal(current_state):
                elapsed = time.time() - start_time
                self.log(f"✅ 找到解决方案：{path}")
                self.log(f"搜索节点数：{count}；路径长度：{len(path)}；耗时：{elapsed:.2f}s")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            if llm is not None and hasattr(llm, 'evaluate_actions'):
                # 转换成 dict 版本 successors 供模型评分
                converted = [(ns.stacks, act) for ns, act in successors]
                try:
                    result = llm.evaluate_actions(current_state.stacks, self.goal.stacks, self, converted) or {}
                    best_action = int(result.get('best_action', -1))
                    worst_action = int(result.get('worst_action', -1))
                    ranking = result.get('ranking', [])
                    # 以上分支：假设索引基于0（模型打分器返回索引）
                except Exception as e:
                    # 尝试兼容 LLM 版本接口：传入 State 对象和原始 successors，且返回1-based编号
                    try:
                        llm_result, actions, current_issue, priority = llm.evaluate_actions(current_state, self.goal, self, successors)
                        # 规范化动作编号
                        def to_zero_based(v):
                            try:
                                vi = int(v)
                                return max(-1, vi - 1)
                            except Exception:
                                return -1
                        best_action = to_zero_based(llm_result.get('best_action', -1))
                        worst_action = to_zero_based(llm_result.get('worst_action', -1))
                        ranking = []
                        # 兼容 Node 风格日志输出
                        node_id = count
                        self.log_raw(f"Node {node_id}: Current fix stack: **{current_state.current_fix_stack}** Current issue: {current_issue} Priority task: {priority}")
                        self.log_raw(f"Node {node_id}: Current state: {current_state.stacks}")
                        self.log_raw(f"Node {node_id}: Goal state: {self.goal.stacks}")
                        # LLM 建议（如果有）
                        if best_action >= 0 and best_action < len(successors):
                            self.log_raw(f"LLM suggests Best Action '({successors[best_action][1][0]}, {successors[best_action][1][1]})'")
                            self.log_raw(f"Best Reason: {llm_result.get('best_reason', '')}")
                        if worst_action >= 0 and worst_action < len(successors):
                            self.log_raw(f"LLM suggests Worst Action '({successors[worst_action][1][0]}, {successors[worst_action][1][1]})'")
                            self.log_raw(f"Worst Reason: {llm_result.get('worst_reason', '')}")
                        # 节点结束空行，严格对齐示范日志的分隔
                        self.log_raw("")
                    except Exception as e2:
                        self.log(f"⚠️ 指导评估失败：{e} / 回退LLM接口失败：{e2}；回退为无引导")
                        best_action = -1
                        worst_action = -1
                        ranking = []
                # 将排名前k项线性加权（如有提供）
                weights = {}
                top_k = getattr(llm, 'top_k', 0) or 0
                if top_k > 0 and ranking:
                    for rank_idx, item in enumerate(ranking[:top_k]):
                        i = int(item.get('i', -1))
                        if i >= 0:
                            w = 1.0 - (rank_idx / max(1, top_k - 1)) * 0.5 if top_k > 1 else 1.0
                            weights[i] = float(w)

                # 通用规则索引（0-based）；若存在冲突，以规则优先，屏蔽相反引导
                rule_best, rule_worst = self._universal_rule_indices(current_state.stacks, self.goal.stacks, successors)
                if rule_best != -1 and best_action == rule_worst:
                    # LLM/NN 认为的最佳与规则最差冲突：取消该最佳的加权
                    best_action = -1
                    weights.pop(rule_worst, None)
                if rule_worst != -1 and worst_action == rule_best:
                    # LLM/NN 认为的最差与规则最佳冲突：取消该最差的加权
                    worst_action = -1

                # 计算每个后继的代价并入队（支持动态权重）
                parent_h_adj = current_state.h_adjusted if current_state.h_adjusted is not None else current_state.h
                for i, (next_state, action) in enumerate(successors):
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    h_orig = next_state.h

                    # 动态权重推断（保持一致性边界），仅在启用时计算
                    if self.use_dynamic_weights:
                        # 仅对 top-K 候选估计动态 k，减少开销
                        consider_dynamic = False
                        if i in weights:
                            consider_dynamic = True
                        elif i == best_action or i == worst_action:
                            consider_dynamic = True
                        # 可选：规则不需要一致性估计，直接走强偏置
                        if consider_dynamic and not (i == rule_best or i == rule_worst):
                            # 从缓存获取子后继启发
                            child_key = tuple(sorted((k, tuple(v)) for k, v in next_state.stacks.items()))
                            if child_key in self._succ_h_cache:
                                h_child_succ = self._succ_h_cache[child_key]
                            else:
                                original_state = self.state
                                self.state = next_state
                                child_succ = self.get_successors()
                                self.state = original_state
                                h_child_succ = [self.heuristic(s[0]) for s in child_succ]
                                self._succ_h_cache[child_key] = h_child_succ
                            # 一致性区间估计
                            child_h = h_orig
                            if child_h <= 0:
                                k_best, k_worst = 1.0, 1.0
                            else:
                                k_min = max(0.0, (parent_h_adj - 1.0) / child_h)
                                k_max = min((1.0 + hm) / child_h for hm in (h_child_succ or [child_h]))
                                # 置信度阈值与安全边距
                                high_th, low_th, delta = 0.8, 0.5, 0.01
                                best_conf = float(result.get('best_confidence', 0.0)) if 'best_confidence' in result else 0.0
                                worst_conf = float(result.get('worst_confidence', 0.0)) if 'worst_confidence' in result else 0.0
                                # 对高置信度：允许突破一致性约束
                                if best_conf >= high_th:
                                    k_best = 0.5
                                elif best_conf <= low_th:
                                    k_best = max(k_min + delta, 0.01)
                                else:
                                    k_best = max(k_min + 2*delta, 0.01)
                                if worst_conf >= high_th:
                                    k_worst = 2.0
                                elif worst_conf <= low_th:
                                    k_worst = max(1.0, k_max - delta)
                                else:
                                    k_worst = max(1.0, k_max - 2*delta)
                        else:
                            # 不考虑动态估计时使用固定值
                            k_best, k_worst = 0.667, 1.5
                    else:
                        k_best, k_worst = 0.667, 1.5

                    if i in weights:
                        w = weights[i]
                        next_state.g -= w
                        # 将线性权重融合到 k_best 中（保持直观影响）
                        next_state.h_adjusted = h_orig * (k_best + (1.0 - w) * (1.0 - k_best))
                    elif i == best_action:
                        next_state.g -= 1.0
                        next_state.h_adjusted = h_orig * k_best
                    elif i == worst_action:
                        next_state.h_adjusted = h_orig * k_worst
                    elif i == rule_best:
                        # 通用规则视为置信度=1，可突破一致性
                        next_state.g -= 2.0
                        next_state.h_adjusted = h_orig * 0.5
                    elif i == rule_worst:
                        # 通用规则视为置信度=1，可突破一致性
                        next_state.h_adjusted = h_orig * 2.0
                    else:
                        next_state.h_adjusted = h_orig
                    next_state.cost = next_state.g + next_state.h_adjusted
                    if next_state not in visited:
                        new_path = path + [action]
                        heapq.heappush(queue, (next_state.cost, len(new_path), next_state, new_path))
            else:
                # 无引导：常规 A*
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.h_adjusted = next_state.h
                    next_state.cost = next_state.g + next_state.h
                    if next_state not in visited:
                        new_path = path + [action]
                        heapq.heappush(queue, (next_state.cost, len(new_path), next_state, new_path))

            # 每1000节点打印一次进度
            if count % 1000 == 0:
                elapsed = time.time() - start_time
                self.log(f"进度：{count}/{self.max_nodes} 节点 (elapsed={elapsed:.1f}s)")

        # 结束
        elapsed = time.time() - start_time
        if count >= self.max_nodes:
            self.log(f"❌ 达到最大节点限制（{self.max_nodes}），未找到解")
        else:
            self.log(f"❌ 搜索空间耗尽，未找到解")
        self.log(f"搜索节点数：{count}；耗时：{elapsed:.2f}s")
        return None, count

