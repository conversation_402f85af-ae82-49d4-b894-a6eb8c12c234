"""
改进的CNN-Transformer模型
专门为Blocks World设计的架构
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, List
import numpy as np




class BlocksWorldEncoder:
    """
    Blocks World 状态编码器（单通道、从栈顶计数）
    - 编码输出: matrix shape = (n_blocks, n_stacks)
      值含义: 0=不在该栈；1=栈顶；n>1=距离栈顶的层数
    - 动作编码: 针对前 (n_stacks-1) 个互相移动（若保留一个缓冲栈，动作空间按实际可用栈数计算）
    """
    def __init__(self, n_blocks: int = 15, n_stacks: int = 6):
        self.n_blocks = n_blocks
        self.n_stacks = n_stacks
        self.block_names = [chr(ord('A') + i) for i in range(n_blocks)]

    def encode_state(self, state: Dict[str, List[str]]):
        matrix = np.zeros((self.n_blocks, self.n_stacks), dtype=np.float32)
        for stack_idx, stack_name in enumerate(sorted(state.keys())):
            if stack_idx >= self.n_stacks:
                break
            blocks = state[stack_name]
            h = len(blocks)
            for pos, block in enumerate(blocks):
                if block in self.block_names:
                    bidx = self.block_names.index(block)
                    matrix[bidx, stack_idx] = h - pos  # 顶=1
        return matrix

    def decode_state(self, matrix):
        state = {}
        for s in range(self.n_stacks):
            name = f'stack{s+1}'
            items = []
            for bidx in range(self.n_blocks):
                h = matrix[bidx, s]
                if h > 0:
                    items.append((self.block_names[bidx], h))
            items.sort(key=lambda x: x[1], reverse=True)  # 底->顶
            state[name] = [b for b, _ in items]
        return state

    def get_movable_blocks(self, matrix):
        movable = []
        for b in range(self.n_blocks):
            for s in range(self.n_stacks):
                if matrix[b, s] == 1:
                    movable.append((b, s))
        return movable

    def encode_action(self, from_stack: int, to_stack: int) -> int:
        assert from_stack != to_stack
        if to_stack > from_stack:
            adjusted_to = to_stack - 1
        else:
            adjusted_to = to_stack
        return from_stack * (self.n_stacks - 1) + adjusted_to

    def decode_action(self, action_id: int):
        from_stack = action_id // (self.n_stacks - 1)
        adjusted_to = action_id % (self.n_stacks - 1)
        to_stack = adjusted_to + 1 if adjusted_to >= from_stack else adjusted_to
        return from_stack, to_stack

    def apply_action(self, matrix, from_stack: int, to_stack: int):
        new_m = matrix.copy()
        movable_block = None
        for b in range(self.n_blocks):
            if new_m[b, from_stack] == 1:
                movable_block = b
                break
        if movable_block is None:
            return new_m
        # 移出源栈顶
        new_m[movable_block, from_stack] = 0
        for b in range(self.n_blocks):
            if new_m[b, from_stack] > 1:
                new_m[b, from_stack] -= 1
        # 放入目标栈顶
        target_h = 0
        for b in range(self.n_blocks):
            target_h = max(target_h, new_m[b, to_stack])
        new_m[movable_block, to_stack] = target_h + 1
        for b in range(self.n_blocks):
            if b != movable_block and new_m[b, to_stack] > 0:
                new_m[b, to_stack] += 1
        return new_m

class ImprovedBlocksWorldCNN(nn.Module):
    """
    改进的CNN架构，专门为Blocks World设计
    
    关键设计：
    1. 使用非对称卷积核（垂直和水平分别处理）
    2. 避免过度池化，保留精确位置信息
    3. 使用注意力机制增强重要特征
    4. 分阶段处理：栈内关系 -> 栈间关系 -> 全局整合
    """
    def __init__(
        self,
        n_layers: int = 22,      # 输入层数（目标+当前+20个后继）
        n_blocks: int = 15,      # 块数
        n_stacks: int = 6,       # 栈数
        embed_dim: int = 128,    # 嵌入维度
        n_heads: int = 8,        # 注意力头数
        n_transformer_layers: int = 3,  # Transformer层数
        n_classes: int = 20,     # 输出类别数（20个动作）
        dropout_rate: float = 0.1
    ):
        super().__init__()
        
        self.n_layers = n_layers
        self.n_blocks = n_blocks
        self.n_stacks = n_stacks
        self.embed_dim = embed_dim
        
        # ========== 阶段1：栈内特征提取 ==========
        # 使用垂直卷积核，专注于栈内关系
        self.stack_conv1 = nn.Conv2d(
            n_layers, 64,
            kernel_size=(5, 1),  # 5×1卷积：捕捉5个块的垂直关系
            padding=(2, 0)
        )
        self.stack_bn1 = nn.BatchNorm2d(64)
        self.stack_dropout1 = nn.Dropout2d(dropout_rate)
        
        self.stack_conv2 = nn.Conv2d(
            64, 128,
            kernel_size=(3, 1),  # 3×1卷积：细化垂直特征
            padding=(1, 0)
        )
        self.stack_bn2 = nn.BatchNorm2d(128)
        self.stack_dropout2 = nn.Dropout2d(dropout_rate)
        
        # ========== 阶段2：栈间关系建模 ==========
        # 使用水平卷积核，学习栈之间的模式
        self.cross_stack_conv = nn.Conv2d(
            128, 128,
            kernel_size=(1, 3),  # 1×3卷积：捕捉相邻栈的关系
            padding=(0, 1)
        )
        self.cross_stack_bn = nn.BatchNorm2d(128)
        self.cross_stack_dropout = nn.Dropout2d(dropout_rate)
        
        # ========== 阶段3：局部-全局整合 ==========
        # 使用多尺度卷积捕捉不同范围的模式
        self.multi_scale_conv1 = nn.Conv2d(
            128, 256,
            kernel_size=3,
            padding=2,
            dilation=2  # 空洞卷积，感受野5×5
        )
        self.multi_scale_bn1 = nn.BatchNorm2d(256)
        
        # 1×1卷积用于特征融合
        self.fusion_conv = nn.Conv2d(256, embed_dim, kernel_size=1)
        self.fusion_bn = nn.BatchNorm2d(embed_dim)
        
        # ========== 空间注意力机制 ==========
        self.spatial_attention = SpatialAttention(embed_dim)
        
        # ========== Transformer编码器 ==========
        # 将CNN特征送入Transformer进行全局建模
        self.pos_embedding = nn.Parameter(
            torch.zeros(1, n_blocks * n_stacks, embed_dim)
        )
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=n_heads,
            dim_feedforward=embed_dim * 4,
            dropout=dropout_rate,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(
            encoder_layer,
            num_layers=n_transformer_layers
        )
        
        # ========== 分类头 ==========
        self.classifier_dropout = nn.Dropout(dropout_rate)
        
        # 双头输出：best和worst动作
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        # 位置编码使用正态分布初始化
        nn.init.normal_(self.pos_embedding, std=0.02)
        
        # 卷积层使用Kaiming初始化
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量，shape (batch, n_layers, n_blocks, n_stacks)
        
        Returns:
            best_logits: 最佳动作的logits，shape (batch, n_classes)
            worst_logits: 最差动作的logits，shape (batch, n_classes)
        """
        batch_size = x.size(0)
        
        # ========== CNN特征提取 ==========
        # 阶段1：栈内特征
        x = F.relu(self.stack_bn1(self.stack_conv1(x)))
        x = self.stack_dropout1(x)
        x = F.relu(self.stack_bn2(self.stack_conv2(x)))
        x = self.stack_dropout2(x)
        
        # 阶段2：栈间关系
        x = F.relu(self.cross_stack_bn(self.cross_stack_conv(x)))
        x = self.cross_stack_dropout(x)
        
        # 阶段3：多尺度整合
        x = F.relu(self.multi_scale_bn1(self.multi_scale_conv1(x)))
        x = F.relu(self.fusion_bn(self.fusion_conv(x)))
        
        # 应用空间注意力
        x = self.spatial_attention(x)
        
        # ========== 准备Transformer输入 ==========
        # x shape: (batch, embed_dim, n_blocks, n_stacks)
        x = x.flatten(2)  # (batch, embed_dim, n_blocks*n_stacks)
        x = x.transpose(1, 2)  # (batch, n_blocks*n_stacks, embed_dim)
        
        # 添加位置编码
        x = x + self.pos_embedding
        
        # ========== Transformer编码 ==========
        x = self.transformer(x)
        
        # ========== 全局池化 ==========
        # 使用平均池化获得全局特征
        x = x.mean(dim=1)  # (batch, embed_dim)
        
        # ========== 分类 ==========
        x = self.classifier_dropout(x)
        best_logits = self.fc_best(x)
        worst_logits = self.fc_worst(x)
        
        return best_logits, worst_logits


class SpatialAttention(nn.Module):
    """
    空间注意力模块
    学习空间位置的重要性权重
    """
    def __init__(self, in_channels: int):
        super().__init__()
        
        # 使用两个1×1卷积生成注意力权重
        self.conv1 = nn.Conv2d(in_channels, in_channels // 8, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels // 8, 1, kernel_size=1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: shape (batch, channels, height, width)
        
        Returns:
            attended_x: 应用注意力后的特征
        """
        # 生成注意力权重
        attention = F.relu(self.conv1(x))
        attention = torch.sigmoid(self.conv2(attention))
        
        # 应用注意力
        return x * attention


class StackSpecificCNN(nn.Module):
    """
    栈特定的CNN架构（替代方案）
    每个栈使用独立的编码器，然后融合
    """
    def __init__(
        self,
        n_layers: int = 22,
        n_blocks: int = 15,
        n_stacks: int = 6,
        embed_dim: int = 128,
        n_classes: int = 20,
        dropout_rate: float = 0.1
    ):
        super().__init__()
        
        self.n_stacks = n_stacks
        self.n_blocks = n_blocks
        
        # 为每个栈创建独立的1D卷积编码器
        self.stack_encoders = nn.ModuleList([
            nn.Sequential(
                # 1D卷积处理单个栈
                nn.Conv1d(n_layers, 32, kernel_size=3, padding=1),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                
                nn.Conv1d(32, 64, kernel_size=3, padding=1),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                
                nn.Conv1d(64, embed_dim // n_stacks, kernel_size=3, padding=1),
                nn.BatchNorm1d(embed_dim // n_stacks),
                nn.ReLU(),
            ) for _ in range(n_stacks)
        ])
        
        # 融合所有栈的特征
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim * n_blocks, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # 分类头
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: shape (batch, n_layers, n_blocks, n_stacks)
        """
        batch_size = x.size(0)
        
        # 分别处理每个栈
        stack_features = []
        for i in range(self.n_stacks):
            # 提取第i个栈的数据
            stack_data = x[:, :, :, i]  # (batch, n_layers, n_blocks)
            
            # 通过对应的编码器
            stack_feat = self.stack_encoders[i](stack_data)  # (batch, embed_dim//n_stacks, n_blocks)
            stack_features.append(stack_feat)
        
        # 合并所有栈的特征
        combined = torch.cat(stack_features, dim=1)  # (batch, embed_dim, n_blocks)
        combined = combined.flatten(1)  # (batch, embed_dim * n_blocks)
        
        # 融合
        features = self.fusion(combined)  # (batch, embed_dim)
        
        # 分类
        best_logits = self.fc_best(features)
        worst_logits = self.fc_worst(features)
        
        return best_logits, worst_logits


def test_models():
    """测试模型的前向传播"""
    print("=" * 70)
    print("测试改进的CNN模型")
    print("=" * 70)
    
    # 创建模型
    model1 = ImprovedBlocksWorldCNN(
        n_layers=22,
        n_blocks=15,
        n_stacks=6,
        embed_dim=128,
        n_heads=8,
        n_transformer_layers=3,
        n_classes=20
    )
    
    model2 = StackSpecificCNN(
        n_layers=22,
        n_blocks=15,
        n_stacks=6,
        embed_dim=128,
        n_classes=20
    )
    
    # 创建测试输入
    batch_size = 2
    dummy_input = torch.randn(batch_size, 22, 15, 6)
    
    print(f"\n输入形状: {dummy_input.shape}")
    print("  - batch_size: 2")
    print("  - n_layers: 22 (目标+当前+20个后继)")
    print("  - n_blocks: 15")
    print("  - n_stacks: 6")
    
    # 测试前向传播
    with torch.no_grad():
        best1, worst1 = model1(dummy_input)
        best2, worst2 = model2(dummy_input)
    
    print(f"\nImprovedBlocksWorldCNN输出:")
    print(f"  - best_logits: {best1.shape}")
    print(f"  - worst_logits: {worst1.shape}")
    
    print(f"\nStackSpecificCNN输出:")
    print(f"  - best_logits: {best2.shape}")
    print(f"  - worst_logits: {worst2.shape}")
    
    # 参数统计
    total_params1 = sum(p.numel() for p in model1.parameters())
    total_params2 = sum(p.numel() for p in model2.parameters())
    
    print(f"\n模型参数量:")
    print(f"  - ImprovedBlocksWorldCNN: {total_params1:,}")
    print(f"  - StackSpecificCNN: {total_params2:,}")
    
    print("\n✓ 模型测试通过！")


if __name__ == "__main__":
    test_models()
