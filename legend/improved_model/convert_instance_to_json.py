#!/usr/bin/env python3
"""
Convert instance description files (.txt) to LLM guidance JSON format.

This script converts Blocks World problem instance files from text format to JSON format
that can be used with the LLM guidance system.

Input format (.txt):
    Instance ID: 2
    Difficulty: 15
    Num Stacks: 6
    Num Blocks: 15
    Start State: {'Stack1': [9], 'Stack2': [], ...}
    G_canonical: {'Stack1': [], 'Stack2': [], ...}
    Fix Order: ['Stack5', 'Stack6', ...]

Output format (.json):
    [
      {
        "id": 2,
        "difficulty": 15,
        "num_stacks": 6,
        "num_blocks": 15,
        "start_state": {"Stack1": [9], "Stack2": [], ...},
        "goal_state": {"Stack1": [], "Stack2": [], ...},
        "fix_order": ["Stack5", "Stack6", ...]
      }
    ]

Usage:
    # Convert single file
    python convert_instance_to_json.py --input data/instances/instance_002.txt --output instance_002.json

    # Convert all instance files in directory
    python convert_instance_to_json.py --input_dir data/instances --output all_instances.json

    # Convert with custom pattern
    python convert_instance_to_json.py --input_dir data/instances --output selected.json --pattern "instance_0*.txt"

Features:
    - Handles both complete and minimal instance file formats
    - Automatically infers missing fields (ID from filename, difficulty from block count, etc.)
    - Sorts instances by ID for consistent output
    - Validates input and provides helpful error messages
"""

import argparse
import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any


def parse_instance_file(file_path: str) -> Dict[str, Any]:
    """
    Parse a single instance file and extract the required information.

    Args:
        file_path: Path to the instance .txt file

    Returns:
        Dictionary containing parsed instance data
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()

    # Initialize result dictionary
    instance_data = {}

    # Try to extract ID from filename if not found in content
    filename = Path(file_path).name
    id_match = re.search(r'instance_(\d+)', filename)
    if id_match:
        default_id = int(id_match.group(1))
    else:
        default_id = 0

    # Parse each line
    for line in content.split('\n'):
        line = line.strip()
        if not line or line.startswith('---'):
            continue

        if line.startswith('Instance ID:'):
            instance_data['id'] = int(line.split(':')[1].strip())
        elif line.startswith('Difficulty:'):
            instance_data['difficulty'] = int(line.split(':')[1].strip())
        elif line.startswith('Num Stacks:'):
            instance_data['num_stacks'] = int(line.split(':')[1].strip())
        elif line.startswith('Num Blocks:'):
            instance_data['num_blocks'] = int(line.split(':')[1].strip())
        elif line.startswith('Start State:'):
            # Extract the dictionary part after "Start State: "
            state_str = line.split('Start State: ')[1]
            instance_data['start_state'] = eval(state_str)  # Safe since we control the format
        elif line.startswith('G_canonical:'):
            # Extract the dictionary part after "G_canonical: "
            goal_str = line.split('G_canonical: ')[1]
            instance_data['goal_state'] = eval(goal_str)  # Safe since we control the format
        elif line.startswith('Fix Order:'):
            # Extract the list part after "Fix Order: "
            order_str = line.split('Fix Order: ')[1]
            instance_data['fix_order'] = eval(order_str)  # Safe since we control the format

    # Fill in missing fields with defaults
    if 'id' not in instance_data:
        instance_data['id'] = default_id

    if 'start_state' in instance_data and 'goal_state' in instance_data:
        # Calculate missing fields from the state data
        if 'num_stacks' not in instance_data:
            instance_data['num_stacks'] = len(instance_data['start_state'])

        if 'num_blocks' not in instance_data:
            # Count total blocks in start state
            total_blocks = sum(len(stack) for stack in instance_data['start_state'].values())
            instance_data['num_blocks'] = total_blocks

        if 'difficulty' not in instance_data:
            # Use total blocks as a simple difficulty measure
            instance_data['difficulty'] = instance_data['num_blocks']

        if 'fix_order' not in instance_data:
            # Default fix order: all stacks in order
            stack_names = sorted(instance_data['start_state'].keys())
            instance_data['fix_order'] = stack_names

    return instance_data


def convert_single_file(input_file: str, output_file: str) -> None:
    """
    Convert a single instance file to JSON format.
    
    Args:
        input_file: Path to input .txt file
        output_file: Path to output .json file
    """
    print(f"Converting {input_file} to {output_file}")
    
    # Parse the instance file
    instance_data = parse_instance_file(input_file)
    
    # Create a list with single instance (to match the expected JSON format)
    json_data = [instance_data]
    
    # Write to JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully converted to {output_file}")


def convert_directory(input_dir: str, output_file: str, pattern: str = "instance_*.txt") -> None:
    """
    Convert all instance files in a directory to a single JSON file.
    
    Args:
        input_dir: Directory containing .txt instance files
        output_file: Path to output .json file
        pattern: File pattern to match (default: "instance_*.txt")
    """
    print(f"Converting all {pattern} files from {input_dir} to {output_file}")
    
    input_path = Path(input_dir)
    if not input_path.exists():
        raise FileNotFoundError(f"Input directory {input_dir} does not exist")
    
    # Find all matching files
    instance_files = list(input_path.glob(pattern))
    if not instance_files:
        print(f"No files matching pattern {pattern} found in {input_dir}")
        return
    
    # Sort files by instance ID for consistent ordering
    instance_files.sort(key=lambda x: int(re.search(r'instance_(\d+)', x.name).group(1)) if re.search(r'instance_(\d+)', x.name) else 0)
    
    # Parse all instance files
    all_instances = []
    for file_path in instance_files:
        try:
            instance_data = parse_instance_file(str(file_path))
            all_instances.append(instance_data)
            print(f"  Parsed {file_path.name} (ID: {instance_data.get('id', 'unknown')})")
        except Exception as e:
            print(f"  Error parsing {file_path.name}: {e}")
            continue
    
    if not all_instances:
        print("No instances were successfully parsed")
        return
    
    # Write to JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_instances, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully converted {len(all_instances)} instances to {output_file}")


def main():
    parser = argparse.ArgumentParser(description="Convert instance files to LLM guidance JSON format")
    
    # Input options
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--input', '-i', type=str, help="Input .txt file path")
    group.add_argument('--input_dir', '-d', type=str, help="Input directory containing .txt files")
    
    # Output options
    parser.add_argument('--output', '-o', type=str, required=True, help="Output .json file path")
    
    # Additional options for directory mode
    parser.add_argument('--pattern', '-p', type=str, default="instance_*.txt", 
                       help="File pattern to match in directory mode (default: instance_*.txt)")
    
    args = parser.parse_args()
    
    try:
        if args.input:
            # Single file mode
            if not os.path.exists(args.input):
                raise FileNotFoundError(f"Input file {args.input} does not exist")
            convert_single_file(args.input, args.output)
        else:
            # Directory mode
            convert_directory(args.input_dir, args.output, args.pattern)
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
