[{"id": 1, "start_state": {"Stack1": [12, 13, 2, 5], "Stack2": [10, 8, 11], "Stack3": [4, 6, 1], "Stack4": [15, 7], "Stack5": [9, 14], "Stack6": [3]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [], "Stack6": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack6", "Stack1"], "difficulty": 15, "num_stacks": 6, "num_blocks": 15}, {"id": 2, "start_state": {"Stack1": [9], "Stack2": [], "Stack3": [5], "Stack4": [6, 4, 15, 7], "Stack5": [2, 3, 12, 8], "Stack6": [14, 13, 11, 1, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [], "Stack6": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack6", "Stack3", "Stack4", "Stack1", "Stack2"], "difficulty": 15, "num_stacks": 6, "num_blocks": 15}]