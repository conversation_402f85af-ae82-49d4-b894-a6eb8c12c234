# Instance File Converter

这个脚本用于将Blocks World问题实例文件从文本格式转换为LLM guidance系统可用的JSON格式。

## 功能特性

- 支持单文件和批量转换
- 自动处理不完整的实例文件（从文件名推断ID，从状态数据计算缺失字段）
- 按实例ID排序输出
- 提供详细的错误信息和转换状态

## 使用方法

### 1. 转换单个文件

```bash
python convert_instance_to_json.py --input data/instances/instance_002.txt --output instance_002.json
```

### 2. 批量转换目录中的所有实例文件

```bash
python convert_instance_to_json.py --input_dir data/instances --output all_instances.json
```

### 3. 使用自定义文件模式

```bash
python convert_instance_to_json.py --input_dir data/instances --output selected.json --pattern "instance_0*.txt"
```

## 输入格式

脚本支持两种输入格式：

### 完整格式
```
Instance ID: 2
Difficulty: 15
Num Stacks: 6
Num Blocks: 15
Start State: {'Stack1': [9], 'Stack2': [], 'Stack3': [5], ...}
G_canonical: {'Stack1': [], 'Stack2': [], 'Stack3': [], ...}
Fix Order: ['Stack5', 'Stack6', 'Stack3', 'Stack4', 'Stack1', 'Stack2']
```

### 简化格式
```
Start State: {'Stack1': [12, 13, 6, 2, 1], 'Stack2': [3, 5], ...}
G_canonical: {'Stack1': [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], ...}
Fix Order: ['Stack1', 'Stack2', 'Stack3', 'Stack4', 'Stack5']
```

对于简化格式，脚本会自动：
- 从文件名提取实例ID（如 `instance_28.txt` → ID: 28）
- 从状态数据计算栈数和块数
- 使用块数作为难度值
- 如果缺少Fix Order，则使用栈名的字母顺序

## 输出格式

生成的JSON文件格式与现有的 `random_instances.json` 兼容：

```json
[
  {
    "id": 2,
    "difficulty": 15,
    "num_stacks": 6,
    "num_blocks": 15,
    "start_state": {
      "Stack1": [9],
      "Stack2": [],
      "Stack3": [5],
      ...
    },
    "goal_state": {
      "Stack1": [],
      "Stack2": [],
      "Stack3": [],
      ...
    },
    "fix_order": [
      "Stack5",
      "Stack6",
      "Stack3",
      "Stack4",
      "Stack1",
      "Stack2"
    ]
  }
]
```

## 示例

### 转换现有实例文件
```bash
# 转换单个实例
python convert_instance_to_json.py -i data/instances/instance_28.txt -o instance_28.json

# 转换所有实例文件
python convert_instance_to_json.py -d data/instances -o converted_instances.json
```

### 验证转换结果
转换完成后，生成的JSON文件可以直接用于LLM guidance系统：

```python
import json

# 加载转换后的实例
with open('converted_instances.json', 'r') as f:
    instances = json.load(f)

# 使用第一个实例
instance = instances[0]
print(f"Instance {instance['id']}: {instance['num_blocks']} blocks, {instance['num_stacks']} stacks")
```

## 错误处理

脚本会处理以下情况：
- 文件不存在或无法读取
- 格式不正确的实例文件
- 缺失的必要字段
- 无效的状态数据

所有错误都会显示详细的错误信息，帮助定位和解决问题。
