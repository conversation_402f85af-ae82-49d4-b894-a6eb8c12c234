"""
Render a Blocks-World multi-stack solution JSON into a GIF (fixed canvas size).

Usage:
  python -m multi_stack_framework.tools.render_solution_gif \
    --input outputs/sample_solution.json \
    --output outputs/sample_solution.gif \
    --fps 2

JSON format (version 1): see exporter for details.

Notes on stability:
- Figure size and axes limits are fixed across frames to avoid visual jumping.
- Colors are consistent per block.
"""
import argparse
import json
import os
from typing import List, Dict, Any, Tuple

import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle


def infer_blocks(trajectory: List[Dict[str, Any]], initial: List[List[str]], goal: List[List[str]]) -> List[str]:
    s = set()
    for stack in initial:
        s.update(stack)
    for stack in goal:
        s.update(stack)
    for frame in trajectory:
        st = frame.get("state", [])
        for stack in st:
            s.update(stack)
    blocks = sorted(list(s))
    return blocks


def build_color_map(blocks: List[str]) -> Dict[str, Tuple[float, float, float]]:
    cmap = plt.get_cmap('tab10')
    colors = {}
    for i, b in enumerate(blocks):
        colors[b] = cmap(i % 10)
    return colors


def compute_global_dims(frames_states: List[List[List[str]]]) -> Tuple[int, int]:
    n_stacks = len(frames_states[0]) if frames_states else 0
    max_h = 0
    for st in frames_states:
        for stack in st:
            if len(stack) > max_h:
                max_h = len(stack)
    return n_stacks, max_h


def draw_state(ax, state: List[List[str]], colors: Dict[str, Tuple[float, float, float]],
               title: str,
               n_stacks: int, max_h: int,
               stack_width: float = 1.0, gap: float = 0.4, block_h: float = 0.6):
    ax.clear()
    # Draw stacks
    for si in range(n_stacks):
        x0 = si * (stack_width + gap)
        ax.add_line(plt.Line2D([x0, x0 + stack_width], [0, 0], color='black', linewidth=2))
        stack = state[si] if si < len(state) else []
        for level, block in enumerate(stack):
            y = level * (block_h + 0.1) + 0.1
            rect = Rectangle((x0, y), stack_width, block_h,
                             facecolor=colors.get(block, (0.7, 0.7, 0.7)), edgecolor='black')
            ax.add_patch(rect)
            ax.text(x0 + stack_width / 2, y + block_h / 2, block,
                    ha='center', va='center', color='white', fontsize=10, weight='bold')
    # Fixed limits
    total_w = n_stacks * (stack_width + gap) - gap
    ax.set_xlim(-0.2, total_w + 0.2)
    ax.set_ylim(0, max(1.0, max_h * (block_h + 0.1) + 0.4))
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title(title)


def save_gif(frames_states: List[List[List[str]]], actions: List[str], blocks: List[str], out_path: str,
             fps: int = 2, title_prefix: str = ""):
    from matplotlib.animation import PillowWriter, FuncAnimation
    colors = build_color_map(blocks)
    n_stacks, max_h = compute_global_dims(frames_states)

    # Fixed figure size based on stacks only (constant across frames)
    fig_w = max(4, n_stacks * 1.5)
    fig_h = max(3.5, max_h * 0.9)
    fig, ax = plt.subplots(figsize=(fig_w, fig_h))

    def update(i):
        note = actions[i-1] if i > 0 and i-1 < len(actions) else "initial"
        draw_state(ax, frames_states[i], colors, f"{title_prefix} {note}", n_stacks, max_h)
        return []

    # interval in ms; convert fps to interval safely
    interval_ms = int(1000 / max(1, fps))
    anim = FuncAnimation(fig, update, frames=len(frames_states), interval=interval_ms, blit=False)
    try:
        writer = PillowWriter(fps=fps)
        os.makedirs(os.path.dirname(out_path) or '.', exist_ok=True)
        anim.save(out_path, writer=writer)
        print(f"GIF saved to {out_path}")
    except Exception as e:
        print(f"Failed to write GIF ({e}); saving PNG frames instead...")
        base, _ = os.path.splitext(out_path)
        os.makedirs(base, exist_ok=True)
        for i, st in enumerate(frames_states):
            note = actions[i-1] if i > 0 and i-1 < len(actions) else "initial"
            draw_state(ax, st, colors, f"{title_prefix} {note}", n_stacks, max_h)
            png_path = os.path.join(base, f"frame_{i:03d}.png")
            plt.savefig(png_path)
            print(f"Saved {png_path}")
    finally:
        plt.close(fig)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--input', required=True, help='Path to solution JSON file')
    parser.add_argument('--output', required=True, help='Path to output GIF, e.g., outputs/solution.gif')
    parser.add_argument('--fps', type=int, default=2)
    parser.add_argument('--title', default='')
    args = parser.parse_args()

    with open(args.input, 'r', encoding='utf-8') as f:
        data = json.load(f)

    if data.get('version', 1) != 1:
        print('Warning: Unknown JSON version; proceeding as v1')

    problem = data['problem']
    solution = data['solution']
    trajectory = solution['trajectory']

    initial = problem.get('initial', trajectory[0].get('state'))
    goal = problem.get('goal', trajectory[-1].get('state'))
    blocks = problem.get('blocks') or infer_blocks(trajectory, initial, goal)

    # Prepare frames and action labels
    frames_states: List[List[List[str]]] = []
    actions: List[str] = []
    for i, frame in enumerate(trajectory):
        st = frame['state']
        frames_states.append(st)
        if i > 0:
            actions.append(frame.get('action', ''))
    save_gif(frames_states, actions, blocks, args.output, fps=args.fps, title_prefix=args.title)


if __name__ == '__main__':
    main()

