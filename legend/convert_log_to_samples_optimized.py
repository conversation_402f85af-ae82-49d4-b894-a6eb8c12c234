import sys
import os
import re
import json
import numpy as np
import pandas as pd
import ast

# 添加路径（相对 legend 目录）
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)

# 内联所需的日志解析与辅助函数，避免引入重依赖
# parse_log — 从日志中提取 current_state/goal_state/fix_stack_id/attention_block_name/best_action/worst_action
import re as _re
import ast as _ast

def parse_log(log_content):
    lines = log_content.split('\n')
    samples = []
    current_sample = None
    for line in lines:
        line = line.strip()
        if line.startswith("Node ") and "Current fix stack:" in line:
            # 遇到新的节点，若上一个样本已完整，则先提交
            if current_sample and current_sample.get("current_state") is not None and \
               current_sample.get("goal_state") is not None and \
               (current_sample.get("best_action") is not None or current_sample.get("worst_action") is not None):
                samples.append(current_sample)
            node_id_str = _re.search(r'Node (\d+):', line)
            node_id = int(node_id_str.group(1)) if node_id_str else None
            fix_stack_str = _re.search(r'Current fix stack: \*\*Stack(\d+)\*\*', line)
            fix_stack_id = int(fix_stack_str.group(1)) if fix_stack_str else None
            # 开启新样本容器（先保留 None 字段，后续逐步填充）
            current_sample = {
                "node": node_id,
                "current_state": None,
                "goal_state": None,
                "fix_stack_id": fix_stack_id,
                "attention_block_name": None,
                "priority_task_description": "",
                "best_action": None,
                "worst_action": None,
            }

        elif "Current state:" in line:
            try:
                m = _re.search(r"\{.*\}", line)
                if m:
                    current_state = _ast.literal_eval(m.group(0))
                    if current_sample is not None:
                        current_sample["current_state"] = current_state
            except Exception:
                pass
        elif "Goal state:" in line or "Goal State:" in line or "Goal:" in line:
            try:
                m = _re.search(r"\{.*\}", line)
                if m:
                    goal_state = _ast.literal_eval(m.group(0))
                    if current_sample is not None:
                        current_sample["goal_state"] = goal_state
            except Exception:
                pass
        elif "Attention Block:" in line:
            m = _re.search(r"Attention Block:\s*([A-Za-z0-9_]+)", line)
            if m and current_sample is not None:
                current_sample["attention_block_name"] = m.group(1)
        elif "Priority task:" in line or "优先任务" in line:
            if current_sample is not None:
                current_sample["priority_task_description"] = line.split(":",1)[1].strip()
        elif "Best Action" in line:
            try:
                m = _re.search(r'\(([^)]+)\)', line)
                if m and current_sample is not None:
                    action = tuple(int(x.strip()) for x in m.group(1).split(',') if x.strip())
                    current_sample["best_action"] = action
                    # 若样本信息已完整，立即提交并重置
                    if current_sample.get("current_state") is not None and current_sample.get("goal_state") is not None:
                        samples.append(current_sample)
                        current_sample = None
            except Exception:
                pass
        elif "Worst Action" in line:
            try:
                m = _re.search(r'\(([^)]+)\)', line)
                if m and current_sample is not None:
                    action = tuple(int(x.strip()) for x in m.group(1).split(',') if x.strip())
                    current_sample["worst_action"] = action
                    if current_sample.get("current_state") is not None and current_sample.get("goal_state") is not None:
                        samples.append(current_sample)
                        current_sample = None
            except Exception:
                pass
        elif line == "":
            if current_sample and current_sample.get("goal_state") and \
               (current_sample.get("best_action") is not None or current_sample.get("worst_action") is not None):
                samples.append(current_sample)
            current_sample = None
    if current_sample and current_sample.get("goal_state") and \
       (current_sample.get("best_action") is not None or current_sample.get("worst_action") is not None):
        samples.append(current_sample)
    return samples

def apply_amplification(matrix_to_modify, original_reference_matrix, row_index, coefficient):
    if 0 <= row_index < matrix_to_modify.shape[0]:
        matrix_to_modify[row_index, :] = np.where(
            original_reference_matrix[row_index, :] == 1,
            coefficient,
            original_reference_matrix[row_index, :]
        )


def get_stable_prefix_and_suffixes(current_blocks, goal_blocks):
    stable_prefix = []
    len_stable = 0
    cb = current_blocks if isinstance(current_blocks, list) else []
    gb = goal_blocks if isinstance(goal_blocks, list) else []
    for i in range(min(len(cb), len(gb))):
        if cb[i] == gb[i]:
            stable_prefix.append(cb[i])
            len_stable += 1
        else:
            break
    current_problematic_suffix = cb[len_stable:]
    future_target_suffix = gb[len_stable:]
    return stable_prefix, current_problematic_suffix, future_target_suffix

class OptimizedBlocksWorldPlanner:
    """优化的Blocks World规划器，生成适合新CNN架构的数据"""
    
    def __init__(self, start_state, goal_state, fix_order, n_stacks=5, max_blocks=15, buffer_rows=0, buffer_cols=1):
        self.start_state = start_state
        self.goal_state = goal_state
        self.fix_order = fix_order
        
        # 新的架构参数
        self.n_stacks = n_stacks
        self.max_blocks = max_blocks
        self.buffer_rows = buffer_rows
        self.buffer_cols = buffer_cols
        
        # 计算矩阵尺寸
        self.n_rows = max_blocks + buffer_rows
        self.n_cols = n_stacks + buffer_cols
        self.n_layers = 22  # 保持与原始设计一致
        
        # 提取块和栈信息
        self.blocks, self.stacks = self._get_blocks_and_stacks(start_state)
        
        print(f"优化规划器初始化: n_stacks={n_stacks}, max_blocks={max_blocks}")
        print(f"矩阵尺寸: ({self.n_layers}, {self.n_rows}, {self.n_cols})")
        print(f"实际块数: {len(self.blocks)}, 实际栈数: {len(self.stacks)}")
    
    def _get_blocks_and_stacks(self, state):
        """从状态中提取块和栈列表"""
        stacks = sorted(state.keys())
        blocks = set()
        for stack in state.values():
            blocks.update(stack)
        blocks = sorted(list(blocks))
        return blocks, stacks
    
    def state_to_matrix(self, state):
        """将状态转换为优化的矩阵形式"""
        matrix = np.zeros((self.n_rows, self.n_cols))
        
        # 只处理前n_stacks个栈
        active_stacks = self.stacks[:self.n_stacks]
        
        for stack_idx, stack_name in enumerate(active_stacks):
            if stack_name not in state or not state[stack_name]:
                continue
                
            stack = state[stack_name]
            
            # 处理栈中的每个块
            for pos, block in enumerate(stack):
                if block in self.blocks:
                    block_idx = self.blocks.index(block)
                    if block_idx < self.max_blocks:  # 只处理前max_blocks个块
                        # 在对应的栈列中标记块的位置
                        if pos < self.n_rows:
                            matrix[pos, stack_idx] = 1
        
        return matrix
    
    def _generate_n_layers_matrix(self):
        """生成多层矩阵，适配新的CNN架构"""
        n_layers_matrix = np.zeros((self.n_layers, self.n_rows, self.n_cols), dtype=np.float32)
        
        # 第0层：目标状态
        n_layers_matrix[0] = self.state_to_matrix(self.goal_state)
        
        # 第1层：当前状态
        n_layers_matrix[1] = self.state_to_matrix(self.start_state)
        
        # 第2-21层：动作表示（每层表示执行对应动作后的后继状态）
        successors = self.get_successors()
        for i in range(2, self.n_layers):
            action_idx = i - 2  # 动作索引：层2对应动作0，层3对应动作1，...，层21对应动作19
            if action_idx < len(successors):
                # 获取对应动作
                _, action = successors[action_idx]
                # 计算执行该动作后的后继状态
                successor_state = self.apply_action(self.start_state, action)
                n_layers_matrix[i] = self.state_to_matrix(successor_state)
            else:
                # 如果动作索引超出范围，填充零矩阵
                n_layers_matrix[i] = np.zeros((self.n_rows, self.n_cols), dtype=np.float32)
        
        return n_layers_matrix
    
    def apply_action(self, state, action):
        """执行动作并返回后继状态"""
        from_stack, to_stack = action
        from_stack_name = f"Stack{from_stack}"
        to_stack_name = f"Stack{to_stack}"
        
        # 深拷贝状态以避免修改原状态
        import copy
        new_state = copy.deepcopy(state)
        
        # 检查源栈是否为空
        if from_stack_name not in new_state or not new_state[from_stack_name]:
            return new_state  # 无效动作，返回原状态
        
        # 移动顶部块
        block = new_state[from_stack_name].pop()  # 从源栈顶部取出块
        
        # 确保目标栈存在
        if to_stack_name not in new_state:
            new_state[to_stack_name] = []
        
        new_state[to_stack_name].append(block)  # 放入目标栈顶部
        
        return new_state
    
    def get_successors(self):
        """生成所有可能的动作（用于标签匹配）"""
        successors = []
        
        # 生成所有可能的 (from_stack, to_stack) 动作
        for from_stack in range(1, self.n_stacks + 1):
            for to_stack in range(1, self.n_stacks + 1):
                if from_stack != to_stack:
                    action = (from_stack, to_stack)  # 使用元组格式，与解析的动作格式匹配
                    # 这里我们不需要真实的后继状态，只需要动作标识符
                    successors.append((self.start_state, action))
        
        return successors

def generate_optimized_sample_matrix(log_samples, 
                                   n_stacks=5, 
                                   max_blocks=15, 
                                   buffer_rows=0, 
                                   buffer_cols=1,
                                   current_state_layer_idx=1,
                                   goal_state_layer_idx=0,
                                   coeffs=None):
    """生成优化的样本矩阵"""
    if coeffs is None:
        coeffs = {
            "PRIORITY_TASK_BLOCK": 15,
            "OTHER_PROBLEMATIC_BLOCKS": 10,
            "FIX_STACK_STABLE_PART": 7,
            "NEXT_TARGET_TO_ADD": 8,
            "FUTURE_TARGET_AWARENESS_LOW": 3
        }

    if not log_samples:
        print("错误: 没有有效的日志样本")
        return None, None, None, None

    # 从第一个样本提取状态信息
    first_sample = log_samples[0]
    current_state = first_sample["current_state"]
    goal_state = first_sample["goal_state"]
    
    # 创建fix_order
    stack_names = sorted(current_state.keys())
    fix_order = stack_names[:n_stacks]  # 只取前n_stacks个栈
    
    print(f"使用优化的 fix_order: {fix_order}")
    
    # 创建优化的规划器实例
    planner = OptimizedBlocksWorldPlanner(
        current_state, goal_state, fix_order, 
        n_stacks=n_stacks, max_blocks=max_blocks, 
        buffer_rows=buffer_rows, buffer_cols=buffer_cols
    )

    samples_matrix = np.zeros((len(log_samples), planner.n_layers, 
                               planner.n_rows, planner.n_cols))
    labels = np.full((len(log_samples), 2), -1, dtype=int)

    for i, sample in enumerate(log_samples):
        if not sample.get("current_state") or not sample.get("goal_state"):
            print(f"警告: 样本 {i} (Node {sample.get('node')}) 缺少状态信息，跳过")
            continue
            
        # 为每个样本创建规划器
        sample_planner = OptimizedBlocksWorldPlanner(
            sample["current_state"], sample["goal_state"], fix_order,
            n_stacks=n_stacks, max_blocks=max_blocks,
            buffer_rows=buffer_rows, buffer_cols=buffer_cols
        )

        # 生成矩阵（占用表示）
        raw_layers_matrix = sample_planner._generate_n_layers_matrix()

        # 对当前状态层（第1层）进行“位置增强”：
        # 依据 LLM 的 fix_stack_id、attention_block_name、priority_task_description，
        # 在相应 (pos, stack) 位置用系数增强（不改变 22 层接口和占用语义）。
        try:
            current_state_dict = sample.get("current_state", {})
            goal_state_dict = sample.get("goal_state", {})
            fix_stack_id = sample.get("fix_stack_id")
            attention_block_name = sample.get("attention_block_name")
            priority_task_desc = sample.get("priority_task_description", "")

            # 使用与矩阵编码一致的列顺序（前 n_stacks 个栈，按名称排序）
            active_stacks = sample_planner.stacks[:n_stacks]
            stack_to_idx = {name: idx for idx, name in enumerate(active_stacks)}

            # 仅在提供了 fix_stack 时执行增强
            if fix_stack_id is not None:
                fix_stack_key = f"Stack{fix_stack_id}"
                curr_blocks = current_state_dict.get(fix_stack_key, [])
                goal_blocks = goal_state_dict.get(fix_stack_key, [])

                stable_prefix, current_problem_suffix, future_target_suffix = \
                    get_stable_prefix_and_suffixes(curr_blocks, goal_blocks)

                # 解析任务类型
                task_type = "remove" if ("移走" in priority_task_desc) else ("add" if ("移入" in priority_task_desc) else "unknown")

                # 定位 fix 栈的列索引
                if fix_stack_key in stack_to_idx:
                    s_idx = stack_to_idx[fix_stack_key]
                    cur_layer = raw_layers_matrix[current_state_layer_idx]

                    # 1) 稳定前缀：用较小系数增强（强调已正确部分）
                    for pos, blk in enumerate(curr_blocks[:len(stable_prefix)]):
                        if pos < cur_layer.shape[0]:
                            if cur_layer[pos, s_idx] == 1:
                                cur_layer[pos, s_idx] = coeffs["FIX_STACK_STABLE_PART"]

                    # 2) 当前问题块：增强其所在位置
                    for blk in current_problem_suffix:
                        if blk in curr_blocks:
                            pos = curr_blocks.index(blk)
                            if pos < cur_layer.shape[0]:
                                if cur_layer[pos, s_idx] == 1:
                                    cur_layer[pos, s_idx] = coeffs["OTHER_PROBLEMATIC_BLOCKS"]

                    # 3) 优先任务块（attention）：更强增强
                    if attention_block_name and attention_block_name in curr_blocks:
                        pos = curr_blocks.index(attention_block_name)
                        if pos < cur_layer.shape[0]:
                            if cur_layer[pos, s_idx] == 1:
                                cur_layer[pos, s_idx] = coeffs["PRIORITY_TASK_BLOCK"]

                    # 4) “移入”任务：可选地增强“应添加的下一块”之下的稳定顶部位置（不新增占用）
                    # 为避免破坏占用语义，这里暂仅依赖上述 1-3 的增强。

        except Exception as e:
            print(f"位置增强时出现问题（样本 {i}）：{e}")

        samples_matrix[i] = raw_layers_matrix

        # 处理标签（正确的动作映射）
        successors = sample_planner.get_successors()
        best_action = sample.get("best_action")
        worst_action = sample.get("worst_action")

        # 标签处理：直接使用动作索引，不依赖于模型设计
        for j, (next_state, action) in enumerate(successors):
            if best_action is not None and action == best_action:
                labels[i, 0] = j
            if worst_action is not None and action == worst_action:
                labels[i, 1] = j

        if labels[i, 0] == -1:
            print(f"Sample {i}: Best action '{best_action}' not found")
        if labels[i, 1] == -1:
            print(f"Sample {i}: Worst action '{worst_action}' not found")
    
    return samples_matrix, labels, planner.blocks[:max_blocks], planner.stacks[:n_stacks]

def save_optimized_csv(samples_matrix, labels, blocks, stacks, output_prefix="optimized_samples"):
    """保存优化的CSV文件"""
    n_samples, n_layers, n_rows, n_cols = samples_matrix.shape

    column_names = []
    for layer in range(n_layers):
        for row in range(n_rows):
            for col in range(n_cols):
                if layer == 0:
                    layer_name = "goal"
                elif layer == 1:
                    layer_name = "current"
                else:
                    layer_name = f"action_{layer - 2}"
                
                row_name = f"pos_{row}"
                col_name = f"stack_{col}" if col < len(stacks) else f"buffer_{col - len(stacks)}"
                column_names.append(f"{layer_name}_{row_name}_{col_name}")

    flat_matrix = samples_matrix.reshape(n_samples, -1)
    matrix_df = pd.DataFrame(flat_matrix, columns=column_names)
    labels_df = pd.DataFrame(labels, columns=["best_action_idx", "worst_action_idx"])

    output_prefix_path = output_prefix if os.path.isabs(output_prefix) or os.path.dirname(output_prefix) else os.path.join(SCRIPT_DIR, output_prefix)
    matrix_df.to_csv(f"{output_prefix_path}_matrix.csv", index=False)
    labels_df.to_csv(f"{output_prefix_path}_labels.csv", index=False)
    
    print(f"✅ 优化样本已保存:")
    print(f"  - {output_prefix_path}_matrix.csv")
    print(f"  - {output_prefix_path}_labels.csv")

def process_log_to_optimized_matrix(log_content, 
                                  n_stacks=5, 
                                  max_blocks=15, 
                                  buffer_rows=0, 
                                  buffer_cols=1,
                                  output_prefix="optimized_samples"):
    """处理日志生成优化的矩阵"""
    log_samples = parse_log(log_content)
    print(f"解析得到 {len(log_samples)} 个样本")
    
    if not log_samples:
        print("错误: 没有解析到有效样本")
        return None, None, None, None
    
    samples_matrix, labels, blocks, stacks = generate_optimized_sample_matrix(
        log_samples, n_stacks=n_stacks, max_blocks=max_blocks,
        buffer_rows=buffer_rows, buffer_cols=buffer_cols
    )
    
    if samples_matrix is not None:
        save_optimized_csv(samples_matrix, labels, blocks, stacks, output_prefix)
    
    return samples_matrix, labels, blocks, stacks

def main():
    import argparse, glob
    parser = argparse.ArgumentParser()
    parser.add_argument('--log_dir', default=os.path.join(SCRIPT_DIR, 'llm_guidance_logs'))
    parser.add_argument('--extra_logs', nargs='*', default=[os.path.join(SCRIPT_DIR, 'llm_guided_test_log.txt')])
    parser.add_argument('--output_prefix', default=os.path.join(SCRIPT_DIR, 'optimized_llm_guided_samples'))
    parser.add_argument('--n_stacks', type=int, default=5)
    parser.add_argument('--max_blocks', type=int, default=15)
    args = parser.parse_args()

    # 收集日志文件
    log_files = []
    if args.log_dir and os.path.isdir(args.log_dir):
        log_files.extend(sorted(glob.glob(os.path.join(args.log_dir, '*.txt'))))
    if args.extra_logs:
        for p in args.extra_logs:
            if os.path.exists(p):
                log_files.append(p)
    # 去重
    log_files = list(dict.fromkeys(log_files))
    if not log_files:
        print('错误: 未找到任何日志文件')
        return

    print('将处理以下日志文件:')
    for p in log_files:
        print(' -', p)

    # 解析所有日志，合并样本
    all_samples = []
    total_chars = 0
    for p in log_files:
        with open(p, 'r', encoding='utf-8') as f:
            content = f.read()
        total_chars += len(content)
        smps = parse_log(content)
        print(f'文件 {os.path.basename(p)} 解析样本数: {len(smps)}')
        all_samples.extend(smps)

    print(f'累计解析样本: {len(all_samples)}，累计字符: {total_chars}')

    if not all_samples:
        print('❌ 未从日志中解析到任何样本')
        return

    try:
        samples_matrix, labels, blocks, stacks = generate_optimized_sample_matrix(
            all_samples,
            n_stacks=args.n_stacks,
            max_blocks=args.max_blocks,
            buffer_rows=0,
            buffer_cols=1,
        )
        if samples_matrix is not None:
            save_optimized_csv(samples_matrix, labels, blocks, stacks, args.output_prefix)
            print('✅ 成功生成优化训练样本!')
            print(f'样本矩阵形状: {samples_matrix.shape}')
            print(f'标签形状: {labels.shape}')
            print(f'前5个样本的标签: {labels[:5]}')
        else:
            print('❌ 优化样本矩阵生成失败')
    except Exception as e:
        print(f'❌ 处理失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()