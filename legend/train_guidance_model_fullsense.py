import os
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from sklearn.utils.class_weight import compute_class_weight

from cnn_transformer_legend import BlocksWorldDataset, CNNTransformerClassifier

def compute_class_weights(labels, n_classes):
    """计算类别权重来处理数据不平衡"""
    valid_labels = labels[labels != -1]
    
    if len(valid_labels) == 0:
        return torch.ones(n_classes)
    
    unique_classes = np.unique(valid_labels)
    class_weights = compute_class_weight(
        'balanced', 
        classes=unique_classes, 
        y=valid_labels
    )
    
    weights = torch.ones(n_classes)
    for i, cls in enumerate(unique_classes):
        weights[cls] = class_weights[i]
    
    return weights

def infer_shape_from_onehot_header(header):
    """从one-hot编码格式的CSV头部推断数据形状"""
    print(f"分析one-hot编码列名: 总列数={len(header)}")
    
    # 按层分组分析
    by_layer = {}
    for col in header:
        if col.startswith('goal_'):
            layer = 'goal'
        elif col.startswith('current_'):
            layer = 'current'  
        elif col.startswith('action_'):
            action_num = col.split('_')[1]
            layer = f'action_{action_num}'
        else:
            layer = 'unknown'
            
        if layer not in by_layer:
            by_layer[layer] = []
        by_layer[layer].append(col)

    print(f"检测到{len(by_layer)}个层，每层{len(by_layer.get('goal', []))}个特征")
    
    # 分析单层结构（使用goal层）
    goal_cols = by_layer.get('goal', [])
    on_cols = [col for col in goal_cols if '_on_' in col]
    clear_cols = [col for col in goal_cols if '_clear_' in col]
    
    print(f"每层结构: {len(on_cols)} on关系 + {len(clear_cols)} clear标记")
    
    # 从特征总数推断形状
    features_per_layer = len(goal_cols)
    n_layers = len(by_layer)
    
    # 对于320特征/层，最可能是16x20
    if features_per_layer == 320:
        n_rows, n_cols = 16, 20
    else:
        # 尝试因数分解
        import math
        for r in range(10, 30):
            if features_per_layer % r == 0:
                c = features_per_layer // r
                if 10 <= c <= 30:
                    n_rows, n_cols = r, c
                    break
        else:
            n_rows, n_cols = 16, 20  # 默认值
    
    print(f"推断最终形状: {n_layers} x {n_rows} x {n_cols}")
    return (n_layers, n_rows, n_cols)

def create_sequential_splits(dataset_size, val_ratio=0.2, test_ratio=0.1):
    """创建顺序切分"""
    val_size = int(dataset_size * val_ratio)
    test_size = int(dataset_size * test_ratio)
    train_size = dataset_size - val_size - test_size
    
    train_indices = list(range(train_size))
    val_indices = list(range(train_size, train_size + val_size))
    test_indices = list(range(train_size + val_size, dataset_size))
    
    print(f"数据切分: 训练={len(train_indices)}, 验证={len(val_indices)}, 测试={len(test_indices)}")
    return train_indices, val_indices, test_indices

def train_model(args):
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据并推断形状
    df = pd.read_csv(args.matrix_csv)
    orig_shape = infer_shape_from_onehot_header(df.columns.tolist())
    new_shape = orig_shape  # 使用推断的形状作为目标形状
    
    print(f"数据形状: {orig_shape} -> {new_shape}")
    
    # 创建数据集
    dataset = BlocksWorldDataset(
        matrix_file=args.matrix_csv,
        label_file=args.labels_csv,
        orig_shape=orig_shape,
        new_shape=new_shape
    )
    
    # 创建数据切分
    train_indices, val_indices, test_indices = create_sequential_splits(
        len(dataset), val_ratio=0.2, test_ratio=0.1
    )
    
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)
    test_dataset = Subset(dataset, test_indices)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    # 创建模型（使用完整感知架构）
    model = CNNTransformerClassifier(
        n_layers=new_shape[0],
        n_rows=new_shape[1],
        n_cols=new_shape[2],
        embed_dim=args.embed_dim,
        n_heads=args.n_heads,
        n_hidden=args.n_hidden,
        n_classes=args.n_classes,
        num_transformer_layers=args.num_transformer_layers,
        classifier_dropout_rate=args.classifier_dropout,
    ).to(device)
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    # 计算类别权重
    all_labels = dataset.labels
    best_labels = all_labels[:, 0] if all_labels.ndim > 1 else all_labels
    worst_labels = all_labels[:, 1] if all_labels.ndim > 1 else np.full_like(best_labels, -1)
    
    if args.use_class_weights_best:
        best_class_weights = compute_class_weights(best_labels, args.n_classes)
        best_class_weights = best_class_weights.to(device)
        print(f"Best头类别权重: {best_class_weights}")
    else:
        best_class_weights = None
    
    # 损失函数
    criterion_best = nn.CrossEntropyLoss(
        weight=best_class_weights,
        ignore_index=-1,
        label_smoothing=args.label_smoothing
    )
    criterion_worst = nn.CrossEntropyLoss(ignore_index=-1, label_smoothing=args.label_smoothing)
    
    # 优化器
    optimizer = optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )
    
    # 训练循环
    best_val_loss = float('inf')
    patience_counter = 0
    train_losses = []
    val_losses = []
    
    for epoch in range(args.epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (data, labels) in enumerate(train_loader):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            best_logits, worst_logits = model(data)
            
            # 计算损失
            if labels.dim() == 1:
                # 单标签格式
                best_loss = criterion_best(best_logits, labels)
                worst_loss = torch.tensor(0.0, device=device)
            else:
                # 双标签格式
                best_loss = criterion_best(best_logits, labels[:, 0])
                worst_loss = criterion_worst(worst_logits, labels[:, 1])
            
            total_loss = best_loss + args.worst_loss_coef * worst_loss
            total_loss.backward()
            optimizer.step()
            
            train_loss += total_loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        correct_best = 0
        total_best = 0
        
        with torch.no_grad():
            for data, labels in val_loader:
                data = data.to(device)
                labels = labels.to(device)
                
                best_logits, worst_logits = model(data)
                
                if labels.dim() == 1:
                    best_loss = criterion_best(best_logits, labels)
                    worst_loss = torch.tensor(0.0, device=device)
                    valid_mask = labels != -1
                    if valid_mask.sum() > 0:
                        pred_best = best_logits[valid_mask].argmax(dim=1)
                        correct_best += (pred_best == labels[valid_mask]).sum().item()
                        total_best += valid_mask.sum().item()
                else:
                    best_loss = criterion_best(best_logits, labels[:, 0])
                    worst_loss = criterion_worst(worst_logits, labels[:, 1])
                    valid_mask = labels[:, 0] != -1
                    if valid_mask.sum() > 0:
                        pred_best = best_logits[valid_mask].argmax(dim=1)
                        correct_best += (pred_best == labels[valid_mask, 0]).sum().item()
                        total_best += valid_mask.sum().item()
                
                val_loss += (best_loss + args.worst_loss_coef * worst_loss).item()
        
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        val_acc_best = correct_best / total_best if total_best > 0 else 0.0
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f"Epoch {epoch+1}/{args.epochs}: "
              f"Train Loss={avg_train_loss:.4f}, Val Loss={avg_val_loss:.4f}, "
              f"Val Best Acc={val_acc_best:.4f}")
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            
            # 保存最佳模型
            torch.save(model.state_dict(), args.model_out)
            
            # 保存元数据
            metadata = {
                'n_layers': new_shape[0],
                'n_rows': new_shape[1], 
                'n_cols': new_shape[2],
                'embed_dim': args.embed_dim,
                'n_heads': args.n_heads,
                'n_hidden': args.n_hidden,
                'n_classes': args.n_classes,
                'num_transformer_layers': args.num_transformer_layers,
                'classifier_dropout': args.classifier_dropout,
                'epoch': epoch + 1,
                'val_loss': avg_val_loss,
                'val_acc_best': val_acc_best,
                'architecture': 'full_sense_cnn_transformer'
            }
            
            with open(args.metadata_out, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        else:
            patience_counter += 1
            
        if patience_counter >= args.patience:
            print(f"早停触发，在第{epoch+1}轮")
            break
    
    print(f"训练完成！最佳模型保存到: {args.model_out}")
    return model

def main():
    parser = argparse.ArgumentParser(description='训练完整感知的CNN-Transformer指导模型')
    
    # 数据参数
    parser.add_argument('--matrix_csv', type=str, required=True, help='矩阵CSV文件路径')
    parser.add_argument('--labels_csv', type=str, required=True, help='标签CSV文件路径')
    
    # 模型参数
    parser.add_argument('--embed_dim', type=int, default=64, help='Transformer嵌入维度')
    parser.add_argument('--n_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--n_hidden', type=int, default=256, help='前馈网络隐藏层大小')
    parser.add_argument('--n_classes', type=int, default=20, help='分类数量')
    parser.add_argument('--num_transformer_layers', type=int, default=6, help='Transformer层数')
    parser.add_argument('--classifier_dropout', type=float, default=0.1, help='分类器dropout率')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64, help='批大小')
    parser.add_argument('--lr', type=float, default=5e-5, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=3e-4, help='权重衰减')
    parser.add_argument('--patience', type=int, default=40, help='早停耐心值')
    parser.add_argument('--label_smoothing', type=float, default=0.1, help='标签平滑')
    
    # 损失权重
    parser.add_argument('--worst_loss_coef', type=float, default=0.1, help='worst损失系数')
    parser.add_argument('--use_class_weights_best', action='store_true', help='为best头使用类别权重')
    
    # 输出参数
    parser.add_argument('--model_out', type=str, required=True, help='模型输出路径')
    parser.add_argument('--metadata_out', type=str, required=True, help='元数据输出路径')
    
    args = parser.parse_args()
    
    print("=== 完整感知CNN-Transformer训练 ===")
    print(f"使用one-hot编码数据: {args.matrix_csv}")
    print(f"架构: 标准(3,3)卷积 + Transformer")
    print(f"worst损失系数: {args.worst_loss_coef}")
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.model_out), exist_ok=True)
    os.makedirs(os.path.dirname(args.metadata_out), exist_ok=True)
    
    # 训练模型
    model = train_model(args)
    
    print("✅ 完整感知模型训练完成！")

if __name__ == "__main__":
    main()
