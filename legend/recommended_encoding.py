"""
最终推荐的CNN编码方式 - 清晰总结
"""
import numpy as np

print("=" * 70)
print("推荐的CNN编码方案：双通道块-栈编码")
print("=" * 70)

# 示例状态
state = {
    'stack1': ['A', 'B', 'C'],      # A在底，C在顶
    'stack2': ['D'],
    'stack3': [],
    'stack4': ['E', 'F'],            # E在底，F在顶
    'stack5': ['G', 'H', 'I', 'J'],  # G在底，J在顶
    'stack6': ['K']
}

goal = {
    'stack1': ['A', 'B'],
    'stack2': ['C', 'D', 'E'],
    'stack3': [],
    'stack4': ['F', 'G', 'H'],
    'stack5': [],
    'stack6': ['I', 'J', 'K']
}

# 定义所有块
all_blocks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O']
n_blocks = 15
n_stacks = 6

print("\n【编码维度】")
print(f"每个状态编码为: (2, {n_blocks}, {n_stacks})")
print("- 2个通道")
print(f"- {n_blocks}行（每行代表一个块）")
print(f"- {n_stacks}列（每列代表一个栈）")

print("\n" + "=" * 70)
print("通道0：块-栈隶属矩阵")
print("=" * 70)

# 创建通道0
channel0 = np.zeros((n_blocks, n_stacks))
for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    for block in blocks:
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            channel0[block_idx, stack_idx] = 1

print("\n含义: channel0[block_id, stack_id] = 1 表示块在该栈中")
print("\n矩阵示例（当前状态）:")
print("    s1 s2 s3 s4 s5 s6")
for i in range(11):  # 只显示前11个块
    block = all_blocks[i]
    row = channel0[i].astype(int)
    in_stack = np.where(row == 1)[0]
    if len(in_stack) > 0:
        print(f"{block}: {row}  # 在stack{in_stack[0]+1}")
    else:
        print(f"{block}: {row}  # 不在任何栈")

print("\n优点:")
print("✓ 保留块的身份信息")
print("✓ 直接表达'块X在栈Y中'")
print("✓ CNN可以学习块的分布模式")

print("\n" + "=" * 70)
print("通道1：高度/可移动性矩阵")
print("=" * 70)

# 创建通道1
channel1 = np.zeros((n_blocks, n_stacks))
for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
    for height, block in enumerate(blocks):
        if block in all_blocks:
            block_idx = all_blocks.index(block)
            # 编码高度（1-based）
            channel1[block_idx, stack_idx] = height + 1
            # 或者编码是否可移动（栈顶块）
            # channel1[block_idx, stack_idx] = 1 if height == len(blocks)-1 else 0

print("\n含义: channel1[block_id, stack_id] = h 表示块在该栈的高度h")
print("      (或 = 1 表示块可移动，= 0 表示被压住)")
print("\n矩阵示例（高度编码）:")
print("    s1 s2 s3 s4 s5 s6")
for i in range(11):
    block = all_blocks[i]
    row = channel1[i].astype(int)
    height_info = np.where(row > 0)[0]
    if len(height_info) > 0:
        stack_idx = height_info[0]
        height = row[stack_idx]
        print(f"{block}: {row}  # 高度{height}在stack{stack_idx+1}")
    else:
        print(f"{block}: {row}  # 不在任何栈")

print("\n优点:")
print("✓ 编码块的精确位置")
print("✓ 隐含可移动性（高度越大越可能可移动）")
print("✓ CNN可以学习堆叠模式")

print("\n" + "=" * 70)
print("完整的输入张量结构")
print("=" * 70)

print("\n对于22层的完整输入:")
print("Shape: (22, 2, 15, 6)")
print("\n层的含义:")
print("- 层0: 目标状态 (2, 15, 6)")
print("- 层1: 当前状态 (2, 15, 6)")
print("- 层2-21: 20个可能动作的后继状态 (2, 15, 6)")

print("\n实际上是: (22, 2, 15, 6) → 可以reshape为 (44, 15, 6)")
print("将22个状态×2个通道 = 44个通道输入CNN")

print("\n" + "=" * 70)
print("为什么这种编码最适合CNN")
print("=" * 70)

print("\n1. 空间局部性有意义:")
print("   - 垂直方向：同一个块在不同栈的模式")
print("   - 水平方向：不同块在同一栈的关系")

print("\n2. 卷积操作的解释性:")
print("   - (3,1)卷积：检测3个块之间的垂直模式")
print("   - (1,3)卷积：检测块在3个相邻栈的分布")
print("   - (3,3)卷积：检测局部的块-栈配置模式")

print("\n3. 平移不变性部分适用:")
print("   - 某些模式（如'三个块堆叠'）在任何栈都相似")
print("   - 但通过位置编码可以区分不同栈")

print("\n4. 信息完整:")
print("   - 不丢失块的身份")
print("   - 保留精确的位置信息")
print("   - 可以重构完整状态")

print("\n" + "=" * 70)
print("实现示例")
print("=" * 70)

def encode_state(state, all_blocks, n_stacks):
    """将状态编码为(2, n_blocks, n_stacks)张量"""
    n_blocks = len(all_blocks)
    encoding = np.zeros((2, n_blocks, n_stacks))
    
    for stack_idx, (stack_name, blocks) in enumerate(sorted(state.items())):
        if stack_idx >= n_stacks:
            break
        for height, block in enumerate(blocks):
            if block in all_blocks:
                block_idx = all_blocks.index(block)
                # 通道0: 块-栈隶属
                encoding[0, block_idx, stack_idx] = 1
                # 通道1: 高度信息
                encoding[1, block_idx, stack_idx] = height + 1
    
    return encoding

# 测试编码
encoded = encode_state(state, all_blocks, n_stacks)
print(f"\n编码后的形状: {encoded.shape}")
print("通道0（隶属）非零元素数:", np.sum(encoded[0] > 0))
print("通道1（高度）非零元素数:", np.sum(encoded[1] > 0))

print("\n" + "=" * 70)
print("总结")
print("=" * 70)
print("\n推荐编码：双通道块-栈矩阵")
print("- 维度: (2, 15, 6) per state")
print("- 通道0: 块在哪个栈（0/1）")
print("- 通道1: 块的高度或可移动性")
print("\n这种编码最适合CNN因为：")
print("1. 保留了所有关键信息")
print("2. 空间结构有明确含义")
print("3. 卷积操作可解释")
print("4. 适合学习Blocks World的模式")
