#!/usr/bin/env python3
"""
带节点扩展限制的A*搜索，集成CNN-Transformer模型指导
基于原始pre_marshalling.py，添加最大节点数限制
"""

import heapq
import time
from datetime import datetime
import os
import sys

# 添加路径以导入原始模块
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)

# 导入原始的State类和基础功能
from pre_marshalling import State, GraphPlanningBlocksWorld as OriginalGraphPlanningBlocksWorld

class LimitedGraphPlanningBlocksWorld(OriginalGraphPlanningBlocksWorld):
    """
    带节点扩展限制的A*搜索规划器
    继承原始实现，添加最大节点数限制
    """
    
    def __init__(self, start_state, goal_state, fix_order, log_file="limited_astar_log.txt", max_nodes=200000):
        """
        初始化规划器
        
        Args:
            start_state: 起始状态
            goal_state: 目标状态  
            fix_order: 修复顺序
            log_file: 日志文件路径
            max_nodes: 最大扩展节点数限制
        """
        super().__init__(start_state, goal_state, fix_order, log_file)
        self.max_nodes = max_nodes
        
    def a_star_search(self, llm=None):
        """
        带节点限制的A*搜索
        
        Args:
            llm: 指导模型实例（可选）
            
        Returns:
            tuple: (solution_path, nodes_expanded) 或 (None, nodes_expanded) 如果未找到解
        """
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h

        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []

        # 清空日志文件
        open(self.log_file, 'w', encoding='utf-8').close()

        if llm is None:
            self.log(f"运行带限制的A*搜索（无指导，最大节点数: {self.max_nodes}）：")
        else:
            guidance_type = getattr(llm, '__class__', type(llm)).__name__
            self.log(f"运行带限制的A*搜索（使用 {guidance_type} 指导，最大节点数: {self.max_nodes}）：")

        start_time = time.time()

        while queue and count < self.max_nodes:
            _, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if len(self.check) < count:
                self.check.append(True)

            # 每1000个节点输出一次进度
            if count % 1000 == 0:
                elapsed = time.time() - start_time
                self.log(f"进度: 已扩展 {count}/{self.max_nodes} 节点 (耗时: {elapsed:.2f}s)")

            if self.is_goal(current_state):
                elapsed = time.time() - start_time
                self.log(f"✅ 找到解决方案：{path}")
                self.log(f"搜索节点数：{count}")
                self.log(f"路径长度：{len(path)}")
                self.log(f"总耗时：{elapsed:.4f}秒\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            if llm is not None:
                # 使用指导模型评估动作
                try:
                    # 适配不同的指导接口
                    if hasattr(llm, 'evaluate_actions'):
                        # CNN-Transformer指导接口
                        # 转换successors格式：从(State, action)转为(dict, action)
                        converted_successors = [(next_state.stacks, action) for next_state, action in successors]
                        llm_result = llm.evaluate_actions(current_state.stacks, self.goal.stacks, self, converted_successors)
                        
                        best_action = llm_result.get("best_action", 0)
                        worst_action = llm_result.get("worst_action", 0)
                        best_reason = llm_result.get("best_reason", "模型推荐")
                        worst_reason = llm_result.get("worst_reason", "模型不推荐")
                        ranking = llm_result.get("ranking", [])
                        top_k = getattr(llm, 'top_k', 0) or 0
                        weights = {}
                        if top_k > 0 and ranking:
                            for rank_idx, item in enumerate(ranking[:top_k]):
                                w = 1.0 - (rank_idx / max(1, top_k-1)) * 0.5 if top_k > 1 else 1.0
                                weights[int(item['i'])] = float(w)
                        
                        self.log(f"\n节点 {count}: 当前修复栈: **{current_state.current_fix_stack}**")
                        self.log(f"节点 {count}: 当前状态: {current_state.stacks}")
                        self.log(f"节点 {count}: 目标状态: {self.goal.stacks}")
                        
                        for i, (next_state, action) in enumerate(successors):
                            next_state.g = current_state.g + 1
                            next_state.h = self.heuristic(next_state)
                            h_original = next_state.h
                            if i in weights:
                                w = weights[i]
                                next_state.g -= w
                                next_state.h_adjusted = h_original * (0.667 + (1.0 - w) * 0.333)
                                next_state.cost = next_state.g + next_state.h_adjusted
                                if i == best_action:
                                    self.log(f"节点 {count}: 模型推荐最佳动作(Top-{top_k}) '{action}'，权重={w:.2f}")
                                    self.log(f"最佳理由: {best_reason}")
                            elif i == best_action:
                                if isinstance(best_reason, str) and ("RULE" in best_reason):
                                    next_state.g -= 2.0
                                    next_state.h_adjusted = h_original * 0.5
                                    self.log(f"节点 {count}: 规则加权最佳动作 '{action}' (g-=2.0, h*=0.50)")
                                else:
                                    next_state.g -= 1
                                    next_state.h_adjusted = h_original * 0.667
                                    self.log(f"节点 {count}: 模型推荐最佳动作 '{action}'")
                                next_state.cost = next_state.g + next_state.h_adjusted
                                self.log(f"最佳理由: {best_reason}")
                            elif i == worst_action:
                                if isinstance(worst_reason, str) and ("RULE" in worst_reason or "Not optimal by rule" in worst_reason):
                                    next_state.h_adjusted = h_original * 2.0
                                    self.log(f"节点 {count}: 规则加权最差动作 '{action}' (h*=2.00)")
                                else:
                                    next_state.h_adjusted = h_original * 1.5
                                    self.log(f"节点 {count}: 模型认为最差动作 '{action}'")
                                next_state.cost = next_state.g + next_state.h_adjusted
                                self.log(f"最差理由: {worst_reason}")
                            else:
                                next_state.h_adjusted = h_original
                                next_state.cost = next_state.g + next_state.h_adjusted

                    else:
                        # 兼容其他指导接口（如LLM）
                        llm_result, actions, current_issue, priority = llm.evaluate_actions(
                            current_state, self.goal, self, successors)
                        
                        best_action = llm_result.get("best_action", "uncertain")
                        worst_action = llm_result.get("worst_action", "uncertain")
                        best_reason = llm_result.get("best_reason", "")
                        worst_reason = llm_result.get("worst_reason", "")
                        
                        self.log(f"\n节点 {count}: 当前修复栈: **{current_state.current_fix_stack}** 当前问题: {current_issue} 优先任务: {priority}")
                        self.log(f"节点 {count}: 当前状态: {current_state.stacks}")
                        self.log(f"节点 {count}: 目标状态: {self.goal.stacks}")
                        
                        for i, (next_state, action) in enumerate(successors):
                            next_state.g = current_state.g + 1
                            next_state.h = self.heuristic(next_state)
                            h_original = next_state.h
                            
                            if best_action != "uncertain" and i == best_action - 1:
                                next_state.g -= 1
                                next_state.h_adjusted = h_original * 0.667
                                next_state.cost = next_state.g + next_state.h_adjusted
                                self.log(f"节点 {count}: LLM推荐最佳动作 '{action}'")
                                self.log(f"最佳理由: {best_reason}")
                            elif worst_action != "uncertain" and i == worst_action - 1:
                                next_state.h_adjusted = h_original * 1.5
                                next_state.cost = next_state.g + next_state.h_adjusted
                                self.log(f"节点 {count}: LLM认为最差动作 '{action}'")
                                self.log(f"最差理由: {worst_reason}")
                            else:
                                next_state.h_adjusted = h_original
                                next_state.cost = next_state.g + next_state.h_adjusted
                                
                except Exception as e:
                    self.log(f"⚠️ 指导模型评估失败: {e}")
                    # 回退到无指导模式
                    for next_state, action in successors:
                        next_state.g = current_state.g + 1
                        next_state.h = self.heuristic(next_state)
                        next_state.h_adjusted = next_state.h
                        next_state.cost = next_state.g + next_state.h
            else:
                # 无指导模式
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.h_adjusted = next_state.h
                    next_state.cost = next_state.g + next_state.h

            # 将后继状态加入队列
            for next_state, action in successors:
                if next_state not in visited:
                    new_path = path + [action]
                    heapq.heappush(queue, (next_state.cost, len(new_path), next_state, new_path))

        # 搜索结束
        elapsed = time.time() - start_time
        if count >= self.max_nodes:
            self.log(f"❌ 达到最大节点限制 ({self.max_nodes})，未找到解决方案")
        else:
            self.log(f"❌ 搜索空间耗尽，未找到解决方案")
        
        self.log(f"搜索节点数：{count}")
        self.log(f"总耗时：{elapsed:.4f}秒\n")
        return None, count

    def log(self, message):
        """记录日志到文件和控制台"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        # 写入文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
        
        # 输出到控制台（可选）
        print(log_message)