import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader

from typing import Tuple

class BlocksWorldDataset(Dataset):
    """
    读取 legend 目录下由 convert_log_to_samples.py 生成的 CSV，
    并按给定形状 reshape 成 (N, n_layers, n_rows, n_cols)。
    与原版cnn_transformer_modify.py的BlocksWorldDataset保持一致。
    """
    def __init__(self, matrix_file: str, label_file: str, orig_shape: Tuple[int, int, int], new_shape: Tuple[int, int, int]):
        assert os.path.exists(matrix_file), f"Matrix CSV not found: {matrix_file}"
        assert os.path.exists(label_file), f"Label CSV not found: {label_file}"
        
        # 加载矩阵数据并reshape
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = self.prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        
        # 数据验证
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签（保持为动作类别索引 0..19，不做 +2 偏移）
        labels = pd.read_csv(label_file).values
        print(f"Labels loaded: min={labels.min()}, max={labels.max()}")
        self.labels = labels

    def prepare_data(self, data, orig_shape, new_shape):
        """数据预处理函数，与原版保持一致"""
        batch_size = data.shape[0]
        new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
        new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data
        return new_data

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels

class CNNTransformerClassifier(nn.Module):
    """
    改进的CNN-Transformer架构，专门针对Blocks World问题优化：
    - 灵活的尺寸设计：支持自定义栈数、块数和缓冲区大小
    - 列隔离卷积：使用(3,1)卷积核保持栈的独立性，避免跨栈信息混合
    - 行方向池化：仅在垂直方向池化，保留栈间的空间关系
    - 双头分类（best, worst）+ Transformer编码器
    - 动态计算CNN输出尺寸，支持不同问题规模
    
    优势：
    - 空间效率：从(22,21,25)优化至(22,15,6)，减少83%空间浪费
    - 结构保持：保留Blocks World的垂直堆叠和栈独立性
    - 灵活配置：支持不同规模的Blocks World问题
    """
    def __init__(
        self,
        n_layers=22,
        n_stacks=5,           # 实际栈数
        max_blocks=15,        # 最大块数
        buffer_rows=0,        # 缓冲行数（可选）
        buffer_cols=1,        # 缓冲列数（可选，默认1）
        embed_dim=64,
        n_heads=8,
        n_hidden=256,
        n_classes=20,
        num_transformer_layers=6,
        classifier_dropout_rate=0.1,
    ):
        super().__init__()
        # 动态计算实际尺寸
        self.n_layers = n_layers
        self.n_rows = max_blocks + buffer_rows
        self.n_cols = n_stacks + buffer_cols
        self.n_stacks = n_stacks
        self.max_blocks = max_blocks
        self.embed_dim = embed_dim
        self.classifier_dropout_rate = classifier_dropout_rate
        self.max_actions_layers = n_layers - 2

        # 改进的CNN架构：保留垂直堆叠关系，避免跨栈信息混合
        self.cnn = nn.Sequential(
            # 第一层：列隔离卷积（仅在行方向卷积，保持栈独立性）
            nn.Conv2d(n_layers, 32, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            
            # 第二层：继续列隔离卷积
            nn.Conv2d(32, 64, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            
            # 仅在行方向池化，保留列（栈）的独立性
            nn.MaxPool2d(kernel_size=(2, 1), stride=(2, 1))
            # Output shape: (batch_size, 64, (n_rows+1)//2, n_cols)
        )

        # Calculate sequence length after CNN
        with torch.no_grad():
            dummy_input = torch.zeros(1, n_layers, self.n_rows, self.n_cols)
            cnn_output_shape = self.cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]  # 64
            self.cnn_out_h = cnn_output_shape[2]  # (n_rows + 1) // 2
            self.cnn_out_w = cnn_output_shape[3]  # n_cols (保持不变)
            self.sequence_length = self.cnn_out_h * self.cnn_out_w

        # Linear projection from CNN channel dim to Transformer embed_dim
        self.input_proj = nn.Linear(self.cnn_out_channels, embed_dim)

        # Positional embedding for the sequence
        self.pos_embed = nn.Parameter(torch.zeros(1, self.sequence_length, embed_dim))

        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim, nhead=n_heads, dim_feedforward=n_hidden, batch_first=True,
            dropout=0.3,  # 提升泛化性
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # 分类头部之前的Dropout层
        self.classifier_dropout = nn.Dropout(p=self.classifier_dropout_rate)

        # Classification heads (using the aggregated output)
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)

        self._initialize_weights()

    def _initialize_weights(self):
        # Initialize positional embedding
        nn.init.normal_(self.pos_embed, std=.02)  # Common initialization for pos embed

        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Slightly different init for Linear layers is common
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            # Note: Initialization within TransformerEncoderLayer might be handled internally

    def forward(self, x):
        batch_size = x.size(0)

        # 1. Pass through CNN
        x = self.cnn(x)  # Shape: (batch_size, 64, H, W)

        # 2. Reshape for Transformer
        x = x.flatten(2)  # Shape: (batch_size, 64, H*W)
        x = x.transpose(1, 2)  # Shape: (batch_size, H*W, 64)

        # 3. Project to embed_dim
        x = self.input_proj(x)  # Shape: (batch_size, H*W, embed_dim)

        # 4. Add positional embedding
        x = x + self.pos_embed  # Shape: (batch_size, H*W, embed_dim)

        # 5. Pass through Transformer Encoder
        x = self.transformer(x)  # Shape: (batch_size, H*W, embed_dim)

        # 6. Mean pooling
        x = x.mean(dim=1)  # Shape: (batch_size, embed_dim)

        # 应用分类头部之前的Dropout
        x = self.classifier_dropout(x)

        # 7. Classification heads
        best_logits = self.fc_best(x)  # Shape: (batch_size, n_classes)
        worst_logits = self.fc_worst(x)  # Shape: (batch_size, n_classes)

        return best_logits, worst_logits

    def freeze_cnn_layers(self, freeze_first=True, freeze_second=False):
        """冻结CNN层，与原版保持一致"""
        for name, param in self.cnn.named_parameters():
            # 冻结第一个卷积层 (self.cnn[0])
            if freeze_first and (name.startswith("0.")):
                param.requires_grad = False
            # 冻结第二个卷积层 (self.cnn[4])
            if freeze_second and (name.startswith("4.")):
                param.requires_grad = False

    def load_state_dict(self, state_dict, strict=False, orig_max_blocks=20, orig_n_stacks=20, orig_buffer_rows=1, orig_buffer_cols=5):
        """
        自定义的 state_dict 加载器，可以对位置编码和分类头进行自适应处理。
        与原版cnn_transformer_modify.py保持一致。
        """
        # --- 1. 处理位置编码 (Positional Embedding) ---
        ckpt_pos_embed = state_dict.get('pos_embed', None)
        if ckpt_pos_embed is not None and self.pos_embed.shape != ckpt_pos_embed.shape:
            print(f"位置编码尺寸不匹配。 Checkpoint: {ckpt_pos_embed.shape}, Model: {self.pos_embed.shape}")
            print("正在尝试对位置编码进行插值...")
            
            with torch.no_grad():
                device = next(self.parameters()).device
                dummy_cnn = self.cnn
                orig_n_rows = orig_max_blocks + orig_buffer_rows
                orig_n_cols = orig_n_stacks + orig_buffer_cols
                dummy_input = torch.zeros(1, self.n_layers, orig_n_rows, orig_n_cols).to(device)
                orig_cnn_out_shape = dummy_cnn(dummy_input).shape
                orig_h, orig_w = orig_cnn_out_shape[2], orig_cnn_out_shape[3]
            
            pos_embed_ckpt_2d = ckpt_pos_embed.reshape(1, orig_h, orig_w, self.embed_dim).permute(0, 3, 1, 2)
            new_h, new_w = self.cnn_out_h, self.cnn_out_w
            print(f"正在从 {orig_h}x{orig_w} 插值到 {new_h}x{new_w}")
            resized_pos_embed = F.interpolate(pos_embed_ckpt_2d, size=(new_h, new_w), mode='bicubic', align_corners=False)
            resized_pos_embed = resized_pos_embed.permute(0, 2, 3, 1).flatten(1, 2)
            state_dict['pos_embed'] = resized_pos_embed
        
        # --- 2. 处理分类头 (Classifier Head) ---
        model_head_shape = self.fc_best.weight.shape
        ckpt_head_shape = state_dict.get('fc_best.weight', None).shape if 'fc_best.weight' in state_dict else None
        
        if ckpt_head_shape is not None and model_head_shape != ckpt_head_shape:
            print(f"分类头尺寸不匹配。 Checkpoint: {ckpt_head_shape[0]} classes, Model: {model_head_shape[0]} classes.")
            print("将不加载预训练的分类头权重，使用新模型的随机初始化权重。")
            state_dict.pop('fc_best.weight', None)
            state_dict.pop('fc_best.bias', None)
            state_dict.pop('fc_worst.weight', None)
            state_dict.pop('fc_worst.bias', None)
        
        # --- 3. 最终加载 ---
        super().load_state_dict(state_dict, strict=False)

# 便捷训练/评估函数（已移至train_guidance_model.py中）

