import os
import json
import math
import argparse
import torch
from torch.utils.data import DataLoader, random_split
import torch.nn as nn
import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
import re

from cnn_transformer_legend import (
    BlocksWorldDataset,
    CNNTransformerClassifier,
)

# 解析列名推断形状（n_layers, n_rows, n_cols）
# 列名格式示例：goal_on_1_1 ... goal_clear_Stack5, current_..., action_0_..., action_k_...
# 假设：行（row）= blocks(1..B) + clear，列（col）= blocks + stacks
# 这里直接从 legend 的生成逻辑默认值推断：B=15, S=5。若需要通用化，可解析列名自动计算。
DEFAULT_B = 15
DEFAULT_S = 5

def infer_shape_from_csv_header(header: list[str]):
    # 从优化的列名格式解析形状
    # 列名格式: {layer}_{pos}_{row}_{stack/buffer}_{col}
    # 例如: goal_pos_0_stack_0, current_pos_1_buffer_0, action_0_pos_2_stack_1
    
    layers = set()
    rows = set()
    cols = set()
    
    for col in header:
        parts = col.split('_')
        if len(parts) >= 5:
            # 解析层
            if parts[0] == 'goal':
                layers.add(0)
            elif parts[0] == 'current':
                layers.add(1)
            elif parts[0] == 'action':
                action_idx = int(parts[1])
                layers.add(2 + action_idx)
            
            # 解析行（位置）
            if parts[1] == 'pos':
                rows.add(int(parts[2]))
            
            # 解析列（栈或缓冲）
            if parts[3] == 'stack':
                cols.add(int(parts[4]))
            elif parts[3] == 'buffer':
                cols.add(5 + int(parts[4]))  # 假设5个栈后是缓冲列
    
    n_layers = max(layers) + 1 if layers else 22
    n_rows = max(rows) + 1 if rows else 15
    n_cols = max(cols) + 1 if cols else 6
    
    print(f"从优化列名推断形状: n_layers={n_layers}, n_rows={n_rows}, n_cols={n_cols}")
    return (n_layers, n_rows, n_cols)


def train_one_epoch(model, loader, optimizer, criterion, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_batches = 0
    
    for batch_matrix, batch_labels in loader:
        batch_matrix = batch_matrix.to(device)
        batch_labels = batch_labels.to(device)
        
        optimizer.zero_grad()
        best_logits, worst_logits = model(batch_matrix)
        
        # 标签有效性掩码，-1表示无效标签
        best_mask = batch_labels[:, 0] != -1
        worst_mask = batch_labels[:, 1] != -1
        
        loss = 0
        current_loss_value = 0
        
        if best_mask.any():
            # 标签减2使其从0开始索引，与n_classes匹配
            loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
            loss += loss_best
            current_loss_value += loss_best.item()
        if worst_mask.any():
            # 标签减2使其从0开始索引
            loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
            loss += loss_worst
            current_loss_value += loss_worst.item()
        
        if loss != 0:  # 只有当存在有效标签时才反向传播
            loss.backward()
            optimizer.step()
            total_loss += current_loss_value
            total_batches += 1
    
    return total_loss / max(1, total_batches)

def evaluate(model, loader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0.0
    total_batches = 0
    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0
    
    with torch.no_grad():
        for batch_matrix, batch_labels in loader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)
            
            best_logits, worst_logits = model(batch_matrix)
            
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1
            
            loss = 0
            current_loss_value = 0
            
            if best_mask.any():
                loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                loss += loss_best
                current_loss_value += loss_best.item()
                
                preds = best_logits[best_mask].argmax(dim=1)
                correct_best += (preds == (batch_labels[best_mask, 0] - 2)).sum().item()
                total_best += int(best_mask.sum().item())
                
            if worst_mask.any():
                loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                loss += loss_worst
                current_loss_value += loss_worst.item()
                
                preds = worst_logits[worst_mask].argmax(dim=1)
                correct_worst += (preds == (batch_labels[worst_mask, 1] - 2)).sum().item()
                total_worst += int(worst_mask.sum().item())
                
            if loss != 0:
                total_loss += current_loss_value
                total_batches += 1
    
    avg_loss = total_loss / max(1, total_batches)
    acc_best = correct_best / total_best if total_best > 0 else 0.0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0.0
    
    return avg_loss, acc_best, acc_worst

def prepare_data(data, orig_shape, new_shape):
    """数据预处理函数，与原版保持一致"""
    batch_size = data.shape[0]
    new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
    new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data
    return new_data

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--matrix_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_matrix.csv"))
    parser.add_argument("--labels_csv", default=os.path.join(os.path.dirname(__file__), "optimized_llm_guided_samples_labels.csv"))
    parser.add_argument("--epochs", type=int, default=5000)
    parser.add_argument("--batch_size", type=int, default=32)
    parser.add_argument("--lr", type=float, default=5e-5)
    parser.add_argument("--weight_decay", type=float, default=3e-4)
    parser.add_argument("--val_ratio", type=float, default=0.1)
    parser.add_argument("--patience", type=int, default=500)
    parser.add_argument("--label_smoothing", type=float, default=0.1)
    parser.add_argument("--classifier_dropout", type=float, default=0.2)
    parser.add_argument("--embed_dim", type=int, default=64)
    parser.add_argument("--n_heads", type=int, default=8)
    parser.add_argument("--n_hidden", type=int, default=256)
    parser.add_argument("--num_transformer_layers", type=int, default=6)
    parser.add_argument("--model_out", default=os.path.join(os.path.dirname(__file__), "models", "cnn_transformer_guidance.pth"))
    parser.add_argument("--metadata_out", default=os.path.join(os.path.dirname(__file__), "models", "metadata.json"))
    parser.add_argument("--use_kfold", action="store_true")
    parser.add_argument("--n_folds", type=int, default=5)
    args = parser.parse_args()

    os.makedirs(os.path.dirname(args.model_out), exist_ok=True)

    # 推断数据形状
    header = pd.read_csv(args.matrix_csv, nrows=0).columns.tolist()
    n_layers, n_rows, n_cols = infer_shape_from_csv_header(header)
    print(f"Inferred shape: n_layers={n_layers}, n_rows={n_rows}, n_cols={n_cols}")

    # 输出维度（类别数）= n_rows - 1，与原实现一致
    n_classes = n_rows - 1
    orig_shape = (n_layers, n_rows, n_cols)
    new_shape = orig_shape  # 保持一致

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    if args.use_kfold:
        # 使用K折交叉验证训练
        train_model_with_kfold(
            matrix_file=args.matrix_csv,
            label_file=args.labels_csv,
            orig_shape=orig_shape,
            new_shape=new_shape,
            n_folds=args.n_folds,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr,
            weight_decay=args.weight_decay,
            patience=args.patience,
            label_smoothing=args.label_smoothing,
            classifier_dropout=args.classifier_dropout,
            embed_dim=args.embed_dim,
            n_heads=args.n_heads,
            n_hidden=args.n_hidden,
            num_transformer_layers=args.num_transformer_layers,
            model_path=args.model_out
        )
    else:
        # 使用常规训练
        model = train_model(
            matrix_file=args.matrix_csv,
            label_file=args.labels_csv,
            orig_shape=orig_shape,
            new_shape=new_shape,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr,
            weight_decay=args.weight_decay,
            val_split_ratio=args.val_ratio,
            patience=args.patience,
            label_smoothing_epsilon=args.label_smoothing,
            classifier_dropout_rate=args.classifier_dropout,
            embed_dim=args.embed_dim,
            n_heads=args.n_heads,
            n_hidden=args.n_hidden,
            num_transformer_layers=args.num_transformer_layers,
            model_path=args.model_out
        )
        
        # 评估模型
        evaluate_model(
            model=model,
            matrix_file=args.matrix_csv,
            label_file=args.labels_csv,
            orig_shape=orig_shape,
            new_shape=new_shape,
            batch_size=args.batch_size
        )

    # 保存元数据
    metadata = {
        # 原始推断的形状参数
        "n_layers": n_layers,
        "n_rows": n_rows,
        "n_cols": n_cols,
        "n_classes": n_classes,
        # 新的CNN架构参数（固定值）
        "n_stacks": 5,
        "max_blocks": 15,
        "buffer_rows": 0,
        "buffer_cols": 1,
        # Transformer参数
        "embed_dim": args.embed_dim,
        "n_heads": args.n_heads,
        "n_hidden": args.n_hidden,
        "num_transformer_layers": args.num_transformer_layers,
        "classifier_dropout": args.classifier_dropout,
        # 数据文件路径
        "matrix_csv": os.path.abspath(args.matrix_csv),
        "labels_csv": os.path.abspath(args.labels_csv),
    }
    with open(args.metadata_out, "w", encoding="utf-8") as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    print(f"Saved metadata to {args.metadata_out}")

def train_model(matrix_file, label_file, orig_shape, new_shape, epochs=100, batch_size=32, lr=3e-4, weight_decay=1e-4,
                val_split_ratio=0.1, patience=10, label_smoothing_epsilon=0.1, classifier_dropout_rate=0.1,
                embed_dim=64, n_heads=8, n_hidden=256, num_transformer_layers=6, model_path="cnn_transformer_guidance.pth"):
    """训练模型，与原版cnn_transformer_modify.py的train_model函数保持一致"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 创建数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape, new_shape)
    
    # 划分训练集和验证集
    val_size = int(len(dataset) * val_split_ratio)
    train_size = len(dataset) - val_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    # 初始化模型
    n_layers, n_rows, n_cols = new_shape
    n_classes = n_rows - 1
    
    # 对于优化的数据格式，直接使用推断的形状参数
    # 这些数据已经是为新架构优化的格式
    model = CNNTransformerClassifier(
        n_layers=n_layers,
        n_stacks=5,  # 固定为5个栈
        max_blocks=15,  # 固定为15个块
        buffer_rows=0,  # 无缓冲行
        buffer_cols=1,  # 1个缓冲列
        embed_dim=embed_dim,
        n_heads=n_heads,
        n_hidden=n_hidden,
        n_classes=n_classes,
        num_transformer_layers=num_transformer_layers,
        classifier_dropout_rate=classifier_dropout_rate
    ).to(device)

    # 检查是否存在已有模型文件，若存在则加载
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"Loaded existing model from {model_path} for incremental training")
    else:
        print(f"No existing model found at {model_path}, starting training from scratch")

    # 使用标签平滑的损失函数
    criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing_epsilon)
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=patience // 2, factor=0.5, verbose=True)
    
    # 早停相关变量
    best_val_loss = float('inf')
    epochs_no_improve = 0
    best_model_state_dict = model.state_dict()

    for epoch in range(epochs):
        train_loss = train_one_epoch(model, train_dataloader, optimizer, criterion, device)
        val_loss, acc_best, acc_worst = evaluate(model, val_dataloader, criterion, device)
        
        print(f"Epoch {epoch + 1}/{epochs} | train_loss={train_loss:.4f} | val_loss={val_loss:.4f} | acc_best={acc_best:.4f} | acc_worst={acc_worst:.4f}")
        
        # 更新学习率
        scheduler.step(val_loss)
        
        # 早停判断
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            best_model_state_dict = model.state_dict()
            print(f"Validation loss improved. Saving model state.")
        else:
            epochs_no_improve += 1
            print(f"Validation loss did not improve for {epochs_no_improve} epoch(s).")

        if epochs_no_improve >= patience:
            print(f"Early stopping triggered after {epoch + 1} epochs.")
            model.load_state_dict(best_model_state_dict)
            break
    
    # 保存最佳模型
    if not (epochs_no_improve >= patience):
        best_model_state_dict = model.state_dict()

    torch.save(best_model_state_dict, model_path)
    print(f"Model saved to {model_path}")

    # 加载回最佳模型用于返回
    model.load_state_dict(best_model_state_dict)
    return model

def evaluate_model(model, matrix_file, label_file, orig_shape, new_shape, batch_size=32):
    """评估模型，与原版保持一致"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()

    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape, new_shape)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)

    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0

    with torch.no_grad():
        for batch_matrix, batch_labels in dataloader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)

            best_logits, worst_logits = model(batch_matrix)
            best_preds = torch.argmax(best_logits, dim=1)
            worst_preds = torch.argmax(worst_logits, dim=1)

            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1

            if best_mask.any():
                true_best = batch_labels[best_mask, 0] - 2
                correct_best += (best_preds[best_mask] == true_best).sum().item()
                total_best += best_mask.sum().item()

            if worst_mask.any():
                true_worst = batch_labels[worst_mask, 1] - 2
                correct_worst += (worst_preds[worst_mask] == true_worst).sum().item()
                total_worst += worst_mask.sum().item()

    acc_best = correct_best / total_best if total_best > 0 else 0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0
    print(f"Best Action Accuracy: {acc_best:.4f} ({correct_best}/{total_best})")
    print(f"Worst Action Accuracy: {acc_worst:.4f} ({correct_worst}/{total_worst})")

def train_model_with_kfold(matrix_file, label_file, orig_shape, new_shape, n_folds=5, epochs=100,
                           batch_size=32, lr=3e-4, weight_decay=1e-4, patience=10, label_smoothing=0.1,
                           classifier_dropout=0.1, embed_dim=64, n_heads=8, n_hidden=256,
                           num_transformer_layers=6, model_path="cnn_transformer_guidance.pth"):
    """使用K折交叉验证训练模型，与原版保持一致"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 加载完整数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape, new_shape)

    # 初始化 KFold
    kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)
    fold_results = []

    n_layers, n_rows, n_cols = new_shape
    n_classes = n_rows - 1

    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        print(f"\nFold {fold + 1}/{n_folds}")

        # 创建训练和验证子集
        train_subset = torch.utils.data.Subset(dataset, train_idx)
        val_subset = torch.utils.data.Subset(dataset, val_idx)

        train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)

        # 初始化模型
        # 对于优化的数据格式，直接使用固定参数
        model = CNNTransformerClassifier(
            n_layers=n_layers,
            n_stacks=5,  # 固定为5个栈
            max_blocks=15,  # 固定为15个块
            buffer_rows=0,  # 无缓冲行
            buffer_cols=1,  # 1个缓冲列
            embed_dim=embed_dim,
            n_heads=n_heads,
            n_hidden=n_hidden,
            n_classes=n_classes,
            num_transformer_layers=num_transformer_layers,
            classifier_dropout_rate=classifier_dropout
        ).to(device)

        # 检查是否加载已有模型
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            print(f"Loaded existing model from {model_path} for fold {fold + 1}")
        else:
            print(f"Starting training from scratch for fold {fold + 1}")

        criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)

        # 早停参数
        best_val_loss = float('inf')
        epochs_no_improve = 0
        best_model_state = None

        for epoch in range(epochs):
            train_loss = train_one_epoch(model, train_loader, optimizer, criterion, device)
            val_loss, acc_best, acc_worst = evaluate(model, val_loader, criterion, device)

            print(f"Epoch {epoch + 1}/{epochs}, Train Loss: {train_loss:.4f}, "
                  f"Val Loss: {val_loss:.4f}, "
                  f"Best Acc: {acc_best:.4f}, Worst Acc: {acc_worst:.4f}")

            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict()
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1

            if epochs_no_improve >= patience:
                print(f"Early stopping triggered after {epoch + 1} epochs")
                break

        # 保存该折的最佳模型
        fold_model_path = f"{model_path}_fold_{fold + 1}.pth"
        torch.save(best_model_state, fold_model_path)
        print(f"Best model for fold {fold + 1} saved to {fold_model_path}")

        # 记录该折的结果
        fold_results.append({
            'fold': fold + 1,
            'model_path': fold_model_path,
            'val_loss': best_val_loss,
            'best_acc': acc_best,
            'worst_acc': acc_worst
        })

    # 打印交叉验证结果
    print("\nCross-Validation Results:")
    for result in fold_results:
        print(f"Fold {result['fold']}: Val Loss = {result['val_loss']:.4f}, "
              f"Best Acc = {result['best_acc']:.4f}, Worst Acc = {result['worst_acc']:.4f}")

    return fold_results

if __name__ == "__main__":
    main()

