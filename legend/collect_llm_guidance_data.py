#!/usr/bin/env python3
"""
为随机算例收集LLM指导的A*搜索训练数据
"""

import os
import sys
import json
import time
import pickle
from typing import Dict, List, Tuple, Any
import traceback

# 添加路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TARGET_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "Expriment", "cnn_transformer_block_in_out"))
if TARGET_DIR not in sys.path:
    sys.path.append(TARGET_DIR)

try:
    from pre_marshalling import GraphPlanningBlocksWorld
    from pre_marshalling_llm import LLMGuidance
    print("✅ 成功导入A*搜索和LLM指导模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class DataCollector:
    """数据收集器，记录LLM指导的A*搜索过程"""
    
    def __init__(self):
        self.training_samples = []
        self.current_instance_id = None
        self.current_search_data = None
    
    def start_instance(self, instance_id: int, start_state: Dict, goal_state: Dict, fix_order: List[str]):
        """开始收集新算例的数据"""
        self.current_instance_id = instance_id
        self.current_search_data = {
            'instance_id': instance_id,
            'start_state': start_state,
            'goal_state': goal_state,
            'fix_order': fix_order,
            'search_nodes': [],
            'solution_found': False,
            'solution_path': None,
            'nodes_expanded': 0
        }
    
    def record_search_node(self, node_count: int, current_state: Dict, goal_state: Dict, 
                          successors: List[Tuple], llm_result: Dict, actions: List, 
                          current_issue: str, priority: str):
        """记录搜索节点的信息"""
        if self.current_search_data is None:
            return
        
        # 提取状态字典（处理State对象）
        if hasattr(current_state, 'stacks'):
            current_state_dict = current_state.stacks
        else:
            current_state_dict = current_state
            
        if hasattr(goal_state, 'stacks'):
            goal_state_dict = goal_state.stacks
        else:
            goal_state_dict = goal_state
        
        # 处理后继状态
        processed_successors = []
        for next_state, action in successors:
            if hasattr(next_state, 'stacks'):
                next_state_dict = next_state.stacks
            else:
                next_state_dict = next_state
            processed_successors.append((next_state_dict, action))
        
        node_data = {
            'node_count': node_count,
            'current_state': current_state_dict,
            'goal_state': goal_state_dict,
            'successors': processed_successors,
            'llm_result': llm_result,
            'actions': actions,
            'current_issue': current_issue,
            'priority': priority,
            'best_action': llm_result.get('best_action', 'uncertain'),
            'worst_action': llm_result.get('worst_action', 'uncertain'),
            'best_reason': llm_result.get('best_reason', ''),
            'worst_reason': llm_result.get('worst_reason', '')
        }
        
        self.current_search_data['search_nodes'].append(node_data)
    
    def finish_instance(self, solution_path: List = None, nodes_expanded: int = 0):
        """完成当前算例的数据收集"""
        if self.current_search_data is None:
            return
        
        self.current_search_data['solution_found'] = solution_path is not None
        self.current_search_data['solution_path'] = solution_path
        self.current_search_data['nodes_expanded'] = nodes_expanded
        
        # 将当前算例数据添加到训练样本中
        self.training_samples.append(self.current_search_data)
        
        # 重置当前数据
        self.current_search_data = None
        self.current_instance_id = None
    
    def get_training_samples(self) -> List[Dict]:
        """获取所有训练样本"""
        return self.training_samples
    
    def save_samples(self, filepath: str):
        """保存训练样本到文件"""
        with open(filepath, 'wb') as f:
            pickle.dump(self.training_samples, f)
        print(f"✅ 已保存 {len(self.training_samples)} 个训练样本到 {filepath}")

class ModifiedGraphPlanningBlocksWorld(GraphPlanningBlocksWorld):
    """修改的A*搜索类，集成数据收集功能"""
    
    def __init__(self, start_state, goal_state, fix_order, log_file="data_collection_log.txt", data_collector=None):
        super().__init__(start_state, goal_state, fix_order, log_file)
        self.data_collector = data_collector
        self.max_nodes = 10000  # 限制搜索节点数以避免过长时间
    
    def a_star_search(self, llm=None):
        """修改的A*搜索，集成数据收集"""
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h

        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []

        # 清空日志文件
        open(self.log_file, 'w', encoding='utf-8').close()

        if llm is None:
            self.log("运行 A* 搜索（无 LLM）：")
            return super().a_star_search(llm)
        else:
            self.log(f"运行 A* 搜索（使用 LLM {llm.model}）：")

        start_time = time.time()

        while queue and count < self.max_nodes:
            _, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if len(self.check) < count:
                self.check.append(True)

            # 每100个节点输出一次进度
            if count % 100 == 0:
                elapsed = time.time() - start_time
                print(f"  进度: 已扩展 {count}/{self.max_nodes} 节点 (耗时: {elapsed:.2f}s)")

            if self.is_goal(current_state):
                elapsed = time.time() - start_time
                self.log(f"找到解决方案：{path}")
                self.log(f"搜索节点数：{count}")
                self.log(f"路径长度：{len(path)}")
                self.log(f"总耗时：{elapsed:.4f}秒\\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            if llm is not None:
                try:
                    llm_result, actions, current_issue, priority = llm.evaluate_actions(
                        current_state, self.goal, self, successors)
                    
                    # 记录数据到收集器
                    if self.data_collector:
                        self.data_collector.record_search_node(
                            count, current_state, self.goal, successors,
                            llm_result, actions, current_issue, priority
                        )
                    
                    # 继续原有的A*搜索逻辑
                    best_action = llm_result["best_action"]
                    worst_action = llm_result["worst_action"]

                    for i, (next_state, action) in enumerate(successors):
                        next_state.g = current_state.g + 1
                        next_state.h = self.heuristic(next_state)
                        h_original = next_state.h
                        
                        if best_action != "uncertain" and i == best_action - 1:
                            next_state.g -= 1
                            next_state.h_adjusted = h_original * 0.667
                            next_state.cost = next_state.g + next_state.h_adjusted
                        elif worst_action != "uncertain" and i == worst_action - 1:
                            next_state.h_adjusted = h_original * 1.5
                            next_state.cost = next_state.g + next_state.h_adjusted
                        else:
                            next_state.h_adjusted = h_original
                            next_state.cost = next_state.g + next_state.h_adjusted
                            
                except Exception as e:
                    self.log(f"⚠️ LLM评估失败: {e}")
                    # 回退到无指导模式
                    for next_state, action in successors:
                        next_state.g = current_state.g + 1
                        next_state.h = self.heuristic(next_state)
                        next_state.h_adjusted = next_state.h
                        next_state.cost = next_state.g + next_state.h

            # 将后继状态加入队列
            for next_state, action in successors:
                if next_state not in visited:
                    new_path = path + [action]
                    heapq.heappush(queue, (next_state.cost, len(new_path), next_state, new_path))

        # 搜索结束
        elapsed = time.time() - start_time
        if count >= self.max_nodes:
            self.log(f"❌ 达到最大节点限制 ({self.max_nodes})，未找到解决方案")
        else:
            self.log(f"❌ 搜索空间耗尽，未找到解决方案")
        
        self.log(f"搜索节点数：{count}")
        self.log(f"总耗时：{elapsed:.4f}秒\\n")
        return None, count

def load_random_instances(json_file: str) -> List[Dict]:
    """加载随机算例"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            instances = json.load(f)
        print(f"✅ 成功加载 {len(instances)} 个随机算例")
        return instances
    except Exception as e:
        print(f"❌ 加载算例失败: {e}")
        return []

def collect_data_for_instance(instance: Dict, llm: LLMGuidance, data_collector: DataCollector) -> bool:
    """为单个算例收集数据"""
    instance_id = instance['id']
    start_state = instance['start_state']
    goal_state = instance['goal_state']
    fix_order = instance['fix_order']
    
    print(f"\n🔍 处理算例 {instance_id} (难度: {instance['difficulty']})")
    
    # 开始数据收集
    data_collector.start_instance(instance_id, start_state, goal_state, fix_order)
    
    try:
        # 创建规划器
        log_file = f"temp_log_{instance_id}.txt"
        planner = ModifiedGraphPlanningBlocksWorld(
            start_state, goal_state, fix_order, log_file, data_collector
        )
        
        # 运行A*搜索
        start_time = time.time()
        solution, nodes_count = planner.a_star_search(llm=llm)
        duration = time.time() - start_time
        
        # 完成数据收集
        data_collector.finish_instance(solution, nodes_count)
        
        # 清理临时日志文件
        if os.path.exists(log_file):
            os.remove(log_file)
        
        if solution:
            print(f"  ✅ 找到解决方案 (路径长度: {len(solution)}, 节点: {nodes_count}, 耗时: {duration:.2f}s)")
        else:
            print(f"  ❌ 未找到解决方案 (节点: {nodes_count}, 耗时: {duration:.2f}s)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理失败: {e}")
        traceback.print_exc()
        data_collector.finish_instance(None, 0)
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("收集LLM指导的A*搜索训练数据")
    print("=" * 80)
    
    # 配置参数
    INSTANCES_FILE = "random_instances/random_instances.json"
    OUTPUT_FILE = "llm_guidance_training_data.pkl"
    MAX_INSTANCES = 50  # 限制处理的算例数量（可以根据需要调整）
    
    print(f"配置参数:")
    print(f"  - 算例文件: {INSTANCES_FILE}")
    print(f"  - 输出文件: {OUTPUT_FILE}")
    print(f"  - 最大处理数量: {MAX_INSTANCES}")
    
    # 检查文件
    if not os.path.exists(INSTANCES_FILE):
        print(f"❌ 算例文件不存在: {INSTANCES_FILE}")
        return
    
    # 加载算例
    instances = load_random_instances(INSTANCES_FILE)
    if not instances:
        return
    
    # 限制处理数量
    if len(instances) > MAX_INSTANCES:
        instances = instances[:MAX_INSTANCES]
        print(f"⚠️ 限制处理前 {MAX_INSTANCES} 个算例")
    
    # 创建LLM指导实例
    print(f"\n🤖 初始化LLM指导...")
    try:
        llm = LLMGuidance()
        print(f"✅ LLM指导初始化成功")
    except Exception as e:
        print(f"❌ LLM指导初始化失败: {e}")
        return
    
    # 创建数据收集器
    data_collector = DataCollector()
    
    # 处理每个算例
    print(f"\n📊 开始处理 {len(instances)} 个算例...")
    successful_count = 0
    start_time = time.time()
    
    for i, instance in enumerate(instances):
        print(f"\n进度: {i+1}/{len(instances)}")
        
        success = collect_data_for_instance(instance, llm, data_collector)
        if success:
            successful_count += 1
        
        # 每10个算例保存一次中间结果
        if (i + 1) % 10 == 0:
            temp_file = f"temp_{OUTPUT_FILE}"
            data_collector.save_samples(temp_file)
            print(f"  💾 已保存中间结果到 {temp_file}")
    
    # 保存最终结果
    total_time = time.time() - start_time
    print(f"\n💾 保存最终训练数据...")
    data_collector.save_samples(OUTPUT_FILE)
    
    # 统计信息
    training_samples = data_collector.get_training_samples()
    total_nodes = sum(len(sample['search_nodes']) for sample in training_samples)
    
    print(f"\n📊 数据收集完成:")
    print(f"  - 处理算例: {len(instances)}")
    print(f"  - 成功算例: {successful_count}")
    print(f"  - 训练样本: {len(training_samples)}")
    print(f"  - 搜索节点: {total_nodes}")
    print(f"  - 总耗时: {total_time:.2f}秒")
    print(f"  - 平均每算例: {total_time/len(instances):.2f}秒")
    
    print(f"\n✅ 数据收集完成!")
    print(f"   下一步: 使用收集的数据重新训练CNN-Transformer模型")

if __name__ == "__main__":
    main()