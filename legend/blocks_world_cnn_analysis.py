"""
多栈Blocks World问题的特性分析与CNN设计探讨
"""

print("=" * 70)
print("多栈Blocks World (6栈15块) 的核心特性")
print("=" * 70)

print("\n1. 问题的结构特性：")
print("   - 栈之间相互独立（移动只在栈间进行）")
print("   - 栈内是严格的垂直堆叠关系")
print("   - 动作空间：从栈顶取块，放到另一栈顶")
print("   - 约束：只能移动栈顶的块")

print("\n2. 信息的空间特性：")
print("   - 垂直方向（栈内）：强依赖关系")
print("   - 水平方向（栈间）：独立关系")
print("   - 关键信息：每个块的身份、位置、是否可移动")

print("\n3. 决策所需的感知：")
print("   - 局部感知：某个块在哪个栈的什么位置")
print("   - 全局感知：所有块的分布情况")
print("   - 关系感知：哪些块压着哪些块")

print("\n" + "=" * 70)
print("CNN设计的核心挑战")
print("=" * 70)

print("\n传统CNN的假设 vs Blocks World的实际：")
print("1. 平移不变性：")
print("   CNN假设：特征在任何位置都相同")
print("   BW实际：块A在栈1和栈2的含义完全不同")

print("\n2. 局部连接性：")
print("   CNN假设：相邻像素相关性高")
print("   BW实际：相邻栈之间没有直接关系")

print("\n3. 层次化特征：")
print("   CNN假设：从边缘到形状到对象")
print("   BW实际：需要的是逻辑关系而非视觉特征")
