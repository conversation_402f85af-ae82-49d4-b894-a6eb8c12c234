--- CPMPDS 算法性能测试结果 (总结报告) ---


--------------------------------------------------------------------------------
配置 TotalS4_U60_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 29/30 个实例中找到解决方案。
    平均路径长度: 8.07
    平均搜索节点数: 6244.83
    平均耗时: 1.4442 秒

  LEGEND 算法总结:
    在 25/30 个实例中至少一次运行找到最优解。
    在 3/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 10.89
    所有实例的平均搜索节点数的平均值: 4606.06
    所有实例的平均耗时的平均值: 14.7187 秒

  LEGEND 算法总结（优化后）:
    在 27/30 个实例中至少一次优化找到最优解。
    在 3/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 10.28
    所有实例的平均搜索节点数的平均值: 4606.06
    所有实例的平均耗时的平均值: 14.7187 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U60_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 30/30 个实例中找到解决方案。
    平均路径长度: 9.50
    平均搜索节点数: 11401.40
    平均耗时: 2.5444 秒

  LEGEND 算法总结:
    在 21/30 个实例中至少一次运行找到最优解。
    在 7/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 12.72
    所有实例的平均搜索节点数的平均值: 7237.35
    所有实例的平均耗时的平均值: 22.4465 秒

  LEGEND 算法总结（优化后）:
    在 21/30 个实例中至少一次优化找到最优解。
    在 7/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 11.93
    所有实例的平均搜索节点数的平均值: 7237.35
    所有实例的平均耗时的平均值: 22.4465 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U60_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 28/30 个实例中找到解决方案。
    平均路径长度: 10.00
    平均搜索节点数: 6103.57
    平均耗时: 1.3521 秒

  LEGEND 算法总结:
    在 21/30 个实例中至少一次运行找到最优解。
    在 5/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 13.72
    所有实例的平均搜索节点数的平均值: 6691.63
    所有实例的平均耗时的平均值: 21.2028 秒

  LEGEND 算法总结（优化后）:
    在 23/30 个实例中至少一次优化找到最优解。
    在 5/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 12.81
    所有实例的平均搜索节点数的平均值: 6691.63
    所有实例的平均耗时的平均值: 21.2028 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U70_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 26/30 个实例中找到解决方案。
    平均路径长度: 9.77
    平均搜索节点数: 13133.69
    平均耗时: 3.2959 秒

  LEGEND 算法总结:
    在 18/30 个实例中至少一次运行找到最优解。
    在 11/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 12.72
    所有实例的平均搜索节点数的平均值: 10369.51
    所有实例的平均耗时的平均值: 36.5040 秒

  LEGEND 算法总结（优化后）:
    在 20/30 个实例中至少一次优化找到最优解。
    在 11/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 11.92
    所有实例的平均搜索节点数的平均值: 10369.51
    所有实例的平均耗时的平均值: 36.5040 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U70_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 26/30 个实例中找到解决方案。
    平均路径长度: 11.27
    平均搜索节点数: 21987.31
    平均耗时: 5.3470 秒

  LEGEND 算法总结:
    在 13/30 个实例中至少一次运行找到最优解。
    在 17/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 16.19
    所有实例的平均搜索节点数的平均值: 13302.39
    所有实例的平均耗时的平均值: 45.9345 秒

  LEGEND 算法总结（优化后）:
    在 17/30 个实例中至少一次优化找到最优解。
    在 17/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 14.96
    所有实例的平均搜索节点数的平均值: 13302.39
    所有实例的平均耗时的平均值: 45.9345 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U70_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 17/30 个实例中找到解决方案。
    平均路径长度: 13.29
    平均搜索节点数: 27427.41
    平均耗时: 6.4121 秒

  LEGEND 算法总结:
    在 5/30 个实例中至少一次运行找到最优解。
    在 23/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 19.62
    所有实例的平均搜索节点数的平均值: 18240.34
    所有实例的平均耗时的平均值: 65.1738 秒

  LEGEND 算法总结（优化后）:
    在 7/30 个实例中至少一次优化找到最优解。
    在 23/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 18.12
    所有实例的平均搜索节点数的平均值: 18240.34
    所有实例的平均耗时的平均值: 65.1738 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U80_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 22/30 个实例中找到解决方案。
    平均路径长度: 10.09
    平均搜索节点数: 24389.05
    平均耗时: 5.9701 秒

  LEGEND 算法总结:
    在 14/30 个实例中至少一次运行找到最优解。
    在 19/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 13.96
    所有实例的平均搜索节点数的平均值: 15281.49
    所有实例的平均耗时的平均值: 48.7007 秒

  LEGEND 算法总结（优化后）:
    在 16/30 个实例中至少一次优化找到最优解。
    在 19/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 13.17
    所有实例的平均搜索节点数的平均值: 15281.49
    所有实例的平均耗时的平均值: 48.7007 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U80_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 15/30 个实例中找到解决方案。
    平均路径长度: 13.33
    平均搜索节点数: 45029.93
    平均耗时: 10.6519 秒

  LEGEND 算法总结:
    在 4/30 个实例中至少一次运行找到最优解。
    在 21/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 17.94
    所有实例的平均搜索节点数的平均值: 20347.04
    所有实例的平均耗时的平均值: 67.9518 秒

  LEGEND 算法总结（优化后）:
    在 7/30 个实例中至少一次优化找到最优解。
    在 21/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 16.85
    所有实例的平均搜索节点数的平均值: 20347.04
    所有实例的平均耗时的平均值: 67.9518 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS4_U80_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 11/30 个实例中找到解决方案。
    平均路径长度: 13.36
    平均搜索节点数: 28833.09
    平均耗时: 6.4863 秒

  LEGEND 算法总结:
    在 6/30 个实例中至少一次运行找到最优解。
    在 19/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 18.74
    所有实例的平均搜索节点数的平均值: 23520.59
    所有实例的平均耗时的平均值: 75.1487 秒

  LEGEND 算法总结（优化后）:
    在 8/30 个实例中至少一次优化找到最优解。
    在 19/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 17.59
    所有实例的平均搜索节点数的平均值: 23520.59
    所有实例的平均耗时的平均值: 75.1487 秒
================================================================================


--------------------------------------------------------------------------------
配置 TotalS5_U60_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 28/30 个实例中找到解决方案。
    平均路径长度: 8.29
    平均搜索节点数: 10031.75
    平均耗时: 3.6608 秒

  LEGEND 算法总结:
    在 20/30 个实例中至少一次运行找到最优解。
    在 9/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 11.58
    所有实例的平均搜索节点数的平均值: 7582.19
    所有实例的平均耗时的平均值: 29.7069 秒

  LEGEND 算法总结（优化后）:
    在 19/30 个实例中至少一次优化找到最优解。
    在 9/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 10.90
    所有实例的平均搜索节点数的平均值: 7582.19
    所有实例的平均耗时的平均值: 29.7069 秒
================================================================================

