--- CPMPDS 算法性能测试结果 (详细报告) ---


--------------------------------------------------------------------------------
测试配置: TotalS5_U60_D70
--------------------------------------------------------------------------------

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [12, 13, 6, 2, 1], 'Stack2': [3, 5], 'Stack3': [8, 4, 7, 10, 9], 'Stack4': [11, 15, 14], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 37.7704 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 21947.00, 耗时: 101.0389 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 16218, 对应耗时: 61.0856 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.50, 搜索节点数: 21947.00, 耗时: 101.0389 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 16218, 对应耗时: 61.0856 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [9, 15, 1, 3], 'Stack2': [4, 12], 'Stack3': [6, 5, 8, 14], 'Stack4': [11, 13, 10, 2, 7], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 36.8133 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.67, 搜索节点数: 3596.00, 耗时: 12.9002 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2638, 对应耗时: 9.4193 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.67, 搜索节点数: 3596.00, 耗时: 12.9002 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2638, 对应耗时: 9.4193 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [15, 7, 14, 1, 10], 'Stack2': [2, 8, 6, 3, 4], 'Stack3': [11, 13, 12, 9, 5], 'Stack4': [], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 35.7137 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 18242.33, 耗时: 79.6332 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 1591, 对应耗时: 5.5373 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 18242.33, 耗时: 79.6332 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1591, 对应耗时: 5.5373 秒


--------------------------------------------------------------------------------
配置 TotalS5_U60_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    所有 3 个实例均未找到解决方案。

  LEGEND 算法总结:
    在 0/3 个实例中至少一次运行找到最优解。
    在 3/3 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 19.06
    所有实例的平均搜索节点数的平均值: 14595.11
    所有实例的平均耗时的平均值: 64.5241 秒

  LEGEND 算法总结（优化后）:
    在 0/3 个实例中至少一次优化找到最优解。
    在 3/3 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 17.06
    所有实例的平均搜索节点数的平均值: 14595.11
    所有实例的平均耗时的平均值: 64.5241 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS5_U70_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [15, 16, 12, 4], 'Stack2': [10, 13, 7, 3, 1], 'Stack3': [14, 8, 9], 'Stack4': [17, 6, 5, 2, 11], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 45.8752 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [12, 16, 6, 1, 13], 'Stack2': [14, 10, 3, 17, 7], 'Stack3': [4, 9], 'Stack4': [15, 11, 5, 2, 8], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 3256, 耗时: 0.6856 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.89, 搜索节点数: 11371.00, 耗时: 45.0436 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1283, 对应耗时: 4.8070 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.44, 搜索节点数: 11371.00, 耗时: 45.0436 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1283, 对应耗时: 4.8070 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [6, 5, 1, 11, 7], 'Stack2': [14, 3, 12, 4], 'Stack3': [15, 10, 2, 8, 17], 'Stack4': [9, 16, 13], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 38.3878 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.00, 搜索节点数: 13221.00, 耗时: 49.6435 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 13221, 对应耗时: 49.6435 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 13221.00, 耗时: 49.6435 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 13221, 对应耗时: 49.6435 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [8, 10, 3, 5, 14], 'Stack2': [13, 11, 1, 6, 15], 'Stack3': [17, 16], 'Stack4': [12, 7, 2, 4, 9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 690, 耗时: 0.1351 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.56, 搜索节点数: 671.00, 耗时: 3.9982 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 111, 对应耗时: 0.4030 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.56, 搜索节点数: 671.00, 耗时: 3.9982 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 111, 对应耗时: 0.4030 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [17, 2, 6], 'Stack2': [16, 12, 15, 8, 3], 'Stack3': [9, 5, 1, 10, 14], 'Stack4': [11, 4, 13, 7], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 75701, 耗时: 34.0423 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.12, 搜索节点数: 23683.12, 耗时: 94.6360 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 42532, 对应耗时: 169.7526 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.62, 搜索节点数: 23683.12, 耗时: 94.6360 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 42532, 对应耗时: 169.7526 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [10, 16, 11, 6], 'Stack2': [17, 4, 8, 7], 'Stack3': [9, 2, 14, 13], 'Stack4': [15, 12, 5, 1, 3], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 38.9817 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.33, 搜索节点数: 12978.33, 耗时: 51.3166 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 20279, 对应耗时: 79.5024 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.67, 搜索节点数: 12978.33, 耗时: 51.3166 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 7231, 对应耗时: 28.4366 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [15, 4, 3, 2, 14], 'Stack2': [13, 6, 11, 5, 1], 'Stack3': [12, 16, 10], 'Stack4': [17, 7, 8, 9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 15158, 耗时: 4.8123 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.00, 搜索节点数: 15868.71, 耗时: 65.5978 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 7311, 对应耗时: 28.2684 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.57, 搜索节点数: 15868.71, 耗时: 65.5978 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 7311, 对应耗时: 28.2684 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [11, 17, 3, 8, 16], 'Stack2': [13, 2, 1, 7], 'Stack3': [15, 10, 9, 14], 'Stack4': [6, 5, 12, 4], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 17218, 耗时: 7.1901 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.10, 搜索节点数: 10085.10, 耗时: 39.6376 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4824, 对应耗时: 18.8505 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 10085.10, 耗时: 39.6376 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4824, 对应耗时: 18.8505 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [16, 5, 17, 11], 'Stack2': [10, 8, 2], 'Stack3': [9, 6, 7, 12, 1], 'Stack4': [14, 3, 15, 4, 13], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 29066, 耗时: 12.0965 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.50, 搜索节点数: 17357.00, 耗时: 68.7707 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 9719, 对应耗时: 37.0187 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.75, 搜索节点数: 17357.00, 耗时: 68.7707 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 9719, 对应耗时: 37.0187 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [6, 4, 3, 8], 'Stack2': [15, 5, 10, 12], 'Stack3': [17, 9, 2, 14], 'Stack4': [11, 16, 13, 7, 1], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 38.3757 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.75, 搜索节点数: 18388.25, 耗时: 73.5170 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 10223, 对应耗时: 40.3727 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.38, 搜索节点数: 18388.25, 耗时: 73.5170 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 10223, 对应耗时: 40.3727 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [11, 10, 7, 2, 17], 'Stack2': [9, 15, 3, 5], 'Stack3': [14, 8, 13, 6, 1], 'Stack4': [4, 12, 16], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 15376, 耗时: 5.9811 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 9638.00, 耗时: 38.0590 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 588, 对应耗时: 2.1704 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 9638.00, 耗时: 38.0590 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 579, 对应耗时: 2.1327 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [17, 9, 16, 4, 1], 'Stack2': [12, 10, 3, 2, 7], 'Stack3': [11, 13, 5, 14], 'Stack4': [6, 15, 8], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 80965, 耗时: 31.1385 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 12852.33, 耗时: 50.8665 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 1884, 对应耗时: 7.3675 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.67, 搜索节点数: 12852.33, 耗时: 50.8665 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 14574, 对应耗时: 57.7933 秒

