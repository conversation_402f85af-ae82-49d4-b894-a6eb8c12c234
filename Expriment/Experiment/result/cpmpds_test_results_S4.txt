--- CPMPDS 算法性能测试结果 (详细报告) ---


--------------------------------------------------------------------------------
测试配置: TotalS4_U60_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [12, 10, 7, 5], 'Stack2': [6, 8, 9], 'Stack3': [4, 11, 3, 2, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 2815, 耗时: 0.5488 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 1533.60, 耗时: 4.7705 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 90, 对应耗时: 0.2623 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.40, 搜索节点数: 1533.60, 耗时: 4.7705 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 90, 对应耗时: 0.2623 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [4, 11, 6, 8], 'Stack2': [12, 7, 3], 'Stack3': [10, 2, 5, 9, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 762, 耗时: 0.0781 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 1248.60, 耗时: 3.8752 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 425, 对应耗时: 1.3582 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.00, 搜索节点数: 1248.60, 耗时: 3.8752 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 425, 对应耗时: 1.3582 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [6, 7, 1], 'Stack2': [10, 9, 5, 3, 12], 'Stack3': [4, 11, 2, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 253, 耗时: 0.0250 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.40, 搜索节点数: 366.60, 耗时: 1.1203 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 59, 对应耗时: 0.1690 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.00, 搜索节点数: 366.60, 耗时: 1.1203 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 59, 对应耗时: 0.1690 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [1, 3, 9, 2], 'Stack2': [10, 8, 12, 6, 4], 'Stack3': [11, 7, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 632, 耗时: 0.1680 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.50, 搜索节点数: 1475.40, 耗时: 4.4525 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 239, 对应耗时: 0.6797 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.20, 搜索节点数: 1475.40, 耗时: 4.4525 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 239, 对应耗时: 0.6797 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [10, 5, 1, 8], 'Stack2': [11, 6, 12], 'Stack3': [7, 9, 4, 3, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 8594, 耗时: 2.0051 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.10, 搜索节点数: 4460.50, 耗时: 13.7420 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 1409, 对应耗时: 4.2010 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.60, 搜索节点数: 4460.50, 耗时: 13.7420 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 1409, 对应耗时: 4.2010 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [8, 9, 2, 11, 6], 'Stack2': [5, 7, 4], 'Stack3': [12, 10, 1, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1987, 耗时: 0.4673 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.20, 搜索节点数: 2961.00, 耗时: 9.1284 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1828, 对应耗时: 5.5789 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 2961.00, 耗时: 9.1284 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1828, 对应耗时: 5.5789 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [10, 8, 2, 11, 12], 'Stack2': [7, 3, 4, 9, 6], 'Stack3': [1, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 71, 耗时: 0.0067 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.80, 搜索节点数: 1049.10, 耗时: 3.2473 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 17, 对应耗时: 0.0473 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.10, 搜索节点数: 1049.10, 耗时: 3.2473 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 17, 对应耗时: 0.0473 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [10, 9, 2, 5], 'Stack2': [12, 7, 8], 'Stack3': [3, 4, 11, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 912, 耗时: 0.0921 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.60, 搜索节点数: 542.90, 耗时: 1.6551 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 318, 对应耗时: 0.9479 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 542.90, 耗时: 1.6551 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 437, 对应耗时: 1.2902 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [2, 8, 5, 7], 'Stack2': [11, 9, 4, 3], 'Stack3': [6, 10, 1, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 113, 耗时: 0.0108 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.20, 搜索节点数: 216.20, 耗时: 0.6577 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 57, 对应耗时: 0.1668 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 7.70, 搜索节点数: 216.20, 耗时: 0.6577 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 57, 对应耗时: 0.1668 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [7, 3, 10], 'Stack2': [9, 6, 1, 2, 8], 'Stack3': [4, 5, 11, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 6, 搜索节点数: 87, 耗时: 0.0088 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.20, 搜索节点数: 6279.80, 耗时: 19.4430 秒
    10次运行最优值: 最优路径长度: 6, 对应搜索节点数: 435, 对应耗时: 1.2730 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.40, 搜索节点数: 6279.80, 耗时: 19.4430 秒
    10次运行最优值: 最优路径长度: 6, 对应搜索节点数: 435, 对应耗时: 1.2730 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [5, 6, 3], 'Stack2': [9, 8, 1, 2, 4], 'Stack3': [12, 10, 11, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 5724, 耗时: 1.3945 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 6582.00, 耗时: 19.9905 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 539, 对应耗时: 1.5877 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 6582.00, 耗时: 19.9905 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 539, 对应耗时: 1.5877 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [3, 1], 'Stack2': [11, 5, 9, 2, 4], 'Stack3': [7, 6, 10, 12, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 456, 耗时: 0.0456 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.90, 搜索节点数: 654.80, 耗时: 2.0028 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 232, 对应耗时: 0.6862 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 654.80, 耗时: 2.0028 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 232, 对应耗时: 0.6862 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [12, 11, 10, 5], 'Stack2': [2, 4, 3, 8], 'Stack3': [7, 9, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 1102, 耗时: 0.2256 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 1290.30, 耗时: 3.8956 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 1886, 对应耗时: 5.6123 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.90, 搜索节点数: 1290.30, 耗时: 3.8956 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 1886, 对应耗时: 5.6123 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [2, 12, 6, 4], 'Stack2': [9, 8, 3], 'Stack3': [10, 1, 7, 5, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2228, 耗时: 0.6014 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 3614.60, 耗时: 11.1753 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 668, 对应耗时: 2.0900 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.60, 搜索节点数: 3614.60, 耗时: 11.1753 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 668, 对应耗时: 2.0900 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [9, 11], 'Stack2': [10, 2, 1, 12, 3], 'Stack3': [7, 8, 6, 5, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 94595, 耗时: 22.4352 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.78, 搜索节点数: 14612.44, 耗时: 44.9735 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6401, 对应耗时: 19.2228 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.67, 搜索节点数: 14612.44, 耗时: 44.9735 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6401, 对应耗时: 19.2228 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [8, 7, 5, 2, 4], 'Stack2': [6, 10], 'Stack3': [11, 12, 3, 1, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 955, 耗时: 0.0987 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.20, 搜索节点数: 649.30, 耗时: 1.9792 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 38, 对应耗时: 0.1087 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.00, 搜索节点数: 649.30, 耗时: 1.9792 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 38, 对应耗时: 0.1087 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [9, 11, 7, 6, 1], 'Stack2': [12, 4, 3, 10], 'Stack3': [5, 2, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 8814, 耗时: 2.0533 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 13777.40, 耗时: 42.4067 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 22714, 对应耗时: 70.4365 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.10, 搜索节点数: 13777.40, 耗时: 42.4067 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 17580, 对应耗时: 54.1376 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [5, 6, 3, 4], 'Stack2': [12, 10, 11, 1], 'Stack3': [9, 8, 2, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 961, 耗时: 0.2082 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 1704.50, 耗时: 5.2190 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 221, 对应耗时: 0.6514 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.00, 搜索节点数: 1704.50, 耗时: 5.2190 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 221, 对应耗时: 0.6514 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [6, 12], 'Stack2': [8, 11, 7, 3, 1], 'Stack3': [10, 9, 4, 2, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 11363, 耗时: 2.5945 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 11875.90, 耗时: 37.8396 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1652, 对应耗时: 5.0928 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 11875.90, 耗时: 37.8396 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 5285, 对应耗时: 16.1506 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [11, 12, 5, 4, 2], 'Stack2': [9, 8, 6], 'Stack3': [10, 1, 7, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 4889, 耗时: 1.0457 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.40, 搜索节点数: 2774.00, 耗时: 9.7200 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 911, 对应耗时: 3.1797 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.00, 搜索节点数: 2774.00, 耗时: 9.7200 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 911, 对应耗时: 3.1797 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [9, 6, 2, 4, 8], 'Stack2': [1, 7], 'Stack3': [11, 12, 5, 3, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 4920, 耗时: 1.1440 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 6879.50, 耗时: 24.1183 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 539, 对应耗时: 1.8177 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.60, 搜索节点数: 6879.50, 耗时: 24.1183 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 539, 对应耗时: 1.8177 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [2, 8], 'Stack2': [11, 4, 5, 3, 12], 'Stack3': [9, 10, 7, 1, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 12703, 耗时: 3.0982 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.50, 搜索节点数: 4976.60, 耗时: 17.3270 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2336, 对应耗时: 8.3033 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.50, 搜索节点数: 4976.60, 耗时: 17.3270 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2336, 对应耗时: 8.3033 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [12, 6, 2, 3, 1], 'Stack2': [8, 5, 7, 4], 'Stack3': [10, 11, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 4606, 耗时: 0.9352 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.11, 搜索节点数: 8857.56, 耗时: 31.0926 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 269, 对应耗时: 1.0398 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.56, 搜索节点数: 8857.56, 耗时: 31.0926 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 269, 对应耗时: 1.0398 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [5, 2, 3, 12], 'Stack2': [9, 8, 7, 10, 6], 'Stack3': [1, 11, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 863, 耗时: 0.0875 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 590.10, 耗时: 2.0345 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 268, 对应耗时: 0.9045 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 590.10, 耗时: 2.0345 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 268, 对应耗时: 0.9045 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [3, 4, 11, 6, 7], 'Stack2': [8, 12, 5, 1, 2], 'Stack3': [10, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1939, 耗时: 0.4203 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.80, 搜索节点数: 2209.50, 耗时: 7.6956 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 159, 对应耗时: 0.5297 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.60, 搜索节点数: 2209.50, 耗时: 7.6956 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 159, 对应耗时: 0.5297 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [11, 9, 1, 10, 7], 'Stack2': [5, 12, 6, 8], 'Stack3': [3, 2, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2546, 耗时: 0.6440 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.10, 搜索节点数: 2264.30, 耗时: 7.9204 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 762, 对应耗时: 2.5879 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 2264.30, 耗时: 7.9204 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 762, 对应耗时: 2.5879 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [9, 3, 5], 'Stack2': [12, 7, 4, 8, 2], 'Stack3': [10, 11, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 3383, 耗时: 0.7675 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 8171.40, 耗时: 28.0994 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 4549, 对应耗时: 15.7562 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 8171.40, 耗时: 28.0994 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 2921, 对应耗时: 8.8441 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [3, 2, 8], 'Stack2': [10, 1, 4, 11, 6], 'Stack3': [9, 5, 12, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2517, 耗时: 0.6415 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 4132.50, 耗时: 12.6221 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 414, 对应耗时: 1.2003 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 4132.50, 耗时: 12.6221 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 11854, 对应耗时: 36.4014 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [4, 5, 1], 'Stack2': [7, 3, 2, 9], 'Stack3': [12, 8, 10, 6, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 310, 耗时: 0.0308 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.40, 搜索节点数: 853.60, 耗时: 2.5822 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 194, 对应耗时: 0.5667 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.00, 搜索节点数: 853.60, 耗时: 2.5822 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 92, 对应耗时: 0.2702 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [6, 11, 5, 1, 3], 'Stack2': [7, 4, 2], 'Stack3': [9, 12, 8, 10], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.4694 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 21577.86, 耗时: 66.7758 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 18448, 对应耗时: 57.7725 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.71, 搜索节点数: 21577.86, 耗时: 66.7758 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 18448, 对应耗时: 57.7725 秒


--------------------------------------------------------------------------------
配置 TotalS4_U60_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 29/30 个实例中找到解决方案。
    平均路径长度: 8.07
    平均搜索节点数: 6244.83
    平均耗时: 1.4442 秒

  LEGEND 算法总结:
    在 25/30 个实例中至少一次运行找到最优解。
    在 3/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 10.89
    所有实例的平均搜索节点数的平均值: 4606.06
    所有实例的平均耗时的平均值: 14.7187 秒

  LEGEND 算法总结（优化后）:
    在 27/30 个实例中至少一次优化找到最优解。
    在 3/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 10.28
    所有实例的平均搜索节点数的平均值: 4606.06
    所有实例的平均耗时的平均值: 14.7187 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U60_D60
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [10, 12, 2, 7], 'Stack2': [9, 1, 3, 11], 'Stack3': [5, 4, 6, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 8864, 耗时: 1.9395 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 8494.20, 耗时: 26.2772 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 784, 对应耗时: 2.3510 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 8494.20, 耗时: 26.2772 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 784, 对应耗时: 2.3510 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [10, 3, 11], 'Stack2': [5, 7, 2, 9], 'Stack3': [8, 12, 6, 4, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 40027, 耗时: 9.2091 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.38, 搜索节点数: 8806.25, 耗时: 27.1285 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3012, 对应耗时: 9.0651 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.62, 搜索节点数: 8806.25, 耗时: 27.1285 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3012, 对应耗时: 9.0651 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [8, 9, 2, 11], 'Stack2': [7, 5, 6, 1, 3], 'Stack3': [10, 12, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 3961, 耗时: 0.7291 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 10213.30, 耗时: 31.7674 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3687, 对应耗时: 11.0227 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 10213.30, 耗时: 31.7674 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3687, 对应耗时: 11.0227 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [7, 5, 1, 11, 2], 'Stack2': [6, 10, 12, 8], 'Stack3': [3, 9, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 2664, 耗时: 0.2804 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.70, 搜索节点数: 2370.60, 耗时: 7.2233 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 446, 对应耗时: 1.3230 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.40, 搜索节点数: 2370.60, 耗时: 7.2233 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 446, 对应耗时: 1.3230 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [9, 3, 11, 5], 'Stack2': [2, 12, 6], 'Stack3': [10, 4, 8, 7, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2091, 耗时: 0.4511 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 2993.70, 耗时: 9.6583 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2432, 对应耗时: 8.5808 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.90, 搜索节点数: 2993.70, 耗时: 9.6583 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2432, 对应耗时: 8.5808 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [2, 4, 1, 11, 9], 'Stack2': [12, 6, 10], 'Stack3': [7, 3, 5, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 596, 耗时: 0.0596 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 1177.20, 耗时: 4.1056 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 971, 对应耗时: 3.4269 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.20, 搜索节点数: 1177.20, 耗时: 4.1056 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 16, 对应耗时: 0.0510 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [7, 5, 8, 1, 11], 'Stack2': [3, 2, 4, 9, 12], 'Stack3': [6, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 9111, 耗时: 2.0329 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.90, 搜索节点数: 9298.60, 耗时: 28.6599 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 586, 对应耗时: 1.7315 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.80, 搜索节点数: 9298.60, 耗时: 28.6599 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 586, 对应耗时: 1.7315 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [10, 11, 4, 2], 'Stack2': [6, 8, 7, 1, 9], 'Stack3': [12, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2947, 耗时: 0.3300 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 11442.80, 耗时: 35.2747 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1684, 对应耗时: 5.1442 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 11442.80, 耗时: 35.2747 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1684, 对应耗时: 5.1442 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [11, 8, 5], 'Stack2': [9, 10, 4, 3], 'Stack3': [7, 12, 2, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 7623, 耗时: 1.6531 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.80, 搜索节点数: 4704.50, 耗时: 14.4358 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 4966, 对应耗时: 15.4718 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.40, 搜索节点数: 4704.50, 耗时: 14.4358 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 4966, 对应耗时: 15.4718 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [9, 6, 4, 5, 10], 'Stack2': [7, 12, 3, 2], 'Stack3': [8, 11, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 14530, 耗时: 3.4736 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.60, 搜索节点数: 8535.50, 耗时: 26.7178 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 5405, 对应耗时: 16.8714 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 8535.50, 耗时: 26.7178 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 5405, 对应耗时: 16.8714 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [11, 12, 2, 3], 'Stack2': [8, 9, 6], 'Stack3': [10, 7, 1, 5, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 10324, 耗时: 2.3285 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.67, 搜索节点数: 9239.11, 耗时: 32.3420 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 14218, 对应耗时: 50.2121 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.78, 搜索节点数: 9239.11, 耗时: 32.3420 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 14218, 对应耗时: 50.2121 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [10, 5, 9, 1], 'Stack2': [4, 8, 2, 3, 11], 'Stack3': [12, 6, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 1089, 耗时: 0.1152 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.00, 搜索节点数: 712.50, 耗时: 2.5094 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 157, 对应耗时: 0.5366 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.40, 搜索节点数: 712.50, 耗时: 2.5094 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 157, 对应耗时: 0.5366 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [11, 6, 8, 3, 2], 'Stack2': [5, 12], 'Stack3': [4, 1, 7, 9, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 4630, 耗时: 1.0665 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 1410.50, 耗时: 4.4951 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 403, 对应耗时: 1.1926 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 1410.50, 耗时: 4.4951 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 403, 对应耗时: 1.1926 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [5, 1, 2], 'Stack2': [12, 9, 10, 7, 6], 'Stack3': [8, 11, 4, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 84972, 耗时: 19.6380 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.71, 搜索节点数: 31297.29, 耗时: 95.1374 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 29251, 对应耗时: 89.9125 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.14, 搜索节点数: 31297.29, 耗时: 95.1374 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 29251, 对应耗时: 89.9125 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [5, 1, 2, 4], 'Stack2': [10, 11, 6, 3], 'Stack3': [8, 7, 12, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 12765, 耗时: 2.4398 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.10, 搜索节点数: 19581.60, 耗时: 60.6823 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3475, 对应耗时: 10.6958 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 19581.60, 耗时: 60.6823 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3475, 对应耗时: 10.6958 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [5, 6, 1], 'Stack2': [2, 4, 12, 11], 'Stack3': [10, 8, 3, 9, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 658, 耗时: 0.0672 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 648.20, 耗时: 1.9692 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 186, 对应耗时: 0.5539 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.90, 搜索节点数: 648.20, 耗时: 1.9692 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 186, 对应耗时: 0.5539 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [8, 4, 5, 6, 7], 'Stack2': [11, 12, 9, 1, 3], 'Stack3': [2, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 45770, 耗时: 10.5300 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.88, 搜索节点数: 14260.12, 耗时: 43.6866 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 10967, 对应耗时: 33.3256 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 14260.12, 耗时: 43.6866 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6041, 对应耗时: 18.1377 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [5, 9, 3, 2, 1], 'Stack2': [11, 10, 7], 'Stack3': [8, 12, 4, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 2924, 耗时: 0.3249 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.90, 搜索节点数: 1652.80, 耗时: 5.0274 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 512, 对应耗时: 1.4972 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.50, 搜索节点数: 1652.80, 耗时: 5.0274 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 80, 对应耗时: 0.2307 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [7, 8, 2, 1], 'Stack2': [9, 11, 5, 12], 'Stack3': [6, 4, 3, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 2485, 耗时: 0.6388 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 2209.50, 耗时: 6.8118 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 539, 对应耗时: 1.6967 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.10, 搜索节点数: 2209.50, 耗时: 6.8118 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 539, 对应耗时: 1.6967 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [9, 5, 6, 7], 'Stack2': [3, 4, 1, 12], 'Stack3': [2, 8, 11, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 226, 耗时: 0.0226 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.44, 搜索节点数: 431.56, 耗时: 1.2966 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 405, 对应耗时: 1.2863 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.11, 搜索节点数: 431.56, 耗时: 1.2966 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 67, 对应耗时: 0.1966 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [6, 1, 12, 7], 'Stack2': [11, 3, 9, 8, 5], 'Stack3': [2, 10, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 8133, 耗时: 1.7537 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.20, 搜索节点数: 2243.10, 耗时: 6.9860 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2232, 对应耗时: 6.9833 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.10, 搜索节点数: 2243.10, 耗时: 6.9860 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2232, 对应耗时: 6.9833 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [1, 8, 5], 'Stack2': [7, 9, 3, 11], 'Stack3': [6, 4, 2, 10, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 111, 耗时: 0.0109 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.10, 搜索节点数: 199.60, 耗时: 0.5989 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 82, 对应耗时: 0.3416 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 199.60, 耗时: 0.5989 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 266, 对应耗时: 0.7714 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [4, 7, 2, 3], 'Stack2': [5, 1, 8, 6], 'Stack3': [11, 10, 12, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 22590, 耗时: 5.0049 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.38, 搜索节点数: 21819.38, 耗时: 65.4757 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 8652, 对应耗时: 26.2229 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.25, 搜索节点数: 21819.38, 耗时: 65.4757 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 8652, 对应耗时: 26.2229 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [8, 6, 7, 1, 4], 'Stack2': [10, 12, 9, 3, 5], 'Stack3': [11, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 36649, 耗时: 9.1330 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.60, 搜索节点数: 19026.40, 耗时: 60.2337 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 41326, 对应耗时: 127.1998 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 19026.40, 耗时: 60.2337 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 27214, 对应耗时: 83.9018 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [5, 10, 4, 1, 12], 'Stack2': [2, 7, 8, 6], 'Stack3': [11, 9, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1574, 耗时: 0.1615 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 965.00, 耗时: 2.9515 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 582, 对应耗时: 1.8066 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.90, 搜索节点数: 965.00, 耗时: 2.9515 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 582, 对应耗时: 1.8066 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [5, 8, 2, 9], 'Stack2': [3, 12, 11], 'Stack3': [7, 6, 1, 10, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 6375, 耗时: 1.4020 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 7863.60, 耗时: 24.3166 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 9163, 对应耗时: 28.2174 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.40, 搜索节点数: 7863.60, 耗时: 24.3166 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 9163, 对应耗时: 28.2174 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [3, 1, 11, 7], 'Stack2': [10, 2, 5, 12, 6], 'Stack3': [8, 9, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 983, 耗时: 0.0978 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.60, 搜索节点数: 3723.90, 耗时: 11.4505 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2225, 对应耗时: 6.8422 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.20, 搜索节点数: 3723.90, 耗时: 11.4505 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1052, 对应耗时: 3.1189 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [8, 3, 5, 1, 9], 'Stack2': [7, 2, 12, 10, 6], 'Stack3': [4, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1261, 耗时: 0.2438 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.90, 搜索节点数: 6935.40, 耗时: 21.3925 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 4716, 对应耗时: 14.7149 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.40, 搜索节点数: 6935.40, 耗时: 21.3925 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 965, 对应耗时: 2.9950 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [2, 10, 9, 3], 'Stack2': [11, 7, 6, 8, 4], 'Stack3': [1, 5, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2802, 耗时: 0.4453 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 816.50, 耗时: 2.4756 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 832, 对应耗时: 2.4510 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 816.50, 耗时: 2.4756 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 832, 对应耗时: 2.4510 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [2, 3], 'Stack2': [12, 10, 11, 1, 9], 'Stack3': [7, 5, 6, 4, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 3307, 耗时: 0.7485 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.60, 搜索节点数: 4047.90, 耗时: 12.3093 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 84, 对应耗时: 0.2433 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.10, 搜索节点数: 4047.90, 耗时: 12.3093 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 856, 对应耗时: 2.5245 秒


--------------------------------------------------------------------------------
配置 TotalS4_U60_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 30/30 个实例中找到解决方案。
    平均路径长度: 9.50
    平均搜索节点数: 11401.40
    平均耗时: 2.5444 秒

  LEGEND 算法总结:
    在 21/30 个实例中至少一次运行找到最优解。
    在 7/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 12.72
    所有实例的平均搜索节点数的平均值: 7237.35
    所有实例的平均耗时的平均值: 22.4465 秒

  LEGEND 算法总结（优化后）:
    在 21/30 个实例中至少一次优化找到最优解。
    在 7/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 11.93
    所有实例的平均搜索节点数的平均值: 7237.35
    所有实例的平均耗时的平均值: 22.4465 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U60_D70
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [4, 7, 10], 'Stack2': [8, 12, 1, 6], 'Stack3': [9, 3, 11, 5, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1396, 耗时: 0.2854 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.20, 搜索节点数: 3745.60, 耗时: 11.5214 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1047, 对应耗时: 3.2092 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.70, 搜索节点数: 3745.60, 耗时: 11.5214 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1047, 对应耗时: 3.2092 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [6, 11, 3, 5, 8], 'Stack2': [12, 4, 9, 1, 2], 'Stack3': [7, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2759, 耗时: 0.6729 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 4209.50, 耗时: 12.9636 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 4302, 对应耗时: 13.1545 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 4209.50, 耗时: 12.9636 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 4302, 对应耗时: 13.1545 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [8, 11], 'Stack2': [4, 7, 1, 2, 9], 'Stack3': [5, 3, 12, 6, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 8034, 耗时: 1.6205 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 12555.17, 耗时: 38.6623 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1501, 对应耗时: 4.4423 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 12555.17, 耗时: 38.6623 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1501, 对应耗时: 4.4423 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [11, 3, 9, 6, 10], 'Stack2': [4, 7, 12], 'Stack3': [2, 8, 1, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 277, 耗时: 0.0270 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.90, 搜索节点数: 1006.60, 耗时: 3.0682 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 131, 对应耗时: 0.3845 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 1006.60, 耗时: 3.0682 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 131, 对应耗时: 0.3845 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [11, 4, 10, 3, 7], 'Stack2': [5, 12], 'Stack3': [8, 9, 2, 1, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2823, 耗时: 0.6842 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 5429.40, 耗时: 16.7626 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3271, 对应耗时: 10.0538 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 5429.40, 耗时: 16.7626 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 333, 对应耗时: 0.9787 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [6, 11, 8, 3], 'Stack2': [9, 10, 4, 12], 'Stack3': [7, 1, 2, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 21572, 耗时: 5.2562 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.33, 搜索节点数: 12089.00, 耗时: 37.4240 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6944, 对应耗时: 21.6557 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.22, 搜索节点数: 12089.00, 耗时: 37.4240 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6944, 对应耗时: 21.6557 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [6, 9, 5, 2], 'Stack2': [7, 3, 10], 'Stack3': [8, 12, 4, 1, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 8140, 耗时: 1.8150 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 10513.90, 耗时: 32.5943 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 642, 对应耗时: 2.0374 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 10513.90, 耗时: 32.5943 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 642, 对应耗时: 2.0374 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [6, 9, 5, 11], 'Stack2': [7, 2, 8, 12], 'Stack3': [4, 10, 3, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 13473, 耗时: 2.9928 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.40, 搜索节点数: 8067.80, 耗时: 24.9006 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 852, 对应耗时: 2.5136 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.70, 搜索节点数: 8067.80, 耗时: 24.9006 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 169, 对应耗时: 0.5001 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [4, 5, 2], 'Stack2': [12, 10, 11, 1, 7], 'Stack3': [3, 6, 9, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 4575, 耗时: 1.0483 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 4192.40, 耗时: 12.7978 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1720, 对应耗时: 5.2310 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.20, 搜索节点数: 4192.40, 耗时: 12.7978 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1720, 对应耗时: 5.2310 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [2, 12, 10, 7], 'Stack2': [3, 9, 8], 'Stack3': [11, 4, 5, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 3730, 耗时: 0.9505 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.40, 搜索节点数: 7224.10, 耗时: 22.3205 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 11239, 对应耗时: 34.3932 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.30, 搜索节点数: 7224.10, 耗时: 22.3205 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 657, 对应耗时: 2.0531 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [2, 6, 7], 'Stack2': [5, 4, 11, 1, 12], 'Stack3': [8, 10, 3, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 212, 耗时: 0.0207 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.90, 搜索节点数: 1631.10, 耗时: 4.9705 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 166, 对应耗时: 0.4906 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 1631.10, 耗时: 4.9705 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 166, 对应耗时: 0.4906 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [9, 3, 7, 4, 6], 'Stack2': [2, 11, 12], 'Stack3': [5, 10, 8, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 3552, 耗时: 0.7532 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.70, 搜索节点数: 3743.20, 耗时: 11.5330 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2561, 对应耗时: 7.7537 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.00, 搜索节点数: 3743.20, 耗时: 11.5330 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2561, 对应耗时: 7.7537 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [3, 5, 11, 8, 12], 'Stack2': [4, 2], 'Stack3': [9, 10, 6, 7, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 1656, 耗时: 0.2904 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.00, 搜索节点数: 2418.80, 耗时: 7.3846 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1979, 对应耗时: 6.1032 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 2418.80, 耗时: 7.3846 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 113, 对应耗时: 0.3294 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [7, 12, 9, 5], 'Stack2': [10, 11, 2, 1], 'Stack3': [8, 3, 4, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.0581 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.25, 搜索节点数: 9746.50, 耗时: 29.7246 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 13516, 对应耗时: 40.8093 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.25, 搜索节点数: 9746.50, 耗时: 29.7246 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 13516, 对应耗时: 40.8093 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [6, 12, 2, 7, 1], 'Stack2': [10, 4, 8], 'Stack3': [3, 5, 11, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 1590, 耗时: 0.1635 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 11271.10, 耗时: 34.6299 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3306, 对应耗时: 9.8688 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.90, 搜索节点数: 11271.10, 耗时: 34.6299 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3306, 对应耗时: 9.8688 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [1, 11, 3, 4], 'Stack2': [5, 10, 7], 'Stack3': [12, 2, 9, 8, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 3285, 耗时: 0.7062 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.80, 搜索节点数: 3298.20, 耗时: 10.0896 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2277, 对应耗时: 6.8221 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 3298.20, 耗时: 10.0896 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2277, 对应耗时: 6.8221 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [3, 6, 8, 2], 'Stack2': [5, 10, 11, 7], 'Stack3': [9, 1, 4, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 877, 耗时: 0.2022 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.50, 搜索节点数: 2706.30, 耗时: 8.2598 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 134, 对应耗时: 0.3902 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.10, 搜索节点数: 2706.30, 耗时: 8.2598 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 134, 对应耗时: 0.3902 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [5, 8, 9, 7], 'Stack2': [3, 6, 2], 'Stack3': [12, 10, 11, 1, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 4523, 耗时: 0.7944 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.50, 搜索节点数: 1618.00, 耗时: 4.8885 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1887, 对应耗时: 5.5474 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 1618.00, 耗时: 4.8885 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 939, 对应耗时: 2.8594 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [10, 3, 4, 7], 'Stack2': [8, 12, 1, 5, 11], 'Stack3': [2, 9, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2923, 耗时: 0.6825 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 1663.60, 耗时: 5.0991 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 287, 对应耗时: 0.8473 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 1663.60, 耗时: 5.0991 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 143, 对应耗时: 0.4166 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [1, 2], 'Stack2': [8, 12, 4, 5, 11], 'Stack3': [10, 6, 9, 3, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2272, 耗时: 0.4756 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 4088.60, 耗时: 12.4129 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1003, 对应耗时: 2.9234 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.50, 搜索节点数: 4088.60, 耗时: 12.4129 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 131, 对应耗时: 0.3784 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [1, 10, 4, 5, 3], 'Stack2': [2, 7, 12], 'Stack3': [8, 6, 9, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1211, 耗时: 0.2327 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.60, 搜索节点数: 715.90, 耗时: 2.1857 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 134, 对应耗时: 0.3897 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.60, 搜索节点数: 715.90, 耗时: 2.1857 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4894, 对应耗时: 14.9874 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [7, 10, 1], 'Stack2': [6, 5, 12, 4], 'Stack3': [9, 11, 8, 3, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 35031, 耗时: 8.0151 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.90, 搜索节点数: 25773.90, 耗时: 79.7252 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 14479, 对应耗时: 44.3576 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 25773.90, 耗时: 79.7252 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 14479, 对应耗时: 44.3576 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [5, 8, 1, 3, 4], 'Stack2': [7, 6, 11, 2], 'Stack3': [9, 12, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 4810, 耗时: 0.5473 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.62, 搜索节点数: 9337.38, 耗时: 28.6973 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2356, 对应耗时: 7.0175 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.75, 搜索节点数: 9337.38, 耗时: 28.6973 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2356, 对应耗时: 7.0175 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [2, 12, 4, 9], 'Stack2': [7, 1, 6, 3], 'Stack3': [5, 11, 10, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 30384, 耗时: 7.1124 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 18881.67, 耗时: 61.4356 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 20862, 对应耗时: 64.4715 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.56, 搜索节点数: 18881.67, 耗时: 61.4356 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 20862, 对应耗时: 64.4715 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [10, 7, 8, 3, 11], 'Stack2': [5, 9], 'Stack3': [4, 12, 2, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 5784, 耗时: 1.2774 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 3258.00, 耗时: 11.4523 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3360, 对应耗时: 11.8866 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 3258.00, 耗时: 11.4523 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3360, 对应耗时: 11.8866 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [11, 2, 10, 4, 6], 'Stack2': [8, 12], 'Stack3': [3, 9, 7, 1, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 934, 耗时: 0.2118 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.40, 搜索节点数: 5116.90, 耗时: 17.8594 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 2489, 对应耗时: 8.7189 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.00, 搜索节点数: 5116.90, 耗时: 17.8594 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 2257, 对应耗时: 7.7990 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [3, 12, 9], 'Stack2': [8, 1, 2, 6], 'Stack3': [4, 10, 5, 7, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 1560, 耗时: 0.2886 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.60, 搜索节点数: 3697.40, 耗时: 13.0992 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3668, 对应耗时: 12.9476 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.90, 搜索节点数: 3697.40, 耗时: 13.0992 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 3668, 对应耗时: 12.9476 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [1, 10, 11], 'Stack2': [6, 9, 8, 2, 5], 'Stack3': [12, 3, 7, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2468, 耗时: 0.5147 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.10, 搜索节点数: 1783.00, 耗时: 6.2023 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 862, 对应耗时: 3.0093 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 1783.00, 耗时: 6.2023 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 862, 对应耗时: 3.0093 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [7, 9, 6], 'Stack2': [10, 11, 1, 8], 'Stack3': [4, 3, 12, 5, 2], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.5218 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.90, 搜索节点数: 16725.90, 耗时: 58.5312 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 5139, 对应耗时: 17.5440 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 16725.90, 耗时: 58.5312 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 5139, 对应耗时: 17.5440 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [6, 8], 'Stack2': [3, 10, 1, 2, 5], 'Stack3': [12, 4, 9, 11, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 1049, 耗时: 0.2171 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.40, 搜索节点数: 4239.90, 耗时: 14.8877 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 415, 对应耗时: 1.3966 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.70, 搜索节点数: 4239.90, 耗时: 14.8877 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 415, 对应耗时: 1.3966 秒


--------------------------------------------------------------------------------
配置 TotalS4_U60_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 28/30 个实例中找到解决方案。
    平均路径长度: 10.00
    平均搜索节点数: 6103.57
    平均耗时: 1.3521 秒

  LEGEND 算法总结:
    在 21/30 个实例中至少一次运行找到最优解。
    在 5/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 13.72
    所有实例的平均搜索节点数的平均值: 6691.63
    所有实例的平均耗时的平均值: 21.2028 秒

  LEGEND 算法总结（优化后）:
    在 23/30 个实例中至少一次优化找到最优解。
    在 5/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 12.81
    所有实例的平均搜索节点数的平均值: 6691.63
    所有实例的平均耗时的平均值: 21.2028 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U70_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [10, 9, 12, 7, 3], 'Stack2': [5, 1, 2, 13, 8], 'Stack3': [11, 6, 4, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2949, 耗时: 0.6314 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.90, 搜索节点数: 6901.60, 耗时: 24.6087 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 839, 对应耗时: 2.8600 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.10, 搜索节点数: 6901.60, 耗时: 24.6087 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 839, 对应耗时: 2.8600 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [14, 6, 1, 7], 'Stack2': [3, 5, 2, 13, 10], 'Stack3': [11, 4, 9, 8, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 600, 耗时: 0.0636 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 1374.50, 耗时: 4.8914 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 906, 对应耗时: 3.1294 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 1374.50, 耗时: 4.8914 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 574, 对应耗时: 2.0760 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [7, 9, 8, 14], 'Stack2': [6, 12, 4, 3, 1], 'Stack3': [13, 11, 10, 5, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 10223, 耗时: 2.6360 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.40, 搜索节点数: 3735.30, 耗时: 13.3000 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 217, 对应耗时: 0.7288 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 3735.30, 耗时: 13.3000 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 207, 对应耗时: 0.7012 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [8, 13, 1, 7, 12], 'Stack2': [11, 9, 3, 10], 'Stack3': [14, 6, 2, 5, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 27058, 耗时: 6.7535 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.44, 搜索节点数: 14251.44, 耗时: 51.3032 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 5456, 对应耗时: 19.6749 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.00, 搜索节点数: 14251.44, 耗时: 51.3032 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 4990, 对应耗时: 18.0325 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [14, 10, 4, 1, 7], 'Stack2': [12, 5, 6, 3, 13], 'Stack3': [9, 11, 8, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 17769, 耗时: 4.5286 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.10, 搜索节点数: 22154.10, 耗时: 76.9693 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4663, 对应耗时: 14.5114 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 22154.10, 耗时: 76.9693 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4663, 对应耗时: 14.5114 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [9, 7, 5, 2], 'Stack2': [1, 11, 8, 6, 13], 'Stack3': [4, 10, 3, 12, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 1538, 耗时: 0.1710 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.11, 搜索节点数: 4284.11, 耗时: 15.8911 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1771, 对应耗时: 5.5233 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.78, 搜索节点数: 4284.11, 耗时: 15.8911 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 418, 对应耗时: 1.7552 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [3, 13, 5, 4, 7], 'Stack2': [12, 11, 2, 10], 'Stack3': [14, 9, 1, 8, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 5074, 耗时: 1.6434 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 3215.90, 耗时: 14.4103 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2484, 对应耗时: 15.5160 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.40, 搜索节点数: 3215.90, 耗时: 14.4103 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2484, 对应耗时: 15.5160 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [13, 14, 9, 1, 4], 'Stack2': [11, 6, 7, 3, 12], 'Stack3': [10, 5, 8, 2], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.4924 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [11, 14, 1, 4, 12], 'Stack2': [7, 5, 3, 6, 9], 'Stack3': [10, 2, 8, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 54631, 耗时: 13.8342 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.75, 搜索节点数: 31539.25, 耗时: 101.2481 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 39311, 对应耗时: 129.9733 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.25, 搜索节点数: 31539.25, 耗时: 101.2481 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 17877, 对应耗时: 55.9365 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [12, 6, 3, 13, 1], 'Stack2': [8, 10, 4, 2, 11], 'Stack3': [14, 7, 5, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 10241, 耗时: 2.4761 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 5359.30, 耗时: 16.7159 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 850, 对应耗时: 2.6359 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.10, 搜索节点数: 5359.30, 耗时: 16.7159 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 850, 对应耗时: 2.6359 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [10, 7, 3, 2, 4], 'Stack2': [9, 13, 5, 8, 6], 'Stack3': [14, 1, 11, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1524, 耗时: 0.4154 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 4295.70, 耗时: 13.3549 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1864, 对应耗时: 5.7137 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 4295.70, 耗时: 13.3549 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1762, 对应耗时: 5.5397 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [10, 14, 2, 6, 4], 'Stack2': [8, 5, 7, 1], 'Stack3': [13, 11, 9, 3, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 45061, 耗时: 11.0901 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.12, 搜索节点数: 11375.12, 耗时: 45.2782 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 24459, 对应耗时: 76.5555 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.38, 搜索节点数: 11375.12, 耗时: 45.2782 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 24459, 对应耗时: 76.5555 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [11, 12, 7, 9, 5], 'Stack2': [10, 6, 3, 4, 8], 'Stack3': [14, 2, 1, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 8871, 耗时: 2.0019 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 15341.50, 耗时: 48.2419 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 10895, 对应耗时: 33.9944 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.60, 搜索节点数: 15341.50, 耗时: 48.2419 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 24948, 对应耗时: 78.4469 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [7, 14, 6, 3, 1], 'Stack2': [13, 10, 2, 5, 9], 'Stack3': [12, 8, 4, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 57501, 耗时: 14.2434 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.60, 搜索节点数: 20774.80, 耗时: 65.0951 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 27355, 对应耗时: 86.5900 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 20774.80, 耗时: 65.0951 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 9704, 对应耗时: 29.7586 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [14, 8, 10, 1], 'Stack2': [11, 12, 9, 7, 3], 'Stack3': [6, 5, 4, 2, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 44116, 耗时: 10.8194 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.67, 搜索节点数: 20784.17, 耗时: 65.7799 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 9101, 对应耗时: 28.4161 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 20784.17, 耗时: 65.7799 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 9101, 对应耗时: 28.4161 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [12, 9, 4, 1, 2], 'Stack2': [13, 6, 8, 7], 'Stack3': [11, 14, 10, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.9249 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 36740.00, 耗时: 114.6383 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 36740, 对应耗时: 114.6383 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 36740.00, 耗时: 114.6383 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 36740, 对应耗时: 114.6383 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [13, 9, 3, 1, 8], 'Stack2': [11, 12, 6, 5, 10], 'Stack3': [14, 2, 4, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 5151, 耗时: 0.6346 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 14201.40, 耗时: 50.3088 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3755, 对应耗时: 13.4011 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 14201.40, 耗时: 50.3088 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3755, 对应耗时: 13.4011 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [13, 10, 11, 12, 5], 'Stack2': [9, 7, 1, 2], 'Stack3': [14, 3, 6, 4, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1435, 耗时: 0.1658 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.90, 搜索节点数: 3895.80, 耗时: 12.8141 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 337, 对应耗时: 1.0130 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 3895.80, 耗时: 12.8141 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 3530, 对应耗时: 10.9310 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [11, 2, 14, 5], 'Stack2': [6, 13, 3, 1, 4], 'Stack3': [10, 8, 7, 12, 9], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 34.8411 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.86, 搜索节点数: 24004.86, 耗时: 92.5691 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 10070, 对应耗时: 32.0571 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.14, 搜索节点数: 24004.86, 耗时: 92.5691 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 10070, 对应耗时: 32.0571 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [8, 4, 7, 13], 'Stack2': [6, 9, 5, 3, 12], 'Stack3': [11, 10, 2, 1, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1182, 耗时: 0.1359 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.10, 搜索节点数: 2161.40, 耗时: 6.6877 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3469, 对应耗时: 10.8543 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 2161.40, 耗时: 6.6877 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1236, 对应耗时: 3.9359 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [8, 7, 5, 13, 4], 'Stack2': [12, 14, 11, 2], 'Stack3': [9, 10, 6, 3, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.5163 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 6401.00, 耗时: 20.1123 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 6401, 对应耗时: 20.1123 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 6401.00, 耗时: 20.1123 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6401, 对应耗时: 20.1123 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [11, 8, 6, 5], 'Stack2': [3, 4, 9, 1, 14], 'Stack3': [12, 10, 13, 2, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 170, 耗时: 0.0184 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 296.40, 耗时: 1.1747 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 417, 对应耗时: 1.4359 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.30, 搜索节点数: 296.40, 耗时: 1.1747 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 254, 对应耗时: 1.1950 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [11, 3, 6, 10, 1], 'Stack2': [14, 9, 13, 7, 2], 'Stack3': [12, 8, 4, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 14571, 耗时: 3.5762 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 10565.00, 耗时: 47.1335 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1115, 对应耗时: 3.5469 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.40, 搜索节点数: 10565.00, 耗时: 47.1335 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1115, 对应耗时: 3.5469 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [11, 8, 4, 1, 10], 'Stack2': [6, 14, 9, 3, 13], 'Stack3': [7, 2, 5, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 11336, 耗时: 5.0348 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 12643.14, 耗时: 53.9940 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 8052, 对应耗时: 42.8700 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.57, 搜索节点数: 12643.14, 耗时: 53.9940 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 23105, 对应耗时: 109.1454 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [11, 13, 3, 1], 'Stack2': [14, 10, 7, 5, 4], 'Stack3': [9, 12, 6, 2, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 6323, 耗时: 1.6090 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 4621.80, 耗时: 20.7349 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1700, 对应耗时: 6.6254 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 4621.80, 耗时: 20.7349 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 214, 对应耗时: 0.8000 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [13, 4, 8, 11], 'Stack2': [14, 2, 5, 7, 6], 'Stack3': [10, 3, 1, 9, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 79, 耗时: 0.0125 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.50, 搜索节点数: 1741.40, 耗时: 7.9919 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 76, 对应耗时: 0.3838 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.50, 搜索节点数: 1741.40, 耗时: 7.9919 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 76, 对应耗时: 0.3838 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [2, 9, 7, 8], 'Stack2': [13, 12, 1, 5, 6], 'Stack3': [14, 11, 3, 10, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 99, 耗时: 0.0110 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.70, 搜索节点数: 202.10, 耗时: 0.9695 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 67, 对应耗时: 0.3185 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.50, 搜索节点数: 202.10, 耗时: 0.9695 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 67, 对应耗时: 0.3185 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [11, 6, 10, 5, 2], 'Stack2': [7, 1, 9, 3, 8], 'Stack3': [13, 12, 4, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 6396, 耗时: 1.3361 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 5768.40, 耗时: 24.5210 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3236, 对应耗时: 11.2070 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 5768.40, 耗时: 24.5210 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3236, 对应耗时: 11.2070 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [11, 6, 5, 3], 'Stack2': [4, 1, 13, 12, 8], 'Stack3': [9, 10, 2, 7, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 2275, 耗时: 0.2593 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.30, 搜索节点数: 5111.80, 耗时: 18.7061 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 414, 对应耗时: 1.2342 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.70, 搜索节点数: 5111.80, 耗时: 18.7061 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2319, 对应耗时: 7.2398 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [10, 12, 1, 4, 7], 'Stack2': [11, 9, 6, 8, 2], 'Stack3': [13, 5, 14, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 5303, 耗时: 1.5923 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.10, 搜索节点数: 6974.60, 耗时: 29.1717 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 771, 对应耗时: 3.6986 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 6974.60, 耗时: 29.1717 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 771, 对应耗时: 3.6986 秒


--------------------------------------------------------------------------------
配置 TotalS4_U70_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 26/30 个实例中找到解决方案。
    平均路径长度: 9.77
    平均搜索节点数: 13133.69
    平均耗时: 3.2959 秒

  LEGEND 算法总结:
    在 18/30 个实例中至少一次运行找到最优解。
    在 11/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 12.72
    所有实例的平均搜索节点数的平均值: 10369.51
    所有实例的平均耗时的平均值: 36.5040 秒

  LEGEND 算法总结（优化后）:
    在 20/30 个实例中至少一次优化找到最优解。
    在 11/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 11.92
    所有实例的平均搜索节点数的平均值: 10369.51
    所有实例的平均耗时的平均值: 36.5040 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U70_D60
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [11, 10, 5, 9, 3], 'Stack2': [4, 2, 8, 1], 'Stack3': [7, 14, 12, 13, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 16069, 耗时: 6.3498 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 16451.89, 耗时: 64.2771 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16177, 对应耗时: 71.5686 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.11, 搜索节点数: 16451.89, 耗时: 64.2771 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16177, 对应耗时: 71.5686 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [7, 14, 6, 1, 11], 'Stack2': [12, 5, 4, 8, 3], 'Stack3': [9, 2, 13, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 80571, 耗时: 19.6376 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 22.00, 搜索节点数: 18846.00, 耗时: 92.3677 秒
    10次运行最优值: 最优路径长度: 22, 对应搜索节点数: 18846, 对应耗时: 92.3677 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 18846.00, 耗时: 92.3677 秒
    10次运行最优值: 最优路径长度: 20, 对应搜索节点数: 18846, 对应耗时: 92.3677 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [11, 5, 3, 1, 14], 'Stack2': [7, 9, 8, 13, 2], 'Stack3': [10, 12, 4, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 12100, 耗时: 2.6514 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 10910.67, 耗时: 35.8470 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 11471, 对应耗时: 39.1405 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.89, 搜索节点数: 10910.67, 耗时: 35.8470 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 3864, 对应耗时: 12.1439 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [14, 5, 13, 9, 11], 'Stack2': [10, 12, 3, 7], 'Stack3': [6, 2, 4, 1, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 16346, 耗时: 4.2814 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.86, 搜索节点数: 19565.57, 耗时: 74.9872 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 15408, 对应耗时: 47.7793 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.43, 搜索节点数: 19565.57, 耗时: 74.9872 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 15408, 对应耗时: 47.7793 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [13, 14, 6, 5, 3], 'Stack2': [4, 2, 11, 10, 12], 'Stack3': [9, 8, 1, 7], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.0785 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 25.75, 搜索节点数: 24433.25, 耗时: 76.1873 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 22803, 对应耗时: 71.3803 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 21.25, 搜索节点数: 24433.25, 耗时: 76.1873 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 22803, 对应耗时: 71.3803 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [10, 7, 5, 3, 9], 'Stack2': [4, 14, 13, 1], 'Stack3': [8, 11, 6, 12, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 35483, 耗时: 8.5385 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.88, 搜索节点数: 12137.88, 耗时: 38.0500 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 4433, 对应耗时: 13.8456 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.88, 搜索节点数: 12137.88, 耗时: 38.0500 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 11432, 对应耗时: 36.3142 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [4, 3, 6, 14], 'Stack2': [11, 5, 10, 2, 1], 'Stack3': [13, 8, 9, 12, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 5768, 耗时: 1.2122 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.90, 搜索节点数: 7763.40, 耗时: 24.2353 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 6060, 对应耗时: 18.9432 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.60, 搜索节点数: 7763.40, 耗时: 24.2353 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 6060, 对应耗时: 18.9432 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [12, 6, 7, 2], 'Stack2': [1, 13, 9, 10, 5], 'Stack3': [11, 4, 3, 8, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 2248, 耗时: 0.4964 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.50, 搜索节点数: 7161.00, 耗时: 28.8947 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 965, 对应耗时: 3.0665 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 7161.00, 耗时: 28.8947 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 965, 对应耗时: 3.0665 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [13, 3, 7, 10, 4], 'Stack2': [9, 2, 1, 12], 'Stack3': [8, 11, 5, 14, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 14766, 耗时: 4.6996 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.70, 搜索节点数: 16153.70, 耗时: 54.2269 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 10211, 对应耗时: 31.7186 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.30, 搜索节点数: 16153.70, 耗时: 54.2269 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 10211, 对应耗时: 31.7186 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [12, 6, 8, 9, 1], 'Stack2': [5, 13, 4, 3, 2], 'Stack3': [11, 10, 7, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 23528, 耗时: 5.6666 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.20, 搜索节点数: 11589.30, 耗时: 36.3084 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2643, 对应耗时: 8.0838 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.40, 搜索节点数: 11589.30, 耗时: 36.3084 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2643, 对应耗时: 8.0838 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [7, 9, 8, 11, 5], 'Stack2': [4, 3, 6, 12], 'Stack3': [13, 10, 2, 14, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 5281, 耗时: 0.9975 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.33, 搜索节点数: 12365.78, 耗时: 41.2510 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 11273, 对应耗时: 36.1976 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.11, 搜索节点数: 12365.78, 耗时: 41.2510 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 3256, 对应耗时: 9.7320 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [5, 2, 8, 4, 11], 'Stack2': [14, 9, 1, 12], 'Stack3': [6, 13, 3, 10, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 25433, 耗时: 5.9695 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.12, 搜索节点数: 20247.88, 耗时: 62.8315 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16466, 对应耗时: 51.1288 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.88, 搜索节点数: 20247.88, 耗时: 62.8315 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16466, 对应耗时: 51.1288 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [11, 8, 13, 2, 14], 'Stack2': [4, 12, 6, 9, 7], 'Stack3': [10, 5, 1, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 1904, 耗时: 0.2116 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.90, 搜索节点数: 6692.40, 耗时: 20.9232 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1355, 对应耗时: 4.0773 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.50, 搜索节点数: 6692.40, 耗时: 20.9232 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1355, 对应耗时: 4.0773 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [12, 1, 13, 2], 'Stack2': [14, 7, 8, 5, 9], 'Stack3': [10, 4, 6, 11, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 11311, 耗时: 2.6602 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.70, 搜索节点数: 6055.50, 耗时: 18.8554 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 10850, 对应耗时: 33.8138 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.60, 搜索节点数: 6055.50, 耗时: 18.8554 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1795, 对应耗时: 5.3863 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [7, 13, 4, 3], 'Stack2': [11, 8, 5, 6, 1], 'Stack3': [14, 10, 12, 2, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 39889, 耗时: 10.0471 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.88, 搜索节点数: 13927.50, 耗时: 43.4385 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 5672, 对应耗时: 17.2315 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.50, 搜索节点数: 13927.50, 耗时: 43.4385 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 5672, 对应耗时: 17.2315 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [9, 8, 5, 14, 4], 'Stack2': [3, 11, 1, 12], 'Stack3': [13, 7, 10, 6, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2783, 耗时: 0.3276 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 4099.00, 耗时: 12.7282 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 732, 对应耗时: 2.3081 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 4099.00, 耗时: 12.7282 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 732, 对应耗时: 2.3081 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [7, 3, 5, 10], 'Stack2': [9, 11, 8, 1, 12], 'Stack3': [14, 13, 2, 4, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 8905, 耗时: 2.0003 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.70, 搜索节点数: 15591.60, 耗时: 48.7492 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4493, 对应耗时: 13.9759 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.20, 搜索节点数: 15591.60, 耗时: 48.7492 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 4493, 对应耗时: 13.9759 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [10, 14, 3, 9, 12], 'Stack2': [8, 6, 1, 5], 'Stack3': [13, 2, 11, 7, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.9603 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.80, 搜索节点数: 23342.80, 耗时: 73.0718 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 23480, 对应耗时: 73.6147 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 23342.80, 耗时: 73.0718 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 23480, 对应耗时: 73.6147 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [8, 6, 4, 13, 2], 'Stack2': [9, 5, 7, 3, 12], 'Stack3': [1, 10, 14, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 1960, 耗时: 0.2213 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.60, 搜索节点数: 5658.80, 耗时: 17.7718 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 19077, 对应耗时: 60.9417 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.20, 搜索节点数: 5658.80, 耗时: 17.7718 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 235, 对应耗时: 0.7026 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [10, 1, 7, 5], 'Stack2': [12, 3, 14, 13, 2], 'Stack3': [9, 6, 11, 8, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 13035, 耗时: 2.8811 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.86, 搜索节点数: 18726.86, 耗时: 59.1584 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 23175, 对应耗时: 73.1792 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.71, 搜索节点数: 18726.86, 耗时: 59.1584 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 23175, 对应耗时: 73.1792 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [1, 10, 4, 2], 'Stack2': [7, 6, 3, 9, 14], 'Stack3': [12, 8, 11, 13, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 2714, 耗时: 0.3141 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.90, 搜索节点数: 3434.50, 耗时: 14.4346 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 1760, 对应耗时: 5.5761 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.70, 搜索节点数: 3434.50, 耗时: 14.4346 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1760, 对应耗时: 5.5761 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [10, 8, 6, 14, 5], 'Stack2': [7, 11, 12, 1], 'Stack3': [4, 2, 13, 3, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 1981, 耗时: 0.2368 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.70, 搜索节点数: 3445.90, 耗时: 16.0648 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 327, 对应耗时: 0.9895 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 3445.90, 耗时: 16.0648 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 327, 对应耗时: 0.9895 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [3, 10, 2, 1, 13], 'Stack2': [8, 5, 11, 14], 'Stack3': [12, 9, 4, 6, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2206, 耗时: 0.5050 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.25, 搜索节点数: 9957.62, 耗时: 32.5900 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3651, 对应耗时: 12.8902 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.50, 搜索节点数: 9957.62, 耗时: 32.5900 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 3651, 对应耗时: 12.8902 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [14, 8, 6, 5, 7], 'Stack2': [1, 13, 9, 12], 'Stack3': [10, 11, 2, 4, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 2670, 耗时: 0.5542 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.60, 搜索节点数: 6719.80, 耗时: 24.1749 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1860, 对应耗时: 6.4026 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.20, 搜索节点数: 6719.80, 耗时: 24.1749 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1860, 对应耗时: 6.4026 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [13, 8, 1, 11, 10], 'Stack2': [4, 3, 5, 12], 'Stack3': [7, 14, 6, 2, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 92763, 耗时: 21.7753 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.43, 搜索节点数: 13092.57, 耗时: 44.6373 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6001, 对应耗时: 21.6692 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.14, 搜索节点数: 13092.57, 耗时: 44.6373 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6001, 对应耗时: 21.6692 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [11, 13, 10, 5, 4], 'Stack2': [6, 14, 2, 3, 1], 'Stack3': [12, 9, 8, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 54077, 耗时: 14.0854 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.43, 搜索节点数: 16224.71, 耗时: 62.0187 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 7279, 对应耗时: 22.0055 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 16224.71, 耗时: 62.0187 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 7279, 对应耗时: 22.0055 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [6, 12, 2, 1, 9], 'Stack2': [8, 5, 14, 3], 'Stack3': [13, 10, 11, 4, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 97631, 耗时: 22.6741 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.40, 搜索节点数: 24167.00, 耗时: 75.1990 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 16091, 对应耗时: 51.5873 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 24167.00, 耗时: 75.1990 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 16091, 对应耗时: 51.5873 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [12, 2, 6, 14, 4], 'Stack2': [10, 13, 7, 9], 'Stack3': [11, 8, 3, 5, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.4939 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.62, 搜索节点数: 32869.12, 耗时: 112.5137 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 49208, 对应耗时: 178.5035 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.50, 搜索节点数: 32869.12, 耗时: 112.5137 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 49208, 对应耗时: 178.5035 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [11, 3, 10, 12], 'Stack2': [2, 6, 1, 7, 13], 'Stack3': [14, 9, 4, 8, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 248, 耗时: 0.0268 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 428.30, 耗时: 1.5180 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 381, 对应耗时: 1.3130 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.20, 搜索节点数: 428.30, 耗时: 1.5180 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 381, 对应耗时: 1.3130 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [7, 11, 4, 2], 'Stack2': [12, 3, 1, 10, 8], 'Stack3': [9, 13, 6, 5, 14], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.9456 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 17011.50, 耗时: 70.7221 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 9321, 对应耗时: 28.5019 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.50, 搜索节点数: 17011.50, 耗时: 70.7221 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 9321, 对应耗时: 28.5019 秒


--------------------------------------------------------------------------------
配置 TotalS4_U70_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 26/30 个实例中找到解决方案。
    平均路径长度: 11.27
    平均搜索节点数: 21987.31
    平均耗时: 5.3470 秒

  LEGEND 算法总结:
    在 13/30 个实例中至少一次运行找到最优解。
    在 17/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 16.19
    所有实例的平均搜索节点数的平均值: 13302.39
    所有实例的平均耗时的平均值: 45.9345 秒

  LEGEND 算法总结（优化后）:
    在 17/30 个实例中至少一次优化找到最优解。
    在 17/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 14.96
    所有实例的平均搜索节点数的平均值: 13302.39
    所有实例的平均耗时的平均值: 45.9345 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U70_D70
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [10, 3, 12, 13], 'Stack2': [4, 8, 1, 7, 6], 'Stack3': [9, 11, 5, 14, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 24214, 耗时: 5.4058 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 14665.62, 耗时: 45.9992 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 38904, 对应耗时: 122.3507 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.62, 搜索节点数: 14665.62, 耗时: 45.9992 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 5539, 对应耗时: 17.4425 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [4, 11, 3, 10, 2], 'Stack2': [6, 1, 5, 9, 12], 'Stack3': [8, 13, 14, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 67322, 耗时: 16.1191 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.25, 搜索节点数: 18842.62, 耗时: 61.5344 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 9213, 对应耗时: 29.3882 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.50, 搜索节点数: 18842.62, 耗时: 61.5344 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 9213, 对应耗时: 29.3882 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [1, 9, 11, 14, 4], 'Stack2': [6, 10, 3, 2, 7], 'Stack3': [12, 13, 8, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 34.8777 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 25838.50, 耗时: 80.9036 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 35793, 对应耗时: 111.9315 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 25838.50, 耗时: 80.9036 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 35793, 对应耗时: 111.9315 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [11, 1, 10, 14], 'Stack2': [2, 6, 9, 7, 13], 'Stack3': [3, 4, 5, 12, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 10107, 耗时: 1.4935 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 22.11, 搜索节点数: 10343.78, 耗时: 32.5380 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 3874, 对应耗时: 12.0623 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.78, 搜索节点数: 10343.78, 耗时: 32.5380 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 3874, 对应耗时: 12.0623 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [6, 8, 11, 14, 10], 'Stack2': [2, 3, 7, 12], 'Stack3': [4, 9, 1, 13, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 234, 耗时: 0.0251 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.60, 搜索节点数: 15688.20, 耗时: 49.1196 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 3126, 对应耗时: 9.8351 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.90, 搜索节点数: 15688.20, 耗时: 49.1196 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 3126, 对应耗时: 9.8351 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [4, 2, 11, 10, 13], 'Stack2': [8, 14, 6, 3, 1], 'Stack3': [5, 12, 9, 7], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.0656 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [2, 11, 12, 10, 13], 'Stack2': [1, 5, 3, 14], 'Stack3': [8, 7, 9, 6, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 10807, 耗时: 1.6034 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.80, 搜索节点数: 14547.50, 耗时: 45.4309 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 2109, 对应耗时: 6.2994 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.10, 搜索节点数: 14547.50, 耗时: 45.4309 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2109, 对应耗时: 6.2994 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [2, 12, 5, 10], 'Stack2': [8, 11, 7, 4, 14], 'Stack3': [6, 1, 3, 9, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 74893, 耗时: 17.5702 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.57, 搜索节点数: 24627.14, 耗时: 85.8001 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 45433, 对应耗时: 195.6979 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.86, 搜索节点数: 24627.14, 耗时: 85.8001 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 20555, 对应耗时: 63.9326 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [6, 7, 13, 12], 'Stack2': [11, 14, 2, 10, 1], 'Stack3': [9, 5, 8, 3, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 38815, 耗时: 11.7376 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.43, 搜索节点数: 19227.86, 耗时: 91.6684 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 36245, 对应耗时: 150.5988 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 19227.86, 耗时: 91.6684 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6638, 对应耗时: 31.5285 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [6, 4, 10, 1, 5], 'Stack2': [8, 9, 3, 13], 'Stack3': [7, 11, 14, 2, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 4454, 耗时: 1.2196 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.11, 搜索节点数: 15612.22, 耗时: 74.5418 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 9436, 对应耗时: 49.4773 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.22, 搜索节点数: 15612.22, 耗时: 74.5418 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 591, 对应耗时: 3.1738 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [9, 8, 13, 3, 10], 'Stack2': [1, 2, 11, 7, 6], 'Stack3': [5, 12, 4, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 17178, 耗时: 4.4125 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.44, 搜索节点数: 14466.00, 耗时: 74.3655 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 681, 对应耗时: 2.0459 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.44, 搜索节点数: 14466.00, 耗时: 74.3655 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 24556, 对应耗时: 132.2516 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [9, 14, 2, 8], 'Stack2': [10, 1, 6, 3, 7], 'Stack3': [5, 13, 12, 11, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.9372 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 40029.50, 耗时: 193.2555 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 32139, 对应耗时: 130.5377 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 40029.50, 耗时: 193.2555 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 32139, 对应耗时: 130.5377 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [11, 3, 12, 9], 'Stack2': [1, 6, 4, 8, 7], 'Stack3': [10, 14, 2, 13, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.1281 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 26.00, 搜索节点数: 35334.00, 耗时: 123.6878 秒
    10次运行最优值: 最优路径长度: 26, 对应搜索节点数: 35334, 对应耗时: 123.6878 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 35334.00, 耗时: 123.6878 秒
    10次运行最优值: 最优路径长度: 20, 对应搜索节点数: 35334, 对应耗时: 123.6878 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [2, 12, 1, 3, 9], 'Stack2': [10, 7, 14, 11], 'Stack3': [4, 13, 5, 8, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.8857 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.67, 搜索节点数: 15935.83, 耗时: 56.5365 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 23127, 对应耗时: 82.5210 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 15935.83, 耗时: 56.5365 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 23127, 对应耗时: 82.5210 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [9, 1, 2, 4, 7], 'Stack2': [8, 13, 6, 14], 'Stack3': [10, 11, 12, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.4299 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 22.83, 搜索节点数: 20650.00, 耗时: 69.3983 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 4691, 对应耗时: 16.7803 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 20650.00, 耗时: 69.3983 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 4691, 对应耗时: 16.7803 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [2, 12, 6, 10, 1], 'Stack2': [8, 13, 7, 5, 9], 'Stack3': [14, 3, 11, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.1541 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.40, 搜索节点数: 16649.80, 耗时: 51.6255 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 14412, 对应耗时: 44.4199 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.80, 搜索节点数: 16649.80, 耗时: 51.6255 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 14412, 对应耗时: 44.4199 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [10, 14, 13, 6, 2], 'Stack2': [12, 3, 8, 7, 5], 'Stack3': [4, 11, 1, 9], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.9325 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [12, 4, 14, 8, 3], 'Stack2': [1, 11, 7, 5], 'Stack3': [6, 10, 2, 13, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 62932, 耗时: 14.2966 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 23.89, 搜索节点数: 21525.89, 耗时: 67.3308 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 21436, 对应耗时: 67.2792 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 22.44, 搜索节点数: 21525.89, 耗时: 67.3308 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 30320, 对应耗时: 94.7546 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [13, 5, 10, 2], 'Stack2': [9, 11, 3, 14, 4], 'Stack3': [7, 12, 6, 1, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 44713, 耗时: 10.5724 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.56, 搜索节点数: 22103.56, 耗时: 77.7933 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 15159, 对应耗时: 48.6093 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.67, 搜索节点数: 22103.56, 耗时: 77.7933 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 15159, 对应耗时: 48.6093 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [10, 11, 6, 7], 'Stack2': [3, 14, 9, 2, 1], 'Stack3': [12, 5, 8, 4, 13], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.3013 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.80, 搜索节点数: 27085.40, 耗时: 90.6966 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 32022, 对应耗时: 102.0066 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.40, 搜索节点数: 27085.40, 耗时: 90.6966 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 32022, 对应耗时: 102.0066 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [1, 10, 2, 8, 14], 'Stack2': [4, 12, 3, 13, 7], 'Stack3': [6, 5, 9, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 2365, 耗时: 0.2735 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.62, 搜索节点数: 7770.75, 耗时: 24.5067 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 283, 对应耗时: 0.8223 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.75, 搜索节点数: 7770.75, 耗时: 24.5067 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 737, 对应耗时: 2.2357 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [10, 3, 5, 9, 8], 'Stack2': [2, 7, 4, 12], 'Stack3': [11, 14, 1, 6, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 18454, 耗时: 4.0525 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.22, 搜索节点数: 19313.56, 耗时: 66.1141 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 2851, 对应耗时: 8.8277 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.67, 搜索节点数: 19313.56, 耗时: 66.1141 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 2851, 对应耗时: 8.8277 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [3, 1, 4, 10], 'Stack2': [12, 13, 11, 8, 6], 'Stack3': [9, 14, 2, 7, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.4413 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [8, 9, 3, 7, 14], 'Stack2': [11, 13, 6, 10, 2], 'Stack3': [12, 4, 5, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.9722 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.50, 搜索节点数: 19704.00, 耗时: 62.6040 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 32076, 对应耗时: 100.5458 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.50, 搜索节点数: 19704.00, 耗时: 62.6040 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 32076, 对应耗时: 100.5458 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [5, 3, 8, 1, 7], 'Stack2': [2, 4, 13, 10], 'Stack3': [12, 14, 11, 6, 9], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.3759 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [6, 4, 7, 14, 11], 'Stack2': [9, 10, 3, 13], 'Stack3': [2, 5, 1, 8, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 2983, 耗时: 0.3531 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.90, 搜索节点数: 9312.70, 耗时: 29.2987 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 5720, 对应耗时: 17.6944 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.90, 搜索节点数: 9312.70, 耗时: 29.2987 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 5720, 对应耗时: 17.6944 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [9, 1, 3, 8, 11], 'Stack2': [4, 13, 5, 12, 7], 'Stack3': [2, 6, 10, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 76831, 耗时: 17.6121 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.67, 搜索节点数: 6172.00, 耗时: 19.2606 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1632, 对应耗时: 4.9392 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 6172.00, 耗时: 19.2606 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1288, 对应耗时: 3.8908 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [9, 13, 6, 8, 14], 'Stack2': [11, 12, 7, 1, 10], 'Stack3': [4, 5, 2, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.6721 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 24.00, 搜索节点数: 16342.00, 耗时: 50.4103 秒
    10次运行最优值: 最优路径长度: 24, 对应搜索节点数: 16342, 对应耗时: 50.4103 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 23.00, 搜索节点数: 16342.00, 耗时: 50.4103 秒
    10次运行最优值: 最优路径长度: 23, 对应搜索节点数: 16342, 对应耗时: 50.4103 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [6, 10, 8, 9, 13], 'Stack2': [5, 3, 12, 1, 11], 'Stack3': [4, 7, 2, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 3465, 耗时: 0.8352 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.67, 搜索节点数: 8079.22, 耗时: 25.1140 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 1187, 对应耗时: 3.6614 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.56, 搜索节点数: 8079.22, 耗时: 25.1140 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1187, 对应耗时: 3.6614 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [5, 10, 9, 1, 13], 'Stack2': [11, 4, 12, 7, 14], 'Stack3': [3, 8, 2, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 6499, 耗时: 1.4236 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.56, 搜索节点数: 14381.22, 耗时: 44.9854 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 9569, 对应耗时: 29.2337 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.56, 搜索节点数: 14381.22, 耗时: 44.9854 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 9569, 对应耗时: 29.2337 秒


--------------------------------------------------------------------------------
配置 TotalS4_U70_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 17/30 个实例中找到解决方案。
    平均路径长度: 13.29
    平均搜索节点数: 27427.41
    平均耗时: 6.4121 秒

  LEGEND 算法总结:
    在 5/30 个实例中至少一次运行找到最优解。
    在 23/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 19.62
    所有实例的平均搜索节点数的平均值: 18240.34
    所有实例的平均耗时的平均值: 65.1738 秒

  LEGEND 算法总结（优化后）:
    在 7/30 个实例中至少一次优化找到最优解。
    在 23/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 18.12
    所有实例的平均搜索节点数的平均值: 18240.34
    所有实例的平均耗时的平均值: 65.1738 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U80_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [14, 11, 8, 10, 7], 'Stack2': [12, 9, 2, 5, 3], 'Stack3': [4, 1, 13, 6, 15], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 39327, 耗时: 9.4260 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.78, 搜索节点数: 12371.11, 耗时: 39.1491 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 7054, 对应耗时: 21.5590 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.78, 搜索节点数: 12371.11, 耗时: 39.1491 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 7054, 对应耗时: 21.5590 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [11, 13, 7, 3, 1], 'Stack2': [15, 9, 10, 8, 4], 'Stack3': [14, 12, 6, 5, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 75679, 耗时: 19.5679 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 25675.50, 耗时: 80.8367 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 15545, 对应耗时: 48.4696 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 25675.50, 耗时: 80.8367 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 15545, 对应耗时: 48.4696 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [11, 13, 9, 6, 5], 'Stack2': [14, 8, 7, 1, 2], 'Stack3': [4, 3, 15, 12, 10], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.7040 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [14, 8, 7, 5, 15], 'Stack2': [12, 11, 2, 4, 6], 'Stack3': [10, 13, 3, 9, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2774, 耗时: 0.3467 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.90, 搜索节点数: 8993.90, 耗时: 28.5540 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 7013, 对应耗时: 22.5057 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.80, 搜索节点数: 8993.90, 耗时: 28.5540 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 7013, 对应耗时: 22.5057 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [11, 9, 6, 5, 2], 'Stack2': [12, 15, 10, 14, 1], 'Stack3': [8, 7, 13, 4, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.5384 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.33, 搜索节点数: 16197.67, 耗时: 51.0961 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 24988, 对应耗时: 79.0025 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.67, 搜索节点数: 16197.67, 耗时: 51.0961 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 24988, 对应耗时: 79.0025 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [10, 6, 4, 11, 2], 'Stack2': [14, 8, 12, 7, 15], 'Stack3': [13, 5, 9, 3, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2516, 耗时: 0.3148 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 9218.00, 耗时: 33.1163 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 2074, 对应耗时: 7.1898 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.70, 搜索节点数: 9218.00, 耗时: 33.1163 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 2074, 对应耗时: 7.1898 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [13, 10, 9, 3, 12], 'Stack2': [5, 7, 4, 2, 14], 'Stack3': [11, 15, 8, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 75379, 耗时: 19.4257 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 17621.25, 耗时: 58.8321 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 14019, 对应耗时: 44.6297 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 17621.25, 耗时: 58.8321 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 14019, 对应耗时: 44.6297 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [14, 9, 5, 15, 7], 'Stack2': [8, 10, 6, 3, 2], 'Stack3': [13, 11, 12, 1, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 26.0371 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.67, 搜索节点数: 27212.33, 耗时: 86.5148 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 35745, 对应耗时: 113.3977 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.67, 搜索节点数: 27212.33, 耗时: 86.5148 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 35745, 对应耗时: 113.3977 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [13, 6, 7, 2, 4], 'Stack2': [12, 14, 8, 5, 15], 'Stack3': [11, 10, 9, 1, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 43498, 耗时: 11.1208 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.75, 搜索节点数: 22989.25, 耗时: 73.3515 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 9402, 对应耗时: 29.5974 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.75, 搜索节点数: 22989.25, 耗时: 73.3515 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 9402, 对应耗时: 29.5974 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [11, 14, 9, 3, 10], 'Stack2': [12, 5, 2, 4, 1], 'Stack3': [15, 13, 7, 8, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.7664 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [1, 6, 14, 3, 10], 'Stack2': [12, 11, 5, 4, 2], 'Stack3': [15, 9, 13, 7, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 3169, 耗时: 0.3796 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.70, 搜索节点数: 8802.00, 耗时: 27.5439 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 14415, 对应耗时: 45.1356 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 8802.00, 耗时: 27.5439 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1766, 对应耗时: 5.4289 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [8, 3, 5, 11, 15], 'Stack2': [13, 12, 7, 9, 1], 'Stack3': [14, 4, 2, 6, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 158, 耗时: 0.0176 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.89, 搜索节点数: 3200.00, 耗时: 9.9790 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 316, 对应耗时: 0.9527 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.33, 搜索节点数: 3200.00, 耗时: 9.9790 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 316, 对应耗时: 0.9527 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [15, 13, 2, 12, 6], 'Stack2': [14, 4, 1, 8, 9], 'Stack3': [10, 7, 11, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 6456, 耗时: 1.0577 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.90, 搜索节点数: 3270.40, 耗时: 10.2990 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 860, 对应耗时: 2.6138 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.90, 搜索节点数: 3270.40, 耗时: 10.2990 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 860, 对应耗时: 2.6138 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [7, 8, 3, 1, 5], 'Stack2': [14, 10, 11, 9, 6], 'Stack3': [15, 13, 12, 2, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 32432, 耗时: 8.0186 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.71, 搜索节点数: 31438.14, 耗时: 98.7939 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 28035, 对应耗时: 87.5763 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.86, 搜索节点数: 31438.14, 耗时: 98.7939 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 28035, 对应耗时: 87.5763 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [5, 2, 10, 12, 14], 'Stack2': [3, 15, 8, 1, 9], 'Stack3': [13, 11, 7, 6, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 228, 耗时: 0.0257 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.90, 搜索节点数: 592.70, 耗时: 1.8548 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 141, 对应耗时: 0.4260 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.90, 搜索节点数: 592.70, 耗时: 1.8548 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 141, 对应耗时: 0.4260 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [9, 13, 8, 5, 15], 'Stack2': [12, 1, 10, 3, 14], 'Stack3': [11, 7, 6, 4, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 14389, 耗时: 3.4459 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.62, 搜索节点数: 17043.50, 耗时: 54.0681 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 25962, 对应耗时: 82.4101 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.38, 搜索节点数: 17043.50, 耗时: 54.0681 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 25962, 对应耗时: 82.4101 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [8, 1, 11, 3, 13], 'Stack2': [12, 5, 6, 7, 15], 'Stack3': [14, 10, 4, 2, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 5393, 耗时: 1.1810 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.11, 搜索节点数: 8435.00, 耗时: 26.7425 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 88, 对应耗时: 0.2633 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.89, 搜索节点数: 8435.00, 耗时: 26.7425 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 88, 对应耗时: 0.2633 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [13, 12, 10, 4, 9], 'Stack2': [15, 6, 8, 5, 1], 'Stack3': [11, 3, 7, 2, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 1153, 耗时: 0.2462 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.60, 搜索节点数: 1046.00, 耗时: 3.3042 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 172, 对应耗时: 0.5226 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.10, 搜索节点数: 1046.00, 耗时: 3.3042 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 172, 对应耗时: 0.5226 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [13, 12, 7, 9, 5], 'Stack2': [15, 8, 10, 6, 3], 'Stack3': [11, 14, 4, 2, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.3984 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.33, 搜索节点数: 38022.33, 耗时: 119.6513 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 47590, 对应耗时: 150.6840 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.33, 搜索节点数: 38022.33, 耗时: 119.6513 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 47590, 对应耗时: 150.6840 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [11, 9, 6, 7, 4], 'Stack2': [5, 15, 3, 2, 10], 'Stack3': [14, 13, 12, 1, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 46381, 耗时: 11.4297 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.62, 搜索节点数: 14225.88, 耗时: 45.0571 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 43938, 对应耗时: 140.5027 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.00, 搜索节点数: 14225.88, 耗时: 45.0571 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 8764, 对应耗时: 27.6298 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [14, 12, 13, 7, 1], 'Stack2': [10, 6, 3, 11, 4], 'Stack3': [15, 2, 8, 5, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 44345, 耗时: 10.4244 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.25, 搜索节点数: 10558.75, 耗时: 33.2153 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6692, 对应耗时: 20.6580 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.25, 搜索节点数: 10558.75, 耗时: 33.2153 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 6692, 对应耗时: 20.6580 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [8, 7, 11, 1, 10], 'Stack2': [12, 14, 9, 5, 3], 'Stack3': [15, 4, 6, 2, 13], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.2071 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [14, 10, 11, 8, 4], 'Stack2': [15, 12, 3, 2, 5], 'Stack3': [13, 1, 9, 6, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 49694, 耗时: 12.2041 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.80, 搜索节点数: 23479.40, 耗时: 73.9970 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6795, 对应耗时: 21.3362 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.80, 搜索节点数: 23479.40, 耗时: 73.9970 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 6795, 对应耗时: 21.3362 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [9, 15, 8, 3, 7], 'Stack2': [14, 13, 6, 10, 2], 'Stack3': [12, 11, 4, 1, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.9827 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 2627.00, 耗时: 7.9384 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 2627, 对应耗时: 7.9384 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.00, 搜索节点数: 2627.00, 耗时: 7.9384 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 2627, 对应耗时: 7.9384 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [11, 10, 7, 5, 6], 'Stack2': [13, 15, 8, 2, 12], 'Stack3': [9, 4, 1, 3, 14], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.8928 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 31135.67, 耗时: 99.0116 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 23281, 对应耗时: 74.0780 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.33, 搜索节点数: 31135.67, 耗时: 99.0116 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 23281, 对应耗时: 74.0780 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [15, 4, 14, 1, 6], 'Stack2': [12, 9, 2, 3, 7], 'Stack3': [13, 11, 5, 8, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 14592, 耗时: 3.1285 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 13552.90, 耗时: 43.6807 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1091, 对应耗时: 3.6049 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 13552.90, 耗时: 43.6807 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 1593, 对应耗时: 4.9550 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [13, 11, 14, 9, 6], 'Stack2': [7, 5, 4, 3, 12], 'Stack3': [15, 2, 8, 10, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 4812, 耗时: 1.4350 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.22, 搜索节点数: 25952.78, 耗时: 82.9138 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 12425, 对应耗时: 38.9659 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.44, 搜索节点数: 25952.78, 耗时: 82.9138 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 11558, 对应耗时: 37.2332 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [5, 11, 15, 4, 8], 'Stack2': [14, 12, 10, 6, 13], 'Stack3': [9, 7, 2, 3, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 27341, 耗时: 6.9058 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.50, 搜索节点数: 10479.20, 耗时: 33.7561 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2537, 对应耗时: 7.8397 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 10479.20, 耗时: 33.7561 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2537, 对应耗时: 7.8397 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [7, 4, 5, 1, 2], 'Stack2': [14, 13, 12, 15, 10], 'Stack3': [9, 8, 3, 6, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 22619, 耗时: 5.2825 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.78, 搜索节点数: 11974.67, 耗时: 38.2087 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 443, 对应耗时: 1.3413 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.22, 搜索节点数: 11974.67, 耗时: 38.2087 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 443, 对应耗时: 1.3413 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [10, 12, 4, 9, 8], 'Stack2': [15, 6, 3, 1, 2], 'Stack3': [14, 13, 5, 11, 7], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 24224, 耗时: 5.9583 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.86, 搜索节点数: 16485.00, 耗时: 53.4524 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 6501, 对应耗时: 21.1619 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.00, 搜索节点数: 16485.00, 耗时: 53.4524 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 6501, 对应耗时: 21.1619 秒


--------------------------------------------------------------------------------
配置 TotalS4_U80_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 22/30 个实例中找到解决方案。
    平均路径长度: 10.09
    平均搜索节点数: 24389.05
    平均耗时: 5.9701 秒

  LEGEND 算法总结:
    在 14/30 个实例中至少一次运行找到最优解。
    在 19/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 13.96
    所有实例的平均搜索节点数的平均值: 15281.49
    所有实例的平均耗时的平均值: 48.7007 秒

  LEGEND 算法总结（优化后）:
    在 16/30 个实例中至少一次优化找到最优解。
    在 19/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 13.17
    所有实例的平均搜索节点数的平均值: 15281.49
    所有实例的平均耗时的平均值: 48.7007 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U80_D60
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [5, 4, 15, 8, 1], 'Stack2': [12, 7, 11, 3, 10], 'Stack3': [13, 2, 9, 14, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 45248, 耗时: 10.4615 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 10525.40, 耗时: 33.6894 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 2497, 对应耗时: 7.7355 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 10525.40, 耗时: 33.6894 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 3738, 对应耗时: 11.4226 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [12, 1, 9, 3, 10], 'Stack2': [8, 7, 5, 6, 14], 'Stack3': [4, 13, 2, 15, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 93051, 耗时: 21.9682 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.56, 搜索节点数: 10563.44, 耗时: 34.0302 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 14937, 对应耗时: 47.3323 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.22, 搜索节点数: 10563.44, 耗时: 34.0302 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 14937, 对应耗时: 47.3323 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [12, 14, 6, 5, 3], 'Stack2': [13, 11, 4, 7, 1], 'Stack3': [10, 15, 9, 8, 2], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.1023 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [5, 2, 11, 9, 15], 'Stack2': [7, 6, 1, 8, 13], 'Stack3': [10, 14, 4, 12, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 91607, 耗时: 21.0238 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [9, 11, 8, 7, 12], 'Stack2': [10, 15, 3, 13, 14], 'Stack3': [6, 4, 1, 2, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.8223 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [5, 14, 15, 3, 10], 'Stack2': [11, 6, 4, 13, 8], 'Stack3': [7, 2, 9, 1, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 40533, 耗时: 9.4251 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.89, 搜索节点数: 17330.22, 耗时: 55.3517 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1509, 对应耗时: 5.0718 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.22, 搜索节点数: 17330.22, 耗时: 55.3517 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 1509, 对应耗时: 5.0718 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [12, 3, 1, 13, 2], 'Stack2': [8, 15, 4, 7, 10], 'Stack3': [11, 14, 9, 5, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.3471 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [10, 15, 13, 2, 8], 'Stack2': [11, 7, 4, 5, 6], 'Stack3': [9, 1, 14, 3, 12], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.5235 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 25.00, 搜索节点数: 20585.00, 耗时: 64.6884 秒
    10次运行最优值: 最优路径长度: 25, 对应搜索节点数: 20585, 对应耗时: 64.6884 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 22.00, 搜索节点数: 20585.00, 耗时: 64.6884 秒
    10次运行最优值: 最优路径长度: 22, 对应搜索节点数: 20585, 对应耗时: 64.6884 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [5, 12, 1, 13, 8], 'Stack2': [15, 11, 7, 4, 9], 'Stack3': [10, 14, 3, 2, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.5016 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 33827.00, 耗时: 109.1219 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16163, 对应耗时: 51.4763 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 33827.00, 耗时: 109.1219 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 16163, 对应耗时: 51.4763 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [14, 7, 2, 11, 1], 'Stack2': [9, 12, 4, 5, 8], 'Stack3': [13, 3, 15, 10, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 45751, 耗时: 11.5249 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 26252.00, 耗时: 85.2669 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 18516, 对应耗时: 59.9571 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 26252.00, 耗时: 85.2669 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 18516, 对应耗时: 59.9571 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [11, 12, 8, 10, 1], 'Stack2': [13, 9, 3, 6, 15], 'Stack3': [14, 5, 7, 2, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.1584 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.50, 搜索节点数: 19103.00, 耗时: 65.7272 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 3922, 对应耗时: 13.7320 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 19103.00, 耗时: 65.7272 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 3922, 对应耗时: 13.7320 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [6, 2, 1, 5, 8], 'Stack2': [7, 11, 4, 9, 12], 'Stack3': [14, 3, 13, 15, 10], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.7662 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 23.00, 搜索节点数: 33057.50, 耗时: 105.1143 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 16166, 对应耗时: 51.4616 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 22.00, 搜索节点数: 33057.50, 耗时: 105.1143 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 16166, 对应耗时: 51.4616 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [9, 4, 8, 7, 13], 'Stack2': [10, 1, 11, 15, 6], 'Stack3': [14, 3, 5, 2, 12], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.0758 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.25, 搜索节点数: 15183.25, 耗时: 48.0258 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 717, 对应耗时: 2.1569 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.12, 搜索节点数: 15183.25, 耗时: 48.0258 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 717, 对应耗时: 2.1569 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [4, 9, 13, 7, 2], 'Stack2': [11, 5, 3, 10, 15], 'Stack3': [12, 6, 8, 1, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 1984, 耗时: 0.4959 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.30, 搜索节点数: 5053.60, 耗时: 15.8680 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 809, 对应耗时: 2.5316 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.10, 搜索节点数: 5053.60, 耗时: 15.8680 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 444, 对应耗时: 1.3331 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [8, 4, 12, 2, 5], 'Stack2': [15, 9, 1, 7, 6], 'Stack3': [11, 13, 10, 3, 14], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.9414 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 24.00, 搜索节点数: 35327.00, 耗时: 121.8246 秒
    10次运行最优值: 最优路径长度: 24, 对应搜索节点数: 35327, 对应耗时: 121.8246 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 23.00, 搜索节点数: 35327.00, 耗时: 121.8246 秒
    10次运行最优值: 最优路径长度: 23, 对应搜索节点数: 35327, 对应耗时: 121.8246 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [7, 6, 3, 13, 11], 'Stack2': [4, 8, 2, 10, 12], 'Stack3': [1, 9, 5, 14, 15], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 32956, 耗时: 8.0505 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 22.50, 搜索节点数: 19094.75, 耗时: 68.4297 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 33932, 对应耗时: 122.2802 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.50, 搜索节点数: 19094.75, 耗时: 68.4297 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 33932, 对应耗时: 122.2802 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [3, 11, 1, 2, 4], 'Stack2': [15, 10, 6, 5, 7], 'Stack3': [13, 14, 12, 9, 8], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.3682 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [10, 7, 11, 1, 8], 'Stack2': [14, 3, 6, 13, 2], 'Stack3': [15, 4, 5, 12, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 3184, 耗时: 0.3801 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 10091.56, 耗时: 36.2888 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 1041, 对应耗时: 3.6209 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.11, 搜索节点数: 10091.56, 耗时: 36.2888 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 1041, 对应耗时: 3.6209 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [14, 10, 13, 7, 4], 'Stack2': [3, 9, 1, 2, 15], 'Stack3': [11, 8, 5, 12, 6], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 86430, 耗时: 21.5021 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.40, 搜索节点数: 25389.40, 耗时: 91.6277 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 16611, 对应耗时: 60.2901 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.80, 搜索节点数: 25389.40, 耗时: 91.6277 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 16611, 对应耗时: 60.2901 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [3, 1, 6, 2, 7], 'Stack2': [15, 10, 8, 14, 4], 'Stack3': [9, 12, 5, 13, 11], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 84310, 耗时: 19.8183 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 25613.43, 耗时: 91.6244 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 15668, 对应耗时: 56.3269 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.43, 搜索节点数: 25613.43, 耗时: 91.6244 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 15668, 对应耗时: 56.3269 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [14, 6, 11, 12, 9], 'Stack2': [10, 4, 1, 7, 2], 'Stack3': [3, 8, 15, 13, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 16, 搜索节点数: 81526, 耗时: 19.2318 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.40, 搜索节点数: 8578.20, 耗时: 30.1402 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 21724, 对应耗时: 75.9742 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.20, 搜索节点数: 8578.20, 耗时: 30.1402 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 21724, 对应耗时: 75.9742 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [8, 7, 10, 2, 14], 'Stack2': [4, 15, 1, 6, 3], 'Stack3': [11, 13, 9, 5, 12], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.9468 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 22.00, 搜索节点数: 39628.00, 耗时: 141.2520 秒
    10次运行最优值: 最优路径长度: 20, 对应搜索节点数: 32066, 对应耗时: 114.6300 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 39628.00, 耗时: 141.2520 秒
    10次运行最优值: 最优路径长度: 20, 对应搜索节点数: 32066, 对应耗时: 114.6300 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [5, 14, 13, 12, 8], 'Stack2': [10, 7, 6, 9, 4], 'Stack3': [11, 1, 2, 3, 15], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.1648 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 28147.25, 耗时: 101.6326 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 24217, 对应耗时: 87.0910 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.50, 搜索节点数: 28147.25, 耗时: 101.6326 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 24217, 对应耗时: 87.0910 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [9, 13, 1, 2, 14], 'Stack2': [11, 15, 3, 10, 12], 'Stack3': [8, 7, 6, 4, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 25171, 耗时: 5.5050 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.33, 搜索节点数: 19521.83, 耗时: 62.7843 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 12173, 对应耗时: 39.2745 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.83, 搜索节点数: 19521.83, 耗时: 62.7843 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 12173, 对应耗时: 39.2745 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [7, 11, 6, 2, 1], 'Stack2': [10, 8, 5, 12, 15], 'Stack3': [13, 4, 9, 3, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 9258, 耗时: 1.9677 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.40, 搜索节点数: 9334.10, 耗时: 29.2755 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2275, 对应耗时: 6.8674 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.20, 搜索节点数: 9334.10, 耗时: 29.2755 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 2275, 对应耗时: 6.8674 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [10, 2, 9, 13, 15], 'Stack2': [14, 5, 3, 7, 1], 'Stack3': [6, 12, 8, 11, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 9883, 耗时: 2.4594 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.22, 搜索节点数: 14518.11, 耗时: 45.2336 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2215, 对应耗时: 6.6176 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.22, 搜索节点数: 14518.11, 耗时: 45.2336 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 2215, 对应耗时: 6.6176 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [15, 8, 1, 11, 9], 'Stack2': [13, 4, 7, 3, 2], 'Stack3': [10, 14, 5, 12, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.0994 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.25, 搜索节点数: 18601.75, 耗时: 58.6361 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 4357, 对应耗时: 13.1470 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.00, 搜索节点数: 18601.75, 耗时: 58.6361 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 4357, 对应耗时: 13.1470 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [15, 5, 9, 4, 1], 'Stack2': [13, 3, 7, 2, 11], 'Stack3': [10, 6, 14, 8, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 24557, 耗时: 5.9636 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.40, 搜索节点数: 14648.40, 耗时: 46.2795 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 2818, 对应耗时: 8.8324 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.90, 搜索节点数: 14648.40, 耗时: 46.2795 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 2490, 对应耗时: 7.9914 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [9, 5, 4, 2, 10], 'Stack2': [12, 14, 8, 11, 7], 'Stack3': [13, 15, 6, 3, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.6615 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [9, 2, 6, 8, 4], 'Stack2': [12, 11, 1, 15, 7], 'Stack3': [10, 14, 5, 3, 13], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.6551 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 28354.67, 耗时: 88.9313 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 26475, 对应耗时: 84.0353 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.67, 搜索节点数: 28354.67, 耗时: 88.9313 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 26475, 对应耗时: 84.0353 秒


--------------------------------------------------------------------------------
配置 TotalS4_U80_D60 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 15/30 个实例中找到解决方案。
    平均路径长度: 13.33
    平均搜索节点数: 45029.93
    平均耗时: 10.6519 秒

  LEGEND 算法总结:
    在 4/30 个实例中至少一次运行找到最优解。
    在 21/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 17.94
    所有实例的平均搜索节点数的平均值: 20347.04
    所有实例的平均耗时的平均值: 67.9518 秒

  LEGEND 算法总结（优化后）:
    在 7/30 个实例中至少一次优化找到最优解。
    在 21/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 16.85
    所有实例的平均搜索节点数的平均值: 20347.04
    所有实例的平均耗时的平均值: 67.9518 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS4_U80_D70
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [14, 5, 12, 2, 4], 'Stack2': [13, 15, 8, 9, 7], 'Stack3': [3, 6, 10, 1, 11], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.7722 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.50, 搜索节点数: 27082.00, 耗时: 84.9394 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 25424, 对应耗时: 79.5339 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 27082.00, 耗时: 84.9394 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 25424, 对应耗时: 79.5339 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [12, 10, 15, 5, 6], 'Stack2': [11, 13, 7, 8, 4], 'Stack3': [3, 9, 1, 2, 14], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.0603 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [13, 5, 6, 8, 10], 'Stack2': [9, 1, 2, 14, 15], 'Stack3': [3, 11, 7, 12, 4], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 1574, 耗时: 0.1842 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.38, 搜索节点数: 12424.12, 耗时: 39.1298 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 3414, 对应耗时: 10.3168 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.50, 搜索节点数: 12424.12, 耗时: 39.1298 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 3414, 对应耗时: 10.3168 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [9, 5, 13, 2, 12], 'Stack2': [3, 1, 10, 8, 14], 'Stack3': [11, 15, 7, 4, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.4096 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.50, 搜索节点数: 46453.50, 耗时: 146.7375 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 46073, 对应耗时: 146.8032 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.50, 搜索节点数: 46453.50, 耗时: 146.7375 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 46834, 对应耗时: 146.6718 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [4, 7, 10, 14, 2], 'Stack2': [5, 12, 8, 15, 11], 'Stack3': [13, 6, 1, 3, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 91103, 耗时: 20.6021 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 24.75, 搜索节点数: 22940.50, 耗时: 82.2033 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 3803, 对应耗时: 11.4587 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 21.50, 搜索节点数: 22940.50, 耗时: 82.2033 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 4127, 对应耗时: 14.7833 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [3, 9, 1, 2, 6], 'Stack2': [12, 7, 10, 4, 11], 'Stack3': [14, 8, 13, 5, 15], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 15993, 耗时: 3.0532 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.67, 搜索节点数: 27155.17, 耗时: 93.2345 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 37514, 对应耗时: 119.1918 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.17, 搜索节点数: 27155.17, 耗时: 93.2345 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 37514, 对应耗时: 119.1918 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [14, 11, 12, 2, 1], 'Stack2': [9, 15, 4, 7, 10], 'Stack3': [8, 6, 13, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.7611 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [9, 2, 3, 12, 14], 'Stack2': [11, 13, 5, 4, 8], 'Stack3': [7, 10, 6, 1, 15], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.9654 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 10151.00, 耗时: 32.0314 秒
    10次运行最优值: 最优路径长度: 19, 对应搜索节点数: 10151, 对应耗时: 32.0314 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 10151.00, 耗时: 32.0314 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 10151, 对应耗时: 32.0314 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [8, 9, 4, 14, 11], 'Stack2': [12, 10, 1, 2, 5], 'Stack3': [13, 15, 7, 6, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.5907 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [12, 2, 5, 8, 9], 'Stack2': [7, 10, 3, 1, 4], 'Stack3': [14, 15, 13, 11, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.5631 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [12, 11, 4, 9, 1], 'Stack2': [3, 6, 5, 10, 2], 'Stack3': [8, 14, 7, 13, 15], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 84619, 耗时: 18.7049 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.75, 搜索节点数: 25585.75, 耗时: 81.0778 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 10470, 对应耗时: 32.7484 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.50, 搜索节点数: 25585.75, 耗时: 81.0778 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 31829, 对应耗时: 99.1972 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [10, 15, 7, 9, 4], 'Stack2': [1, 8, 5, 13, 3], 'Stack3': [12, 6, 2, 14, 11], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.5047 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [8, 14, 7, 10, 2], 'Stack2': [12, 4, 3, 6, 5], 'Stack3': [9, 15, 13, 11, 1], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.9525 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [11, 10, 6, 15, 3], 'Stack2': [2, 4, 1, 5, 7], 'Stack3': [8, 12, 14, 9, 13], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 3638, 耗时: 0.6247 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 20.25, 搜索节点数: 20924.25, 耗时: 65.9521 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 10452, 对应耗时: 32.2402 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 20924.25, 耗时: 65.9521 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 10452, 对应耗时: 32.2402 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [8, 13, 5, 6, 7], 'Stack2': [14, 2, 9, 3, 11], 'Stack3': [15, 4, 12, 1, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 12, 搜索节点数: 8857, 耗时: 1.4016 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.20, 搜索节点数: 11259.10, 耗时: 35.6793 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 1488, 对应耗时: 4.6746 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.60, 搜索节点数: 11259.10, 耗时: 35.6793 秒
    10次运行最优值: 最优路径长度: 12, 对应搜索节点数: 1488, 对应耗时: 4.6746 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [11, 2, 15, 7, 1], 'Stack2': [4, 8, 3, 10, 12], 'Stack3': [13, 14, 5, 6, 9], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 57164, 耗时: 14.1132 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 17944.29, 耗时: 58.1931 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 39454, 对应耗时: 124.8933 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.00, 搜索节点数: 17944.29, 耗时: 58.1931 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 8013, 对应耗时: 24.4220 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [11, 8, 9, 5, 4], 'Stack2': [12, 1, 14, 15, 3], 'Stack3': [7, 10, 6, 13, 2], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.6375 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.33, 搜索节点数: 20283.17, 耗时: 63.6841 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 19937, 对应耗时: 63.4435 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.00, 搜索节点数: 20283.17, 耗时: 63.6841 秒
    10次运行最优值: 最优路径长度: 17, 对应搜索节点数: 8118, 对应耗时: 24.6521 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [9, 3, 8, 14, 4], 'Stack2': [6, 13, 10, 15, 5], 'Stack3': [11, 1, 2, 7, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 15, 搜索节点数: 42339, 耗时: 10.5993 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [10, 3, 5, 1, 4], 'Stack2': [2, 12, 15, 9, 14], 'Stack3': [13, 7, 11, 8, 6], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.3171 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 26213.00, 耗时: 84.2611 秒
    10次运行最优值: 最优路径长度: 20, 对应搜索节点数: 28625, 对应耗时: 91.3389 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.67, 搜索节点数: 26213.00, 耗时: 84.2611 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 28625, 对应耗时: 91.3389 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [8, 14, 5, 4, 1], 'Stack2': [12, 10, 6, 9, 7], 'Stack3': [11, 15, 3, 2, 13], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.3865 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [6, 5, 3, 11, 15], 'Stack2': [2, 9, 14, 1, 12], 'Stack3': [10, 13, 8, 7, 4], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 22.1967 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [6, 12, 5, 11, 4], 'Stack2': [1, 2, 7, 13, 15], 'Stack3': [8, 3, 9, 10, 14], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 14, 搜索节点数: 7626, 耗时: 1.4388 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.67, 搜索节点数: 14149.33, 耗时: 44.9485 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2974, 对应耗时: 9.3101 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 17.89, 搜索节点数: 14149.33, 耗时: 44.9485 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 2974, 对应耗时: 9.3101 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [1, 4, 12, 8, 10], 'Stack2': [13, 6, 2, 7, 14], 'Stack3': [11, 15, 9, 5, 3], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.4352 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 26377.50, 耗时: 82.1723 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 2947, 对应耗时: 9.1684 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 19.50, 搜索节点数: 26377.50, 耗时: 82.1723 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 2947, 对应耗时: 9.1684 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [11, 2, 6, 3, 1], 'Stack2': [7, 13, 8, 4, 9], 'Stack3': [12, 10, 15, 14, 5], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 23.8837 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 21.00, 搜索节点数: 35600.00, 耗时: 112.3498 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 21931, 对应耗时: 67.9839 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 20.00, 搜索节点数: 35600.00, 耗时: 112.3498 秒
    10次运行最优值: 最优路径长度: 15, 对应搜索节点数: 21931, 对应耗时: 67.9839 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [4, 8, 12, 2, 13], 'Stack2': [10, 14, 15, 5, 11], 'Stack3': [9, 6, 7, 1, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 2475, 耗时: 0.2972 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 16.62, 搜索节点数: 13434.62, 耗时: 42.0716 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 4483, 对应耗时: 13.2935 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 15.25, 搜索节点数: 13434.62, 耗时: 42.0716 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 4483, 对应耗时: 13.2935 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [15, 6, 3, 12, 9], 'Stack2': [2, 5, 7, 8, 14], 'Stack3': [1, 13, 11, 4, 10], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 13, 搜索节点数: 1776, 耗时: 0.3300 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 17.86, 搜索节点数: 12293.43, 耗时: 38.9952 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 6318, 对应耗时: 19.8857 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 16.43, 搜索节点数: 12293.43, 耗时: 38.9952 秒
    10次运行最优值: 最优路径长度: 13, 对应搜索节点数: 6318, 对应耗时: 19.8857 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [11, 12, 2, 6, 10], 'Stack2': [15, 5, 4, 9, 1], 'Stack3': [13, 14, 7, 3, 8], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.4957 秒

  [LEGEND 算法 (运行 10 次)]
    所有 10 次运行均未找到解决方案。
  [LEGEND 算法 (运行 10 次)]（优化后）
    所有 10 次运行（优化后）均未找到解决方案。

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [11, 15, 13, 2, 1], 'Stack2': [10, 5, 14, 3, 4], 'Stack3': [12, 8, 9, 6, 7], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.4816 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 47270.00, 耗时: 148.8544 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 47270, 对应耗时: 148.8544 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.00, 搜索节点数: 47270.00, 耗时: 148.8544 秒
    10次运行最优值: 最优路径长度: 18, 对应搜索节点数: 47270, 对应耗时: 148.8544 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [13, 5, 4, 9, 12], 'Stack2': [7, 15, 11, 10, 6], 'Stack3': [8, 14, 1, 3, 2], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 25.1511 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 25.00, 搜索节点数: 15409.00, 耗时: 48.3761 秒
    10次运行最优值: 最优路径长度: 25, 对应搜索节点数: 15409, 对应耗时: 48.3761 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 22.00, 搜索节点数: 15409.00, 耗时: 48.3761 秒
    10次运行最优值: 最优路径长度: 22, 对应搜索节点数: 15409, 对应耗时: 48.3761 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [14, 11, 1, 9, 12], 'Stack2': [3, 13, 10, 6, 2], 'Stack3': [7, 15, 5, 4, 8], 'Stack4': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 24.3783 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 18.80, 搜索节点数: 37462.00, 耗时: 118.0831 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 34882, 对应耗时: 109.4830 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 18.20, 搜索节点数: 37462.00, 耗时: 118.0831 秒
    10次运行最优值: 最优路径长度: 16, 对应搜索节点数: 34882, 对应耗时: 109.4830 秒


--------------------------------------------------------------------------------
配置 TotalS4_U80_D70 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 11/30 个实例中找到解决方案。
    平均路径长度: 13.36
    平均搜索节点数: 28833.09
    平均耗时: 6.4863 秒

  LEGEND 算法总结:
    在 6/30 个实例中至少一次运行找到最优解。
    在 19/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 18.74
    所有实例的平均搜索节点数的平均值: 23520.59
    所有实例的平均耗时的平均值: 75.1487 秒

  LEGEND 算法总结（优化后）:
    在 8/30 个实例中至少一次优化找到最优解。
    在 19/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 17.59
    所有实例的平均搜索节点数的平均值: 23520.59
    所有实例的平均耗时的平均值: 75.1487 秒
================================================================================


--------------------------------------------------------------------------------
测试配置: TotalS5_U60_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [14, 15, 9, 5, 12], 'Stack2': [8, 6, 4, 10], 'Stack3': [7, 3, 1, 2], 'Stack4': [11, 13], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 37530, 耗时: 14.4935 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.67, 搜索节点数: 28709.17, 耗时: 112.4314 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 8848, 对应耗时: 35.5051 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.33, 搜索节点数: 28709.17, 耗时: 112.4314 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 8848, 对应耗时: 35.5051 秒

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [15, 2, 6, 4], 'Stack2': [3], 'Stack3': [12, 11, 10, 8, 9], 'Stack4': [7, 14, 5, 1, 13], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 10072, 耗时: 3.3844 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 5003.70, 耗时: 19.1480 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 12685, 对应耗时: 48.9361 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 5003.70, 耗时: 19.1480 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 1797, 对应耗时: 6.6439 秒

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [6, 15, 7], 'Stack2': [12, 14, 11, 9, 4], 'Stack3': [10, 3, 13], 'Stack4': [8, 5, 2, 1], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 39.2952 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.50, 搜索节点数: 30976.00, 耗时: 120.5109 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 37380, 对应耗时: 145.2726 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 30976.00, 耗时: 120.5109 秒
    10次运行最优值: 最优路径长度: 14, 对应搜索节点数: 24572, 对应耗时: 95.7491 秒

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [15, 14, 12, 1, 13], 'Stack2': [9, 2, 4, 7, 6], 'Stack3': [], 'Stack4': [8, 3, 5, 11, 10], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 11153, 耗时: 3.1088 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.10, 搜索节点数: 2316.90, 耗时: 9.1630 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 35, 对应耗时: 0.1268 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.30, 搜索节点数: 2316.90, 耗时: 9.1630 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 35, 对应耗时: 0.1268 秒

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [11, 3, 12, 7], 'Stack2': [5, 4], 'Stack3': [10, 8, 6, 15, 2], 'Stack4': [13, 14, 1, 9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 8991, 耗时: 3.4778 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.90, 搜索节点数: 3014.80, 耗时: 12.0206 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1003, 对应耗时: 3.9092 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.80, 搜索节点数: 3014.80, 耗时: 12.0206 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 1003, 对应耗时: 3.9092 秒

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [11, 2, 14, 3, 15], 'Stack2': [], 'Stack3': [12, 6, 7, 8, 13], 'Stack4': [9, 5, 4, 1, 10], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 9, 耗时: 0.0014 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.40, 搜索节点数: 2246.50, 耗时: 9.0218 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 18, 对应耗时: 0.0612 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 2246.50, 耗时: 9.0218 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 18, 对应耗时: 0.0612 秒

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [7, 14, 4, 2, 1], 'Stack2': [9, 13, 8, 6, 3], 'Stack3': [12, 11], 'Stack4': [15, 10, 5], 'Stack5': []}

  [纯 A* 算法]
    未找到解决方案。搜索节点数: 100000, 耗时: 39.8122 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.14, 搜索节点数: 22704.71, 耗时: 90.0748 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 40156, 对应耗时: 164.5676 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.43, 搜索节点数: 22704.71, 耗时: 90.0748 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 11260, 对应耗时: 44.0131 秒

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [12, 11, 8, 13, 3], 'Stack2': [15, 1, 9, 2, 4], 'Stack3': [14, 7], 'Stack4': [5, 10, 6], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 179, 耗时: 0.0319 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 397.90, 耗时: 1.4710 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 301, 对应耗时: 1.0804 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.20, 搜索节点数: 397.90, 耗时: 1.4710 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 301, 对应耗时: 1.0804 秒

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [6, 3, 4, 12, 1], 'Stack2': [8, 13, 9, 7], 'Stack3': [11, 10, 14], 'Stack4': [15, 5, 2], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 14057, 耗时: 4.9407 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.40, 搜索节点数: 2734.90, 耗时: 10.7731 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 551, 对应耗时: 2.0549 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.30, 搜索节点数: 2734.90, 耗时: 10.7731 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 551, 对应耗时: 2.0549 秒

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [14, 4, 8, 13, 10], 'Stack2': [9, 12, 1], 'Stack3': [15, 7, 3, 2, 5], 'Stack4': [6, 11], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 1382, 耗时: 0.2572 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.10, 搜索节点数: 2671.00, 耗时: 10.5217 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 272, 对应耗时: 1.0091 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 2671.00, 耗时: 10.5217 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 272, 对应耗时: 1.0091 秒

  --- 实例: instance_11.txt ---
  初始状态: {'Stack1': [14, 11, 5], 'Stack2': [13, 6, 8, 1], 'Stack3': [12, 4, 9, 10, 3], 'Stack4': [2, 7, 15], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 85, 耗时: 0.0139 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.30, 搜索节点数: 207.10, 耗时: 0.7985 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 37, 对应耗时: 0.1334 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.10, 搜索节点数: 207.10, 耗时: 0.7985 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 37, 对应耗时: 0.1334 秒

  --- 实例: instance_12.txt ---
  初始状态: {'Stack1': [2, 12], 'Stack2': [3, 6, 8, 1], 'Stack3': [15, 11, 10, 4, 5], 'Stack4': [14, 9, 13, 7], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 5065, 耗时: 2.0184 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 6549.00, 耗时: 25.2100 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 7142, 对应耗时: 27.2712 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 6549.00, 耗时: 25.2100 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 7142, 对应耗时: 27.2712 秒

  --- 实例: instance_13.txt ---
  初始状态: {'Stack1': [13, 7, 5, 15, 6], 'Stack2': [11, 10, 9, 2, 12], 'Stack3': [], 'Stack4': [4, 8, 3, 1, 14], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 414, 耗时: 0.0762 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.70, 搜索节点数: 844.30, 耗时: 3.2577 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 210, 对应耗时: 0.7853 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.60, 搜索节点数: 844.30, 耗时: 3.2577 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 210, 对应耗时: 0.7853 秒

  --- 实例: instance_14.txt ---
  初始状态: {'Stack1': [10, 4, 11, 8, 12], 'Stack2': [6, 3, 13], 'Stack3': [14, 2, 1, 7, 15], 'Stack4': [5, 9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 9772, 耗时: 4.1127 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 15.11, 搜索节点数: 1148.67, 耗时: 4.3533 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 445, 对应耗时: 1.6492 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 14.33, 搜索节点数: 1148.67, 耗时: 4.3533 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 445, 对应耗时: 1.6492 秒

  --- 实例: instance_15.txt ---
  初始状态: {'Stack1': [11, 1, 14, 9], 'Stack2': [8, 12, 6], 'Stack3': [15, 7, 4, 13], 'Stack4': [5, 2, 3, 10], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 2444, 耗时: 0.4702 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 10.60, 搜索节点数: 5374.00, 耗时: 20.9973 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 5779, 对应耗时: 22.8075 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.50, 搜索节点数: 5374.00, 耗时: 20.9973 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 2302, 对应耗时: 8.5849 秒

  --- 实例: instance_16.txt ---
  初始状态: {'Stack1': [4], 'Stack2': [14, 7, 11, 9, 3], 'Stack3': [13, 6, 2, 12, 8], 'Stack4': [15, 5, 10, 1], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 5955, 耗时: 1.9321 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.30, 搜索节点数: 1866.80, 耗时: 7.2440 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 470, 对应耗时: 1.8440 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.60, 搜索节点数: 1866.80, 耗时: 7.2440 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 470, 对应耗时: 1.8440 秒

  --- 实例: instance_17.txt ---
  初始状态: {'Stack1': [8, 7, 9, 14], 'Stack2': [15, 6, 5, 4, 11], 'Stack3': [1, 2, 13, 12], 'Stack4': [3, 10], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 10, 搜索节点数: 247, 耗时: 0.0415 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 191.40, 耗时: 0.7295 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 32, 对应耗时: 0.1134 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.70, 搜索节点数: 191.40, 耗时: 0.7295 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 32, 对应耗时: 0.1134 秒

  --- 实例: instance_18.txt ---
  初始状态: {'Stack1': [8, 6, 5, 10], 'Stack2': [1, 14], 'Stack3': [11, 3, 7, 13], 'Stack4': [15, 2, 4, 9, 12], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 3862, 耗时: 1.4987 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.60, 搜索节点数: 1598.50, 耗时: 6.3250 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 98, 对应耗时: 0.3625 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.00, 搜索节点数: 1598.50, 耗时: 6.3250 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 98, 对应耗时: 0.3625 秒

  --- 实例: instance_19.txt ---
  初始状态: {'Stack1': [15, 13, 11, 12], 'Stack2': [5, 10, 2], 'Stack3': [8, 9, 3], 'Stack4': [7, 6, 1, 4, 14], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 1004, 耗时: 0.3204 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 8.40, 搜索节点数: 1226.00, 耗时: 4.7764 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 163, 对应耗时: 0.6070 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.20, 搜索节点数: 1226.00, 耗时: 4.7764 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 163, 对应耗时: 0.6070 秒

  --- 实例: instance_20.txt ---
  初始状态: {'Stack1': [7, 5, 2], 'Stack2': [6, 13, 3, 15], 'Stack3': [10, 14, 11, 9, 1], 'Stack4': [12, 8, 4], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 10582, 耗时: 4.3020 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.43, 搜索节点数: 7462.43, 耗时: 28.8162 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 5733, 对应耗时: 22.1965 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.86, 搜索节点数: 7462.43, 耗时: 28.8162 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 5733, 对应耗时: 22.1965 秒

  --- 实例: instance_21.txt ---
  初始状态: {'Stack1': [12, 10, 3, 1, 8], 'Stack2': [15, 2, 13, 7], 'Stack3': [11, 14, 4, 6, 5], 'Stack4': [9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 3702, 耗时: 1.2434 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.00, 搜索节点数: 4771.40, 耗时: 18.8443 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 460, 对应耗时: 1.8300 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 8.90, 搜索节点数: 4771.40, 耗时: 18.8443 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 460, 对应耗时: 1.8300 秒

  --- 实例: instance_22.txt ---
  初始状态: {'Stack1': [7, 10], 'Stack2': [2, 14, 3, 9], 'Stack3': [15, 4, 1, 12, 6], 'Stack4': [13, 8, 5, 11], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 3689, 耗时: 1.2300 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.00, 搜索节点数: 2185.20, 耗时: 8.6224 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 529, 对应耗时: 1.9774 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.00, 搜索节点数: 2185.20, 耗时: 8.6224 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 529, 对应耗时: 1.9774 秒

  --- 实例: instance_23.txt ---
  初始状态: {'Stack1': [13], 'Stack2': [8, 15, 3, 2, 9], 'Stack3': [6, 4, 1, 10, 14], 'Stack4': [7, 12, 5, 11], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 11, 搜索节点数: 41277, 耗时: 15.9809 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 14.78, 搜索节点数: 17998.00, 耗时: 71.7835 秒
    10次运行最优值: 最优路径长度: 11, 对应搜索节点数: 9256, 对应耗时: 36.2071 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 13.56, 搜索节点数: 17998.00, 耗时: 71.7835 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 26164, 对应耗时: 103.4761 秒

  --- 实例: instance_24.txt ---
  初始状态: {'Stack1': [15, 14, 10, 12, 7], 'Stack2': [8, 11, 5, 2, 1], 'Stack3': [13, 9, 6, 3, 4], 'Stack4': [], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 7078, 耗时: 2.6218 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.40, 搜索节点数: 4529.10, 耗时: 17.2847 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 1167, 对应耗时: 4.4514 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.20, 搜索节点数: 4529.10, 耗时: 17.2847 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 1167, 对应耗时: 4.4514 秒

  --- 实例: instance_25.txt ---
  初始状态: {'Stack1': [15, 5, 4, 3, 7], 'Stack2': [12, 13, 8, 1], 'Stack3': [14, 9, 11, 2, 10], 'Stack4': [6], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 24540, 耗时: 9.3646 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 13.44, 搜索节点数: 14786.11, 耗时: 57.9368 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 5957, 对应耗时: 22.8938 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.11, 搜索节点数: 14786.11, 耗时: 57.9368 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 24131, 对应耗时: 95.1412 秒

  --- 实例: instance_26.txt ---
  初始状态: {'Stack1': [4, 7], 'Stack2': [13, 8, 9, 3, 1], 'Stack3': [15, 12, 6, 10, 2], 'Stack4': [11, 5, 14], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 1896, 耗时: 0.7791 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 9.70, 搜索节点数: 1867.00, 耗时: 7.2546 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 4289, 对应耗时: 16.7614 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 9.10, 搜索节点数: 1867.00, 耗时: 7.2546 秒
    10次运行最优值: 最优路径长度: 7, 对应搜索节点数: 4289, 对应耗时: 16.7614 秒

  --- 实例: instance_27.txt ---
  初始状态: {'Stack1': [7, 1, 6, 9], 'Stack2': [15, 14, 4, 10], 'Stack3': [13, 2, 12], 'Stack4': [8, 11, 5, 3], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 18956, 耗时: 7.2737 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.50, 搜索节点数: 13412.10, 耗时: 52.9832 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 34510, 对应耗时: 136.5153 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 12.00, 搜索节点数: 13412.10, 耗时: 52.9832 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 34510, 对应耗时: 136.5153 秒

  --- 实例: instance_28.txt ---
  初始状态: {'Stack1': [7, 4, 1, 8, 2], 'Stack2': [14, 10, 11, 15], 'Stack3': [13], 'Stack4': [9, 3, 12, 5, 6], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 988, 耗时: 0.4549 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.67, 搜索节点数: 3269.00, 耗时: 12.7244 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 1150, 对应耗时: 4.2836 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.11, 搜索节点数: 3269.00, 耗时: 12.7244 秒
    10次运行最优值: 最优路径长度: 8, 对应搜索节点数: 1150, 对应耗时: 4.2836 秒

  --- 实例: instance_29.txt ---
  初始状态: {'Stack1': [3, 15, 2], 'Stack2': [12, 14, 10], 'Stack3': [13, 11, 9, 4, 8], 'Stack4': [5, 1, 7, 6], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 34402, 耗时: 11.9701 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 12.17, 搜索节点数: 32214.00, 耗时: 124.6170 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 19658, 对应耗时: 75.5397 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 11.83, 搜索节点数: 32214.00, 耗时: 124.6170 秒
    10次运行最优值: 最优路径长度: 10, 对应搜索节点数: 19658, 对应耗时: 75.5397 秒

  --- 实例: instance_30.txt ---
  初始状态: {'Stack1': [13, 6, 8, 3, 2], 'Stack2': [5], 'Stack3': [14, 10, 12, 7, 15], 'Stack4': [11, 1, 4, 9], 'Stack5': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 21558, 耗时: 7.1017 秒

  [LEGEND 算法 (运行 10 次)]
    10次运行平均值: 路径长度: 11.50, 搜索节点数: 5190.00, 耗时: 21.5104 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 335, 对应耗时: 1.2360 秒
  [LEGEND 算法 (运行 10 次)]（优化后）
    10次运行平均值: 路径长度: 10.60, 搜索节点数: 5190.00, 耗时: 21.5104 秒
    10次运行最优值: 最优路径长度: 9, 对应搜索节点数: 335, 对应耗时: 1.2360 秒


--------------------------------------------------------------------------------
配置 TotalS5_U60_D50 总结:
--------------------------------------------------------------------------------
  纯 A* 算法总结:
    在 28/30 个实例中找到解决方案。
    平均路径长度: 8.29
    平均搜索节点数: 10031.75
    平均耗时: 3.6608 秒

  LEGEND 算法总结:
    在 20/30 个实例中至少一次运行找到最优解。
    在 9/30 个实例中存在至少一次运行无解的情况。
    所有实例的平均路径长度的平均值: 11.58
    所有实例的平均搜索节点数的平均值: 7582.19
    所有实例的平均耗时的平均值: 29.7069 秒

  LEGEND 算法总结（优化后）:
    在 19/30 个实例中至少一次优化找到最优解。
    在 9/30 个实例中存在至少一次运行（优化后）无解的情况。
    所有实例的平均路径长度的平均值: 10.90
    所有实例的平均搜索节点数的平均值: 7582.19
    所有实例的平均耗时的平均值: 29.7069 秒
================================================================================