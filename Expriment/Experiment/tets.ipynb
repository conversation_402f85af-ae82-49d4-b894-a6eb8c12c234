{"cells": [{"cell_type": "code", "execution_count": 1, "id": "88d2638f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录已修改为: /home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out\n"]}], "source": ["import os\n", "\n", "# 指定目标路径（替换为你的实际路径）\n", "target_dir = os.path.expanduser(r\"/home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out\")\n", "\n", "# 检查路径是否存在\n", "if os.path.exists(target_dir):\n", "    os.chdir(target_dir)  # 修改工作目录\n", "    print(f\"当前工作目录已修改为: {os.getcwd()}\")\n", "else:\n", "    print(f\"错误：路径不存在 - {target_dir}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "1a895ec3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/envs/pre_marshalling/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pre_marshalling_astar_neural_dynamic_weight_no_goal import GraphPlanningBlocksWorld\n", "from optimize_no_goal import optimize_path,apply_move,is_valid_cpmp_solution\n", "from pre_marshalling_llm import LLMGuidance\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 3, "id": "dc9cb36c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试一：A*算法 ---\n", "自动生成的 G_canonical 目标:\n", "{'Stack1': [6, 5, 4, 3, 2, 1], 'Stack2': [], 'Stack3': []}\n", "自动生成的修复顺序 (fix_order):\n", "['Stack1', 'Stack2', 'Stack3']\n", "Running A* without guidance:\n", "Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "Nodes searched: 7\n", "Path length: 4\n", "\n", "耗时: 0.0592 秒\n", "找到解决方案，共 4 步: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "搜索的节点数: 7\n"]}], "source": ["print(\"\\n--- 测试一：A*算法 ---\")\n", "letter_map = {chr(ord('A') + i): i + 1 for i in range(26)}\n", "\n", "\n", "start_state_3 = {\n", "    'Stack1': [1, 2, 6],\n", "    'Stack2': [3, 4, 5],\n", "    'Stack3': []\n", "}\n", "\n", "start_state_numeric = {stack: [c for c in blocks] for stack, blocks in start_state_3.items()}\n", "\n", "# 2. 自动生成 G_canonical (人工引力场)\n", "all_blocks = sorted([item for sublist in start_state_numeric.values() for item in sublist], reverse=True)\n", "\n", "# 假设我们总是选择 'Stack1' 作为目标栈\n", "target_stack_name = 'Stack1'\n", "g_canonical = {stack_name: [] for stack_name in start_state_numeric.keys()}\n", "g_canonical[target_stack_name] = all_blocks\n", "\n", "print(\"自动生成的 G_canonical 目标:\")\n", "print(g_canonical)\n", "\n", "# 3. 自动生成 fix_order\n", "# 把 G_canonical 的目标栈放在第一位\n", "stack_names = list(start_state_numeric.keys())\n", "fix_order = sorted(stack_names, key=lambda x: x != target_stack_name)\n", "\n", "print(\"自动生成的修复顺序 (fix_order):\")\n", "print(fix_order)\n", "\n", "log_file = 'a*_test.txt'\n", "\n", "# 5. 运行规划器\n", "start = time.time()\n", "\n", "\n", "planner = GraphPlanningBlocksWorld(\n", "    start_state=start_state_numeric, \n", "    goal_state=g_canonical, \n", "    fix_order=fix_order, \n", "    log_file=log_file\n", ")\n", "    \n", "# 运行A*搜索，此时 llm=None, model_path不为None, 将会使用模型指导\n", "solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000)\n", "\n", "print(f\"耗时: {time.time() - start:.4f} 秒\")\n", "if solution:\n", "    print(f\"找到解决方案，共 {len(solution)} 步: {solution}\")\n", "else:\n", "    print(\"未找到解决方案。\")\n", "print(f\"搜索的节点数: {nodes_count}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "dc0ebd60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试二：G_canonical + 模型引导的A*算法 ---\n", "自动生成的 G_canonical 目标:\n", "{'Stack1': [6, 5, 4, 3, 2, 1], 'Stack2': [], 'Stack3': []}\n", "自动生成的修复顺序 (fix_order):\n", "['Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 7, 9, 6)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out/pre_marshalling_astar_neural_dynamic_weight_no_goal.py:150: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  checkpoint = torch.load(model_path, map_location=self.device)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 12, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 3x4\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 6 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 3), (1, 3), (1, 3), (2, 1), (2, 1)]\n", "Nodes searched: 9\n", "Path length: 5\n", "\n", "耗时: 0.7103 秒\n", "找到解决方案，共 5 步: [(1, 3), (1, 3), (1, 3), (2, 1), (2, 1)]\n", "搜索的节点数: 9\n", "\n", "========= 优化流程结束 =========\n", "最终路径长度从 5 减少到 5，缩短了 0.00%。\n", "\n", "--- 验证结果 ---\n", "优化路径达到的最终状态: {'Stack1': [5, 4], 'Stack2': [3], 'Stack3': [6, 2, 1]}\n", "✅ 验证成功：优化后的路径能够正确达到一个合法的有序状态！\n"]}], "source": ["print(\"\\n--- 测试二：G_canonical + 模型引导的A*算法 ---\")\n", "letter_map = {chr(ord('A') + i): i + 1 for i in range(26)}\n", "\n", "\n", "start_state = {\n", "    'Stack1': [1, 2, 6],\n", "    'Stack2': [3, 4, 5],\n", "    'Stack3': []\n", "}\n", "\n", "start_state_numeric = {stack: [c for c in blocks] for stack, blocks in start_state.items()}\n", "\n", "# 2. 自动生成 G_canonical (人工引力场)\n", "all_blocks = sorted([item for sublist in start_state_numeric.values() for item in sublist], reverse=True)\n", "\n", "# 假设我们总是选择 'Stack1' 作为目标栈\n", "target_stack_name = 'Stack1'\n", "g_canonical = {stack_name: [] for stack_name in start_state_numeric.keys()}\n", "g_canonical[target_stack_name] = all_blocks\n", "\n", "print(\"自动生成的 G_canonical 目标:\")\n", "print(g_canonical)\n", "\n", "# 3. 自动生成 fix_order\n", "# 把 G_canonical 的目标栈放在第一位\n", "stack_names = list(start_state_numeric.keys())\n", "fix_order = sorted(stack_names, key=lambda x: x != target_stack_name)\n", "\n", "print(\"自动生成的修复顺序 (fix_order):\")\n", "print(fix_order)\n", "\n", "# 4. 定义模型和日志路径\n", "# 请确保这里的路径是正确的，并且相关的依赖文件存在\n", "model_path = \"/home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'g_canonical_model_test.txt'\n", "\n", "# 5. 运行规划器\n", "start = time.time()\n", "\n", "\n", "planner = GraphPlanningBlocksWorld(\n", "    start_state=start_state_numeric, \n", "    goal_state=g_canonical, \n", "    fix_order=fix_order, \n", "    model_path=model_path, \n", "    log_file=log_file\n", ")\n", "    \n", "# 运行A*搜索，此时 llm=None, model_path不为None, 将会使用模型指导\n", "solution, nodes_count = planner.a_star_search(llm=None, max_iterations=50000, is_consistency=False)\n", "\n", "print(f\"耗时: {time.time() - start:.4f} 秒\")\n", "if solution:\n", "    print(f\"找到解决方案，共 {len(solution)} 步: {solution}\")\n", "else:\n", "    print(\"未找到解决方案。\")\n", "print(f\"搜索的节点数: {nodes_count}\")\n", "\n", "\n", "# 执行优化\n", "optimized_path = optimize_path(solution, start_state)\n", "\n", "# 验证优化后的路径是否依然能达到一个合法的CPMP解\n", "final_state_from_optimized_path = start_state\n", "for move in optimized_path:\n", "    final_state_from_optimized_path = apply_move(final_state_from_optimized_path, move)\n", "\n", "print(\"\\n--- 验证结果 ---\")\n", "print(f\"优化路径达到的最终状态: {final_state_from_optimized_path}\")\n", "\n", "# ### MODIFICATION ###: 使用新的验证函数\n", "if is_valid_cpmp_solution(final_state_from_optimized_path):\n", "    print(\"✅ 验证成功：优化后的路径能够正确达到一个合法的有序状态！\")\n", "else:\n", "    print(\"❌ 验证失败：优化后的路径未能达到一个合法的有序状态！\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0d33db25", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}