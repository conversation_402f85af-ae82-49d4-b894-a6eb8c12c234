import os
import time
import json
import numpy as np
import sys
import re # 用于自然排序

# 目标目录路径
target_directory = "/home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out"
 
# 切换工作目录（可选）
try:
    os.chdir(target_directory)
    print(f"成功切换工作目录到: {os.getcwd()}")
except Exception as e:
    print(f"切换工作目录失败: {e}")
 
# 关键：添加目标目录到 sys.path
if target_directory not in sys.path:
    sys.path.append(target_directory)

# 从您的模块导入必要的类和函数
# 确保这些模块在sys.path中或者在当前目录下
from pre_marshalling_astar_neural_dynamic_weight_no_goal import GraphPlanningBlocksWorld
from optimize_no_goal import optimize_path, apply_move, is_valid_cpmp_solution
from pre_marshalling_llm import LLMGuidance # 确保这个模块是可用的，尽管这里llm=None


# --- 配置参数 ---
DATASET_DIR = "/home/<USER>/cem208/code/tz_experiment/cpmpds_datasets_copy"

OUTPUT_DETAIL_FILE = "/home/<USER>/cem208/code/tz_experiment/Expriment/Experiment/result/cpmpds_test_results_S5.txt" # 详细报告文件
OUTPUT_SUMMARY_FILE = "/home/<USER>/cem208/code/tz_experiment/Expriment/Experiment/result/cpmpds_test_results_summary_S5.txt" # 总结报告文件

A_STAR_MAX_ITERATIONS = 100000
LEGEND_MAX_ITERATIONS = 50000
LEGEND_RUNS_PER_INSTANCE = 10

# !!! 请确认您的模型路径正确 !!!
MODEL_PATH = "/home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth"


def parse_instance_file(file_path):
    """从文件中解析start_state, g_canonical, fix_order"""
    data = {}
    with open(file_path, 'r') as f:
        lines = f.readlines()
        for line in lines:
            if line.startswith("Start State:"):
                # 使用eval在这里是安全的，因为我们控制输入格式
                start_state_str = line.replace("Start State:", "").strip().replace("'", "\"")
                data['start_state'] = eval(start_state_str)
            elif line.startswith("G_canonical:"):
                g_canonical_str = line.replace("G_canonical:", "").strip().replace("'", "\"")
                data['g_canonical'] = eval(g_canonical_str)
            elif line.startswith("Fix Order:"):
                fix_order_str = line.replace("Fix Order:", "").strip().replace("'", "\"")
                data['fix_order'] = eval(fix_order_str)
    return data

def run_a_star_test(start_state, g_canonical, fix_order, max_iterations, log_file_prefix="a_star"):
    """运行纯A*测试并返回结果"""
    log_file = f"{log_file_prefix}_{os.getpid()}.txt" # 使用PID避免多进程写入冲突
    planner = GraphPlanningBlocksWorld(
        start_state=start_state,
        goal_state=g_canonical,
        fix_order=fix_order,
        log_file=log_file
    )
    start_time = time.time()
    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=max_iterations)
    end_time = time.time()
    duration = end_time - start_time

    # 清理日志文件 (如果不需要保留详细日志)
    if os.path.exists(log_file):
        os.remove(log_file)

    solution_len = len(solution) if solution else -1 # -1表示未找到解决方案
    return solution_len, nodes_count, duration, solution

def run_legend_test(start_state, g_canonical, fix_order, model_path, max_iterations, is_consistency=False, log_file_prefix="legend"):
    """运行LEGEND算法测试并返回结果"""
    log_file = f"{log_file_prefix}_{os.getpid()}.txt" # 使用PID避免多进程写入冲突
    planner = GraphPlanningBlocksWorld(
        start_state=start_state,
        goal_state=g_canonical,
        fix_order=fix_order,
        model_path=model_path,
        log_file=log_file
    )
    start_time = time.time()
    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=max_iterations, is_consistency=is_consistency)
    end_time = time.time()
    duration = end_time - start_time

    # 清理日志文件 (如果不需要保留详细日志)
    if os.path.exists(log_file):
        os.remove(log_file)

    solution_len = len(solution) if solution else -1
    return solution_len, nodes_count, duration, solution

def natural_sort_key(s):
    """
    用于自然语言排序的key函数，将字符串中的数字部分转换为整数。
    例如：'instance_10.txt' 会在 'instance_2.txt' 之后。
    """
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split('([0-9]+)', s)]


def main():
    # 确保输出目录存在
    os.makedirs(os.path.dirname(OUTPUT_DETAIL_FILE), exist_ok=True)
    os.makedirs(os.path.dirname(OUTPUT_SUMMARY_FILE), exist_ok=True)

    with open(OUTPUT_DETAIL_FILE, 'w') as f_detail, \
         open(OUTPUT_SUMMARY_FILE, 'w') as f_summary: # 打开两个文件句柄
        
        f_detail.write("--- CPMPDS 算法性能测试结果 (详细报告) ---\n\n")
        f_summary.write("--- CPMPDS 算法性能测试结果 (总结报告) ---\n\n")

        all_config_names = sorted(os.listdir(DATASET_DIR))

        for config_name in all_config_names:
            config_path = os.path.join(DATASET_DIR, config_name)
            if not os.path.isdir(config_path):
                continue

            f_detail.write(f"\n{'-' * 80}\n")
            f_detail.write(f"测试配置: {config_name}\n")
            f_detail.write(f"{'-' * 80}\n\n")
            print(f"\n--- 开始测试配置: {config_name} ---") # 命令行输出

            # 修正这里的排序逻辑
            instance_files = sorted(
                [f for f in os.listdir(config_path) if f.startswith('instance_') and f.endswith('.txt')],
                key=natural_sort_key
            )

            # 存储A*和LEGEND每个实例的成功结果，用于最终的平均值的平均值计算
            config_a_star_solved_path_lengths = []
            config_a_star_solved_nodes_counts = []
            config_a_star_solved_durations = []
            a_star_unsolved_count = 0

            # 新增：记录A*找到的最优解路径长度，用于LEGEND的最优性比较
            a_star_optimal_lengths_per_instance = {} 

            # LEGEND (未优化) 结果
            config_legend_raw_avg_path_lengths = []
            config_legend_raw_avg_nodes_counts = []
            config_legend_raw_avg_durations = []
            legend_unsolved_count_raw = 0 
            legend_optimal_count_raw = 0 # 新增：LEGEND（未优化）找到A*最优解的实例数
            legend_partial_unsolved_count_raw = 0 # 新增：LEGEND（未优化）存在至少一次无解情况的实例数

            # LEGEND (优化后) 结果
            config_legend_optimized_avg_path_lengths = []
            config_legend_optimized_avg_nodes_counts = []
            config_legend_optimized_avg_durations = []
            legend_unsolved_count_optimized = 0 
            legend_optimal_count_optimized = 0 # 新增：LEGEND（优化后）找到A*最优解的实例数
            legend_partial_unsolved_count_optimized = 0 # 新增：LEGEND（优化后）存在至少一次无解情况的实例数


            for i, instance_file in enumerate(instance_files):
                print(f"  正在处理实例: {instance_file} ({i+1}/{len(instance_files)})") # 命令行输出

                instance_path = os.path.join(config_path, instance_file)
                instance_data = parse_instance_file(instance_path)
                start_state = instance_data['start_state']
                g_canonical = instance_data['g_canonical']
                fix_order = instance_data['fix_order']

                f_detail.write(f"  --- 实例: {instance_file} ---\n")
                f_detail.write(f"  初始状态: {start_state}\n")

                # --- 运行纯 A* 算法 ---
                f_detail.write(f"\n  [纯 A* 算法]\n")
                a_star_solution_len, a_star_nodes_count, a_star_duration, a_star_solution = \
                    run_a_star_test(start_state, g_canonical, fix_order, A_STAR_MAX_ITERATIONS, f"{config_name}_A_star_inst{i+1}")

                if a_star_solution_len != -1:
                    f_detail.write(f"    找到解决方案。步数: {a_star_solution_len}, 搜索节点数: {a_star_nodes_count}, 耗时: {a_star_duration:.4f} 秒\n")
                    config_a_star_solved_path_lengths.append(a_star_solution_len)
                    config_a_star_solved_nodes_counts.append(a_star_nodes_count)
                    config_a_star_solved_durations.append(a_star_duration)
                    a_star_optimal_lengths_per_instance[instance_file] = a_star_solution_len
                else:
                    f_detail.write(f"    未找到解决方案。搜索节点数: {a_star_nodes_count}, 耗时: {a_star_duration:.4f} 秒\n")
                    a_star_unsolved_count += 1
                    a_star_optimal_lengths_per_instance[instance_file] = -1 # Mark as unsolveable by A*

                # --- 运行 LEGEND 算法 (多次运行并收集数据) ---
                f_detail.write(f"\n  [LEGEND 算法 (运行 {LEGEND_RUNS_PER_INSTANCE} 次)]\n")
                
                legend_raw_results_for_instance = [] # 存储每次运行的 (原始len, nodes, duration)
                legend_optimized_results_for_instance = [] # 存储每次运行的 (优化/选择后的len, nodes, duration, is_valid_after_opt)

                found_solution_in_any_raw_run = False # Track if any raw run found solution for current instance
                found_solution_in_any_optimized_run = False # Track if any optimized run found solution for current instance

                for run_idx in range(LEGEND_RUNS_PER_INSTANCE):
                    print(f"    - 正在运行 LEGEND 算法第 {run_idx+1}/{LEGEND_RUNS_PER_INSTANCE} 次...") # 命令行输出

                    g_solution_len, g_nodes_count, g_duration, g_solution = \
                        run_legend_test(start_state, g_canonical, fix_order, MODEL_PATH, LEGEND_MAX_ITERATIONS, is_consistency=False, log_file_prefix=f"{config_name}_LEGEND_inst{i+1}_run{run_idx+1}")
                    
                    # 记录原始运行结果，包括未找到解的情况
                    legend_raw_results_for_instance.append((g_solution_len, g_nodes_count, g_duration))
                    if g_solution_len != -1:
                        found_solution_in_any_raw_run = True

                    # 检查是否找到解，然后尝试优化
                    if g_solution_len != -1:
                        # 深拷贝start_state以进行优化验证，避免修改原始状态
                        initial_state_for_opt_check = {stack: list(blocks) for stack, blocks in start_state.items()}
                        optimized_sol = optimize_path(g_solution, initial_state_for_opt_check)
                        
                        # 验证优化后的路径
                        final_state_after_opt = {stack: list(blocks) for stack, blocks in initial_state_for_opt_check.items()} 
                        for move in optimized_sol:
                            final_state_after_opt = apply_move(final_state_after_opt, move)
                        
                        is_opt_valid = is_valid_cpmp_solution(final_state_after_opt)

                        if is_opt_valid:
                            chosen_len = len(optimized_sol)
                            legend_optimized_results_for_instance.append((chosen_len, g_nodes_count, g_duration, True))
                            found_solution_in_any_optimized_run = True
                        else:
                            # 优化无效，使用原始路径
                            chosen_len = g_solution_len
                            legend_optimized_results_for_instance.append((chosen_len, g_nodes_count, g_duration, False))
                            found_solution_in_any_optimized_run = True # Original solution found, even if optimized is invalid
                    else:
                        # 未找到解，优化结果也标记为未找到
                        legend_optimized_results_for_instance.append((-1, g_nodes_count, g_duration, False))
                
                # --- LEGEND (未优化) 结果统计 ---
                # 统计存在无解情况的实例
                if not found_solution_in_any_raw_run:
                    legend_unsolved_count_raw += 1 # All 10 runs failed
                elif any(res[0] == -1 for res in legend_raw_results_for_instance):
                    legend_partial_unsolved_count_raw += 1 # At least one run failed, but not all

                solved_legend_raw_runs = [res for res in legend_raw_results_for_instance if res[0] != -1]
                
                if not solved_legend_raw_runs: # If no solutions were found in any run
                    f_detail.write(f"    所有 {LEGEND_RUNS_PER_INSTANCE} 次运行均未找到解决方案。\n")
                else:
                    avg_path_len_raw = np.mean([res[0] for res in solved_legend_raw_runs])
                    avg_nodes_count_raw = np.mean([res[1] for res in solved_legend_raw_runs])
                    avg_duration_raw = np.mean([res[2] for res in solved_legend_raw_runs])
                    
                    config_legend_raw_avg_path_lengths.append(avg_path_len_raw)
                    config_legend_raw_avg_nodes_counts.append(avg_nodes_count_raw)
                    config_legend_raw_avg_durations.append(avg_duration_raw)

                    # 严格按照路径长度、节点数、耗时优先级选择最优
                    best_raw_run_result = min(solved_legend_raw_runs, key=lambda x: (x[0], x[1], x[2]))
                    f_detail.write(f"    10次运行平均值: 路径长度: {avg_path_len_raw:.2f}, 搜索节点数: {avg_nodes_count_raw:.2f}, 耗时: {avg_duration_raw:.4f} 秒\n")
                    f_detail.write(f"    10次运行最优值: 最优路径长度: {best_raw_run_result[0]}, 对应搜索节点数: {best_raw_run_result[1]}, 对应耗时: {best_raw_run_result[2]:.4f} 秒\n")

                    # 判断是否找到A*的最优解
                    if a_star_optimal_lengths_per_instance[instance_file] != -1 and \
                       best_raw_run_result[0] == a_star_optimal_lengths_per_instance[instance_file]:
                        legend_optimal_count_raw += 1


                # --- LEGEND (优化后) 结果统计 ---
                f_detail.write(f"  [LEGEND 算法 (运行 {LEGEND_RUNS_PER_INSTANCE} 次)]（优化后）\n")
                # 统计存在无解情况的实例
                if not found_solution_in_any_optimized_run:
                    legend_unsolved_count_optimized += 1 # All 10 runs failed or optimization invalidates all
                elif any(res[0] == -1 for res in legend_optimized_results_for_instance):
                    legend_partial_unsolved_count_optimized += 1 # At least one run failed (even after considering fallback to raw)

                solved_legend_optimized_runs = [res for res in legend_optimized_results_for_instance if res[0] != -1]

                if not solved_legend_optimized_runs: # If no solutions were found in any run (even after optimization consideration)
                    f_detail.write(f"    所有 {LEGEND_RUNS_PER_INSTANCE} 次运行（优化后）均未找到解决方案。\n")
                else:
                    avg_path_len_opt = np.mean([res[0] for res in solved_legend_optimized_runs])
                    avg_nodes_count_opt = np.mean([res[1] for res in solved_legend_optimized_runs])
                    avg_duration_opt = np.mean([res[2] for res in solved_legend_optimized_runs])

                    config_legend_optimized_avg_path_lengths.append(avg_path_len_opt)
                    config_legend_optimized_avg_nodes_counts.append(avg_nodes_count_opt)
                    config_legend_optimized_avg_durations.append(avg_duration_opt)

                    # 严格按照路径长度、节点数、耗时优先级选择最优
                    best_optimized_run_result = min(solved_legend_optimized_runs, key=lambda x: (x[0], x[1], x[2]))
                    f_detail.write(f"    10次运行平均值: 路径长度: {avg_path_len_opt:.2f}, 搜索节点数: {avg_nodes_count_opt:.2f}, 耗时: {avg_duration_opt:.4f} 秒\n")
                    f_detail.write(f"    10次运行最优值: 最优路径长度: {best_optimized_run_result[0]}, 对应搜索节点数: {best_optimized_run_result[1]}, 对应耗时: {best_optimized_run_result[2]:.4f} 秒\n")
                    
                    # 判断是否找到A*的最优解
                    if a_star_optimal_lengths_per_instance[instance_file] != -1 and \
                       best_optimized_run_result[0] == a_star_optimal_lengths_per_instance[instance_file]:
                        legend_optimal_count_optimized += 1
                
                f_detail.write("\n") # 实例之间空一行

                # --- 新增修改：强制将当前实例的详细结果写入磁盘 ---
                # 这能确保即使程序在下一个实例或总结部分崩溃，这个实例的结果也已保存。
                f_detail.flush()

            # --- 计算并输出该配置下两种算法的总结 ---
            summary_content = []
            summary_content.append(f"\n{'-' * 80}\n")
            summary_content.append(f"配置 {config_name} 总结:\n")
            summary_content.append(f"{'-' * 80}\n")

            # A* 算法总结
            summary_content.append(f"  纯 A* 算法总结:\n")
            if config_a_star_solved_path_lengths:
                final_avg_a_star_path_len = np.mean(config_a_star_solved_path_lengths)
                final_avg_a_star_nodes_count = np.mean(config_a_star_solved_nodes_counts)
                final_avg_a_star_duration = np.mean(config_a_star_solved_durations)
                summary_content.append(f"    在 {len(instance_files) - a_star_unsolved_count}/{len(instance_files)} 个实例中找到解决方案。\n")
                summary_content.append(f"    平均路径长度: {final_avg_a_star_path_len:.2f}\n")
                summary_content.append(f"    平均搜索节点数: {final_avg_a_star_nodes_count:.2f}\n")
                summary_content.append(f"    平均耗时: {final_avg_a_star_duration:.4f} 秒\n")
            else:
                summary_content.append(f"    所有 {len(instance_files)} 个实例均未找到解决方案。\n")
            
            # LEGEND 算法总结 (未优化)
            summary_content.append(f"\n  LEGEND 算法总结:\n")
            summary_content.append(f"    在 {legend_optimal_count_raw}/{len(instance_files)} 个实例中至少一次运行找到最优解。\n") # Changed wording
            
            # Print partial unsolved count
            summary_content.append(f"    在 {legend_partial_unsolved_count_raw}/{len(instance_files)} 个实例中存在至少一次运行无解的情况。\n")
            
            if config_legend_raw_avg_path_lengths:
                final_avg_legend_raw_path_len = np.mean(config_legend_raw_avg_path_lengths)
                final_avg_legend_raw_nodes_count = np.mean(config_legend_raw_avg_nodes_counts)
                final_avg_legend_raw_duration = np.mean(config_legend_raw_avg_durations)

                summary_content.append(f"    所有实例的平均路径长度的平均值: {final_avg_legend_raw_path_len:.2f}\n")
                summary_content.append(f"    所有实例的平均搜索节点数的平均值: {final_avg_legend_raw_nodes_count:.2f}\n")
                summary_content.append(f"    所有实例的平均耗时的平均值: {final_avg_legend_raw_duration:.4f} 秒\n")
            else:
                summary_content.append(f"    所有 {len(instance_files)} 个实例的任何运行均未找到解决方案。\n")

            # LEGEND 算法总结 (优化后)
            summary_content.append(f"\n  LEGEND 算法总结（优化后）:\n")
            summary_content.append(f"    在 {legend_optimal_count_optimized}/{len(instance_files)} 个实例中至少一次优化找到最优解。\n") # Changed wording
            
            # Print partial unsolved count
            summary_content.append(f"    在 {legend_partial_unsolved_count_optimized}/{len(instance_files)} 个实例中存在至少一次运行（优化后）无解的情况。\n")

            if config_legend_optimized_avg_path_lengths:
                final_avg_legend_optimized_path_len = np.mean(config_legend_optimized_avg_path_lengths)
                final_avg_legend_optimized_nodes_count = np.mean(config_legend_optimized_avg_nodes_counts)
                final_avg_legend_optimized_duration = np.mean(config_legend_optimized_avg_durations)

                summary_content.append(f"    所有实例的平均路径长度的平均值: {final_avg_legend_optimized_path_len:.2f}\n")
                summary_content.append(f"    所有实例的平均搜索节点数的平均值: {final_avg_legend_optimized_nodes_count:.2f}\n")
                summary_content.append(f"    所有实例的平均耗时的平均值: {final_avg_legend_optimized_duration:.4f} 秒\n")
            else:
                summary_content.append(f"    所有 {len(instance_files)} 个实例均未能找到任何可用（包括优化后或原始）的解决方案。\n")
            
            summary_content.append(f"{'=' * 80}\n\n")

            # 将总结内容写入两个文件
            f_detail.writelines(summary_content)
            f_summary.writelines(summary_content)

    print(f"\n所有测试完成！详细结果已保存到 '{OUTPUT_DETAIL_FILE}'。")
    print(f"总结报告已保存到 '{OUTPUT_SUMMARY_FILE}'。")

if __name__ == "__main__":
    main()