{"cells": [{"cell_type": "code", "execution_count": 1, "id": "91217d97-9d7e-48e5-8f93-18848ffec80e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录已修改为: /home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out\n"]}], "source": ["import os\n", "\n", "# 指定目标路径（替换为你的实际路径）\n", "target_dir = os.path.expanduser(r\"/home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out\")\n", "\n", "# 检查路径是否存在\n", "if os.path.exists(target_dir):\n", "    os.chdir(target_dir)  # 修改工作目录\n", "    print(f\"当前工作目录已修改为: {os.getcwd()}\")\n", "else:\n", "    print(f\"错误：路径不存在 - {target_dir}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "68b5971b-c235-4e70-bf92-437b6bf20a23", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/envs/pre_marshalling/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pre_marshalling_astar_neural_dynamic_weights import GraphPlanningBlocksWorld\n", "from pre_marshalling_llm import LLMGuidance\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 3, "id": "9e5a6893-e3bf-4305-abcc-c2711549ec4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Running permutation 1/24: ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/cem208/code/tz_experiment/Expriment/cnn_transformer_block_in_out/pre_marshalling_astar_neural_dynamic_weights.py:150: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  checkpoint = torch.load(model_path, map_location=self.device)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 2), (5, 1), (5, 1), (2, 5), (2, 5), (2, 3), (5, 1), (5, 1), (5, 1), (3, 5), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (1, 2), (1, 5), (1, 2), (3, 5), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (4, 1), (4, 3), (1, 5), (1, 3), (5, 3), (5, 1), (5, 3), (1, 3)]\n", "Nodes searched: 396\n", "Path length: 72\n", "\n", "Time consumption: 2.2478866577148438 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 2), (5, 1), (5, 1), (2, 5), (2, 5), (2, 3), (5, 1), (5, 1), (5, 1), (3, 5), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (1, 2), (1, 5), (1, 2), (3, 5), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (4, 1), (4, 3), (1, 5), (1, 3), (5, 3), (5, 1), (5, 3), (1, 3)]\n", "Nodes searched: 396\n", "Path.Length: 72\n", "\n", "Running permutation 2/24: ['Stack4', 'Stack1', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 2), (5, 1), (5, 1), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 1), (4, 3), (1, 5), (1, 5), (2, 1), (2, 3), (5, 1), (5, 3), (1, 3), (1, 2), (1, 4), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (5, 1), (5, 1), (5, 2), (5, 2), (4, 2), (1, 5), (1, 2), (5, 2)]\n", "Nodes searched: 261\n", "Path length: 76\n", "\n", "Time consumption: 1.049665927886963 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 2), (5, 1), (5, 1), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 1), (4, 3), (1, 5), (1, 5), (2, 1), (2, 3), (5, 1), (5, 3), (1, 3), (1, 2), (1, 4), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (5, 1), (5, 1), (5, 2), (5, 2), (4, 2), (1, 5), (1, 2), (5, 2)]\n", "Nodes searched: 261\n", "Path.Length: 76\n", "\n", "Running permutation 3/24: ['Stack4', 'Stack2', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (4, 2), (1, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 4), (5, 2), (5, 1), (2, 1), (5, 3), (5, 1), (5, 1), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 3), (4, 3), (5, 3), (1, 3)]\n", "Nodes searched: 299\n", "Path length: 72\n", "\n", "Time consumption: 1.3037652969360352 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (4, 2), (1, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 4), (5, 2), (5, 1), (2, 1), (5, 3), (5, 1), (5, 1), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 3), (4, 3), (5, 3), (1, 3)]\n", "Nodes searched: 299\n", "Path.Length: 72\n", "\n", "Running permutation 4/24: ['Stack4', 'Stack2', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (4, 2), (1, 5), (1, 5), (1, 5), (1, 2), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 312\n", "Path length: 85\n", "\n", "Time consumption: 1.2241299152374268 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2), (4, 2), (1, 5), (1, 5), (1, 5), (1, 2), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 312\n", "Path.Length: 85\n", "\n", "Running permutation 5/24: ['Stack4', 'Stack3', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 2)]\n", "Nodes searched: 1555\n", "Path length: 116\n", "\n", "Time consumption: 6.676145553588867 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 2)]\n", "Nodes searched: 1555\n", "Path.Length: 116\n", "\n", "Running permutation 6/24: ['Stack4', 'Stack3', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 1550\n", "Path length: 111\n", "\n", "Time consumption: 6.535748720169067 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (3, 5), (3, 5), (1, 5), (3, 5), (1, 5), (1, 4), (2, 3), (2, 5), (2, 4), (2, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (1, 2), (2, 5), (1, 5), (1, 2), (1, 4), (3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 1550\n", "Path.Length: 111\n", "\n", "Running permutation 7/24: ['Stack1', 'Stack4', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (5, 4), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (2, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (2, 3), (2, 5), (2, 4), (5, 1), (5, 1), (5, 4), (1, 4), (2, 5), (1, 2), (2, 5), (2, 5), (1, 2), (3, 5), (3, 2), (5, 2), (5, 1), (5, 1), (5, 2), (3, 4), (5, 3), (1, 5), (1, 5), (1, 2), (3, 2), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3), (1, 3), (4, 3)]\n", "Nodes searched: 352\n", "Path length: 54\n", "\n", "Time consumption: 1.3942248821258545 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (5, 4), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (2, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (2, 3), (2, 5), (2, 4), (5, 1), (5, 1), (5, 4), (1, 4), (2, 5), (1, 2), (2, 5), (2, 5), (1, 2), (3, 5), (3, 2), (5, 2), (5, 1), (5, 1), (5, 2), (3, 4), (5, 3), (1, 5), (1, 5), (1, 2), (3, 2), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3), (1, 3), (4, 3)]\n", "Nodes searched: 352\n", "Path.Length: 54\n", "\n", "Running permutation 8/24: ['Stack1', 'Stack4', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (5, 4), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (2, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (2, 3), (2, 5), (2, 4), (5, 1), (5, 1), (5, 4), (1, 4), (2, 3), (1, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 3), (2, 5), (2, 3), (5, 3), (1, 3), (5, 2), (5, 1), (5, 3), (2, 5), (1, 2), (5, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 2)]\n", "Nodes searched: 487\n", "Path length: 56\n", "\n", "Time consumption: 2.0679352283477783 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (5, 4), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (2, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (3, 5), (2, 3), (2, 5), (2, 4), (5, 1), (5, 1), (5, 4), (1, 4), (2, 3), (1, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 3), (2, 5), (2, 3), (5, 3), (1, 3), (5, 2), (5, 1), (5, 3), (2, 5), (1, 2), (5, 1), (5, 2), (5, 2), (5, 2), (1, 2), (5, 2)]\n", "Nodes searched: 487\n", "Path.Length: 56\n", "\n", "Running permutation 9/24: ['Stack1', 'Stack2', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (3, 5), (3, 2), (4, 2), (5, 4), (1, 5), (4, 5), (1, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 4), (1, 4), (3, 5), (1, 5), (1, 3), (5, 3), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3)]\n", "Nodes searched: 333\n", "Path length: 67\n", "\n", "Time consumption: 1.4394328594207764 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (3, 5), (3, 2), (4, 2), (5, 4), (1, 5), (4, 5), (1, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 4), (1, 4), (3, 5), (1, 5), (1, 3), (5, 3), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3)]\n", "Nodes searched: 333\n", "Path.Length: 67\n", "\n", "Running permutation 10/24: ['Stack1', 'Stack2', 'Stack3', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (3, 5), (3, 2), (4, 2), (5, 4), (1, 5), (4, 5), (1, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 4), (1, 4), (3, 5), (1, 5), (1, 3), (5, 3), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3)]\n", "Nodes searched: 347\n", "Path length: 67\n", "\n", "Time consumption: 1.3711068630218506 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (3, 5), (3, 2), (4, 2), (5, 4), (1, 5), (4, 5), (1, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 4), (1, 4), (3, 5), (1, 5), (1, 3), (5, 3), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 3)]\n", "Nodes searched: 347\n", "Path.Length: 67\n", "\n", "Running permutation 11/24: ['Stack1', 'Stack3', 'Stack4', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (3, 4), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (2, 5), (2, 5), (2, 5), (2, 5), (2, 3), (5, 2), (5, 1), (5, 3), (1, 2), (1, 3), (5, 1), (4, 1), (5, 4), (2, 4), (5, 1), (5, 1), (5, 4), (1, 5), (2, 4), (5, 2), (1, 4), (5, 4), (1, 5), (1, 5), (1, 2), (1, 3), (4, 5), (4, 5), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 3051\n", "Path length: 62\n", "\n", "Time consumption: 12.561450242996216 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (3, 4), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (2, 5), (2, 5), (2, 5), (2, 5), (2, 3), (5, 2), (5, 1), (5, 3), (1, 2), (1, 3), (5, 1), (4, 1), (5, 4), (2, 4), (5, 1), (5, 1), (5, 4), (1, 5), (2, 4), (5, 2), (1, 4), (5, 4), (1, 5), (1, 5), (1, 2), (1, 3), (4, 5), (4, 5), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 3051\n", "Path.Length: 62\n", "\n", "Running permutation 12/24: ['Stack1', 'Stack3', 'Stack2', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (3, 4), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (2, 5), (2, 5), (2, 5), (2, 5), (2, 3), (5, 2), (5, 1), (5, 3), (1, 2), (1, 3), (5, 1), (4, 1), (5, 4), (2, 4), (5, 2), (5, 1), (5, 4), (5, 2), (1, 5), (1, 4), (5, 4), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 5), (4, 2), (5, 1), (5, 1), (5, 1), (5, 2), (5, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 5939\n", "Path length: 60\n", "\n", "Time consumption: 24.17953085899353 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (3, 1), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (3, 4), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (2, 5), (2, 5), (2, 5), (2, 5), (2, 3), (5, 2), (5, 1), (5, 3), (1, 2), (1, 3), (5, 1), (4, 1), (5, 4), (2, 4), (5, 2), (5, 1), (5, 4), (5, 2), (1, 5), (1, 4), (5, 4), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 5), (4, 2), (5, 1), (5, 1), (5, 1), (5, 2), (5, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 5939\n", "Path.Length: 60\n", "\n", "Running permutation 13/24: ['Stack2', 'Stack4', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (3, 5), (3, 5), (3, 4), (1, 3), (5, 2), (5, 4), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1), (3, 5), (5, 1), (5, 1), (5, 3), (1, 5), (1, 3), (2, 3), (4, 3), (5, 3), (5, 3)]\n", "Nodes searched: 224\n", "Path length: 94\n", "\n", "Time consumption: 1.0562942028045654 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (3, 5), (3, 5), (3, 4), (1, 3), (5, 2), (5, 4), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1), (3, 5), (5, 1), (5, 1), (5, 3), (1, 5), (1, 3), (2, 3), (4, 3), (5, 3), (5, 3)]\n", "Nodes searched: 224\n", "Path.Length: 94\n", "\n", "Running permutation 14/24: ['Stack2', 'Stack4', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (3, 5), (3, 5), (3, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 239\n", "Path length: 109\n", "\n", "Time consumption: 0.9452207088470459 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (3, 5), (3, 5), (3, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (5, 1), (5, 1)]\n", "Nodes searched: 239\n", "Path.Length: 109\n", "\n", "Running permutation 15/24: ['Stack2', 'Stack1', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (1, 5), (1, 4), (3, 4), (1, 2), (1, 4), (1, 3), (2, 3), (5, 3), (5, 3), (1, 3), (4, 3)]\n", "Nodes searched: 300\n", "Path length: 83\n", "\n", "Time consumption: 1.262648105621338 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (1, 5), (1, 4), (3, 4), (1, 2), (1, 4), (1, 3), (2, 3), (5, 3), (5, 3), (1, 3), (4, 3)]\n", "Nodes searched: 300\n", "Path.Length: 83\n", "\n", "Running permutation 16/24: ['Stack2', 'Stack1', 'Stack3', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (3, 4), (5, 1), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (1, 2), (1, 2), (4, 5), (1, 4), (1, 5), (1, 3), (5, 3), (2, 5), (2, 4), (5, 4), (5, 4)]\n", "Nodes searched: 159\n", "Path length: 85\n", "\n", "Time consumption: 0.63498854637146 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 1), (3, 4), (5, 1), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (1, 2), (1, 2), (4, 5), (1, 4), (1, 5), (1, 3), (5, 3), (2, 5), (2, 4), (5, 4), (5, 4)]\n", "Nodes searched: 159\n", "Path.Length: 85\n", "\n", "Running permutation 17/24: ['Stack2', 'Stack3', 'Stack4', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (4, 5), (4, 5), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (4, 1), (4, 5), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 4), (2, 4), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (2, 1)]\n", "Nodes searched: 160\n", "Path length: 90\n", "\n", "Time consumption: 0.6137285232543945 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (4, 5), (4, 5), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (4, 1), (4, 5), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 4), (2, 4), (1, 4), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (2, 1)]\n", "Nodes searched: 160\n", "Path.Length: 90\n", "\n", "Running permutation 18/24: ['Stack2', 'Stack3', 'Stack1', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (4, 5), (4, 5), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (5, 4), (5, 1), (5, 4), (2, 4), (1, 4)]\n", "Nodes searched: 153\n", "Path length: 83\n", "\n", "Time consumption: 0.5849709510803223 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (3, 4), (1, 3), (3, 5), (1, 3), (3, 5), (1, 3), (1, 5), (3, 5), (1, 3), (5, 1), (3, 5), (1, 3), (3, 5), (1, 3), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 5), (4, 5), (4, 5), (5, 3), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 4), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 1), (5, 4), (4, 1), (5, 4), (5, 1), (5, 4), (2, 4), (1, 4)]\n", "Nodes searched: 153\n", "Path.Length: 83\n", "\n", "Running permutation 19/24: ['Stack3', 'Stack4', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 86\n", "Path length: 84\n", "\n", "Time consumption: 0.3589797019958496 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 86\n", "Path.Length: 84\n", "\n", "Running permutation 20/24: ['Stack3', 'Stack4', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (3, 2), (1, 2), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (3, 2), (5, 1), (1, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 1), (5, 1)]\n", "Nodes searched: 624\n", "Path length: 68\n", "\n", "Time consumption: 2.4895825386047363 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (3, 2), (1, 2), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (3, 2), (5, 1), (1, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 1), (5, 1)]\n", "Nodes searched: 624\n", "Path.Length: 68\n", "\n", "Running permutation 21/24: ['Stack3', 'Stack1', 'Stack4', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 1), (5, 1), (2, 3), (3, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 4), (5, 3), (3, 4), (5, 2), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (1, 2), (2, 5), (2, 5), (1, 2), (2, 5), (1, 2), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 273\n", "Path length: 86\n", "\n", "Time consumption: 1.15444016456604 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 1), (5, 1), (2, 3), (3, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 4), (5, 3), (3, 4), (5, 2), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (1, 2), (2, 5), (2, 5), (1, 2), (2, 5), (1, 2), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2)]\n", "Nodes searched: 273\n", "Path.Length: 86\n", "\n", "Running permutation 22/24: ['Stack3', 'Stack1', 'Stack2', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 1), (5, 1), (2, 3), (3, 5), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 2), (5, 4), (1, 4), (1, 5), (1, 2), (4, 5), (4, 2), (5, 2), (5, 4), (5, 1), (5, 2), (1, 5), (1, 4), (5, 4), (5, 4)]\n", "Nodes searched: 94\n", "Path length: 82\n", "\n", "Time consumption: 0.3862583637237549 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 1), (5, 1), (2, 3), (3, 5), (2, 4), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (4, 5), (4, 2), (5, 4), (1, 4), (1, 5), (1, 2), (4, 5), (4, 2), (5, 2), (5, 4), (5, 1), (5, 2), (1, 5), (1, 4), (5, 4), (5, 4)]\n", "Nodes searched: 94\n", "Path.Length: 82\n", "\n", "Running permutation 23/24: ['Stack3', 'Stack2', 'Stack4', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 3), (5, 2), (5, 3), (1, 5), (1, 5), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 4), (5, 4), (3, 4), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 1), (5, 1)]\n", "Nodes searched: 500\n", "Path length: 62\n", "\n", "Time consumption: 2.084773063659668 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 3), (5, 2), (5, 3), (1, 5), (1, 5), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (5, 1), (5, 2), (4, 5), (4, 5), (4, 5), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 4), (5, 4), (3, 4), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 1), (1, 5), (2, 1), (5, 1)]\n", "Nodes searched: 500\n", "Path.Length: 62\n", "\n", "Running permutation 24/24: ['Stack3', 'Stack2', 'Stack1', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 3), (5, 2), (5, 3), (1, 5), (1, 5), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (5, 1), (5, 2), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 1), (5, 1), (5, 4), (2, 5), (2, 4), (5, 4), (3, 4)]\n", "Nodes searched: 489\n", "Path length: 57\n", "\n", "Time consumption: 1.9084422588348389 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 5), (2, 5), (2, 1), (2, 5), (2, 3), (5, 2), (5, 3), (4, 3), (5, 1), (2, 1), (5, 2), (5, 1), (2, 1), (5, 2), (5, 1), (5, 1), (5, 3), (5, 2), (5, 3), (1, 5), (1, 5), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (5, 1), (5, 2), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (4, 5), (4, 1), (5, 1), (5, 4), (2, 5), (2, 4), (5, 4), (3, 4)]\n", "Nodes searched: 489\n", "Path.Length: 57\n"]}], "source": ["#is_consistency=False next.g-1 0.667 1\n", "#5*20-1\n", "import time\n", "from itertools import permutations\n", "\n", "# Define the initial and goal states\n", "# Define the initial and goal states\n", "start_state={'Stack1': ['J', 'N', 'T', 'K', 'H', 'R'], 'Stack2': ['G', 'B', 'O', 'E', 'I'], 'Stack3': ['M', 'L', 'C', 'Q', 'P'], 'Stack4': ['F', 'D', 'S', 'A'], 'Stack5': []}\n", "goal_state={'Stack1': ['P', 'Q', 'F', 'D'], 'Stack2': ['K', 'L', 'I', 'B', 'T', 'R'], 'Stack3': ['A', 'J', 'G', 'E', 'S', 'M'], 'Stack4': ['N', 'O', 'H', 'C'], 'Stack5': []}\n", "stack_order = ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "model_path = \"/home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'trained_model_test.txt'\n", "\n", "# Generate all permutations of stack_order\n", "stack_permutations = list(permutations(stack_order))\n", "\n", "# Loop through each permutation\n", "for idx, order in enumerate(stack_permutations, 1):\n", "    print(f\"\\nRunning permutation {idx}/{len(stack_permutations)}: {list(order)}\")\n", "    start = time.time()\n", "    \n", "    # Initialize the planner with the current stack order\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state, list(order), model_path=model_path, log_file=log_file)\n", "    \n", "    # Run A* search\n", "    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=50000, is_consistency=False)\n", "    \n", "    # Print results\n", "    print(\"Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Solution found:\", solution)\n", "    print(\"Nodes searched:\", nodes_count)\n", "    print(\"Path.Length:\", 0 if solution is None else len(solution))"]}, {"cell_type": "code", "execution_count": 3, "id": "0b7df26b", "metadata": {}, "outputs": [], "source": ["from pre_marshalling_astar_neural_dynamic_weight_no_goal import GraphPlanningBlocksWorld\n", "from pre_marshalling_llm import LLMGuidance\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 4, "id": "6ae4de23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试一：A*算法 ---\n", "自动生成的 G_canonical 目标:\n", "{'Stack1': [6, 5, 4, 3, 2, 1], 'Stack2': [], 'Stack3': []}\n", "自动生成的修复顺序 (fix_order):\n", "['Stack1', 'Stack2', 'Stack3']\n", "Running A* without guidance:\n", "Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "Nodes searched: 7\n", "Path length: 4\n", "\n", "耗时: 0.0479 秒\n", "找到解决方案，共 4 步: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "搜索的节点数: 7\n"]}], "source": ["print(\"\\n--- 测试一：A*算法 ---\")\n", "letter_map = {chr(ord('A') + i): i + 1 for i in range(26)}\n", "\n", "# start_state_letters = {'Stack1': ['J', 'N', 'T', 'K', 'H', 'R'], \n", "#                         'Stack2': ['G', 'B', 'O', 'E', 'I'], \n", "#                         'Stack3': ['M', 'L', 'C', 'Q', 'P'], \n", "#                         'Stack4': ['F', 'D', 'S', 'A'], \n", "#                         'Stack5': []}\n", "\n", "\n", "start_state_3 = {\n", "    'Stack1': [1, 2, 6],\n", "    'Stack2': [3, 4, 5],\n", "    'Stack3': []\n", "}\n", "\n", "start_state_numeric = {stack: [c for c in blocks] for stack, blocks in start_state_3.items()}\n", "\n", "# 2. 自动生成 G_canonical (人工引力场)\n", "all_blocks = sorted([item for sublist in start_state_numeric.values() for item in sublist], reverse=True)\n", "\n", "# 假设我们总是选择 'Stack1' 作为目标栈\n", "target_stack_name = 'Stack1'\n", "g_canonical = {stack_name: [] for stack_name in start_state_numeric.keys()}\n", "g_canonical[target_stack_name] = all_blocks\n", "\n", "print(\"自动生成的 G_canonical 目标:\")\n", "print(g_canonical)\n", "\n", "# 3. 自动生成 fix_order\n", "# 把 G_canonical 的目标栈放在第一位\n", "stack_names = list(start_state_numeric.keys())\n", "fix_order = sorted(stack_names, key=lambda x: x != target_stack_name)\n", "\n", "print(\"自动生成的修复顺序 (fix_order):\")\n", "print(fix_order)\n", "\n", "log_file = 'a*_test.txt'\n", "\n", "# 5. 运行规划器\n", "start = time.time()\n", "\n", "\n", "planner = GraphPlanningBlocksWorld(\n", "    start_state=start_state_numeric, \n", "    goal_state=g_canonical, \n", "    fix_order=fix_order, \n", "    log_file=log_file\n", ")\n", "    \n", "# 运行A*搜索，此时 llm=None, model_path不为None, 将会使用模型指导\n", "solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000)\n", "\n", "print(f\"耗时: {time.time() - start:.4f} 秒\")\n", "if solution:\n", "    print(f\"找到解决方案，共 {len(solution)} 步: {solution}\")\n", "else:\n", "    print(\"未找到解决方案。\")\n", "print(f\"搜索的节点数: {nodes_count}\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c058c82b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试二：G_canonical + 模型引导的A*算法 ---\n", "自动生成的 G_canonical 目标:\n", "{'Stack1': [6, 5, 4, 3, 2, 1], 'Stack2': [], 'Stack3': []}\n", "自动生成的修复顺序 (fix_order):\n", "['Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 7, 9, 6)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 12, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 3x4\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 6 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "Nodes searched: 7\n", "Path length: 4\n", "\n", "耗时: 0.0664 秒\n", "找到解决方案，共 4 步: [(1, 3), (2, 3), (2, 3), (1, 2)]\n", "搜索的节点数: 7\n"]}], "source": ["print(\"\\n--- 测试二：G_canonical + 模型引导的A*算法 ---\")\n", "letter_map = {chr(ord('A') + i): i + 1 for i in range(26)}\n", "\n", "# start_state_letters = {'Stack1': ['J', 'N', 'T', 'K', 'H', 'R'], \n", "#                         'Stack2': ['G', 'B', 'O', 'E', 'I'], \n", "#                         'Stack3': ['M', 'L', 'C', 'Q', 'P'], \n", "#                         'Stack4': ['F', 'D', 'S', 'A'], \n", "#                         'Stack5': []}\n", "\n", "start_state_3 = {\n", "    'Stack1': [1, 2, 6],\n", "    'Stack2': [3, 4, 5],\n", "    'Stack3': []\n", "}\n", "\n", "start_state_numeric = {stack: [c for c in blocks] for stack, blocks in start_state_3.items()}\n", "\n", "# 2. 自动生成 G_canonical (人工引力场)\n", "all_blocks = sorted([item for sublist in start_state_numeric.values() for item in sublist], reverse=True)\n", "\n", "# 假设我们总是选择 'Stack1' 作为目标栈\n", "target_stack_name = 'Stack1'\n", "g_canonical = {stack_name: [] for stack_name in start_state_numeric.keys()}\n", "g_canonical[target_stack_name] = all_blocks\n", "\n", "print(\"自动生成的 G_canonical 目标:\")\n", "print(g_canonical)\n", "\n", "# 3. 自动生成 fix_order\n", "# 把 G_canonical 的目标栈放在第一位\n", "stack_names = list(start_state_numeric.keys())\n", "fix_order = sorted(stack_names, key=lambda x: x != target_stack_name)\n", "\n", "print(\"自动生成的修复顺序 (fix_order):\")\n", "print(fix_order)\n", "\n", "# 4. 定义模型和日志路径\n", "# 请确保这里的路径是正确的，并且相关的依赖文件存在\n", "model_path = \"/home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'g_canonical_model_test.txt'\n", "\n", "# 5. 运行规划器\n", "start = time.time()\n", "\n", "\n", "planner = GraphPlanningBlocksWorld(\n", "    start_state=start_state_numeric, \n", "    goal_state=g_canonical, \n", "    fix_order=fix_order, \n", "    model_path=model_path, \n", "    log_file=log_file\n", ")\n", "    \n", "# 运行A*搜索，此时 llm=None, model_path不为None, 将会使用模型指导\n", "solution, nodes_count = planner.a_star_search(llm=None, max_iterations=50000, is_consistency=False)\n", "\n", "print(f\"耗时: {time.time() - start:.4f} 秒\")\n", "if solution:\n", "    print(f\"找到解决方案，共 {len(solution)} 步: {solution}\")\n", "else:\n", "    print(\"未找到解决方案。\")\n", "print(f\"搜索的节点数: {nodes_count}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "2285da43", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}