import json
import numpy as np
import pandas as pd
from pre_marshalling_astar_neural_dynamic_weights import GraphPlanningBlocksWorld
import ast
import re

import re
import ast


def parse_log(log_content):
    """
    从新格式日志中提取状态、动作信息、修复栈ID（数字）和关注块名称（字符串）。
    以空行或包含 "Current fix stack:" 的新 Node 行作为样本的开始和上一个样本的结束。
    """
    lines = log_content.split('\n')
    samples = []
    current_sample = None

    for line in lines:
        line = line.strip()

        # 当遇到定义了 "Current fix stack:" 的 Node 行时，
        # 意味着上一个样本（如果存在且有效）结束，新的样本开始。
        if line.startswith("Node ") and "Current fix stack:" in line:
            # 1. 保存上一个有效样本
            if current_sample and \
                    current_sample.get("current_state") is not None and \
                    current_sample.get("goal_state") is not None and \
                    (current_sample.get("best_action") is not None or current_sample.get("worst_action") is not None):
                samples.append(current_sample)

            # 2. 初始化新样本
            node_id_str = re.search(r'Node (\d+):', line)
            node_id = int(node_id_str.group(1)) if node_id_str else None

            fix_stack_str = re.search(r'Current fix stack: \*\*Stack(\d+)\*\*', line)
            # fix_stack_id 存储的是栈的数字编号，例如 Stack4 -> 4
            fix_stack_id = int(fix_stack_str.group(1)) if fix_stack_str else None

            attention_block_name = None  # 初始化关注块名称
            task_description = None  # 初始化优先任务描述
            priority_task_match = re.search(r'Priority task: (.*)', line)
            if priority_task_match:
                task_description = priority_task_match.group(1).strip()

                # 模式1: "将{block}移入..." (例如: "将O移入Stack4")
                # \w+ 会匹配一个或多个字母、数字或下划线。
                # 如果你的积木块名称严格为单个大写字母，可以使用 ([A-Z])
                match_move_to = re.search(r"将(\w+)移入", task_description)
                if match_move_to:
                    attention_block_name = match_move_to.group(1)  # 例如 'O'
                else:
                    # 模式2: "移走{stack}顶部错误块{block}" (例如: "移走Stack4顶部错误块C")
                    match_remove_top = re.search(r"移走Stack\d+顶部错误块(\w+)", task_description)
                    if match_remove_top:
                        attention_block_name = match_remove_top.group(1)  # 例如 'C'
                    else:
                        # 模式3: "移入目标状态的下一块{block}" (例如: "移入目标状态的下一块N")
                        match_move_next = re.search(r"移入目标状态的下一块(\w+)", task_description)
                        if match_move_next:
                            attention_block_name = match_move_next.group(1)  # 例如 'N'
                # 如果任务是 "StackX目标为空..." 或 "StackX已匹配..." 等不涉及具体单个移动块的，
                # attention_block_name 将保持为 None。

            current_sample = {
                "node": node_id,
                "current_state": None,
                "goal_state": None,
                "best_action": None,
                "worst_action": None,
                "fix_stack_id": fix_stack_id,  # 例如：4 (代表Stack4)
                "attention_block_name": attention_block_name, # 例如：'O' 或 None
                "priority_task_description": task_description  # 新增字段
            }

        # 处理当前状态
        elif line.startswith("Node ") and "Current state:" in line:
            if current_sample:
                state_str = line.split("Current state: ")[1]
                current_sample["current_state"] = ast.literal_eval(state_str)
        # 处理目标状态
        elif line.startswith("Node ") and "Goal state:" in line:
            if current_sample:
                state_str = line.split("Goal state: ")[1]
                current_sample["goal_state"] = ast.literal_eval(state_str)
        # 处理最佳动作
        elif line.startswith("Node ") and "LLM suggests Best Action" in line:
            if current_sample:
                action_str = re.search(r'\(([^)]+)\)', line).group(1)
                action = tuple(int(x) for x in action_str.split(', '))
                current_sample["best_action"] = action
        # 处理最差动作
        elif line.startswith("Node ") and "LLM suggests Worst Action" in line:
            if current_sample:
                action_str = re.search(r'\(([^)]+)\)', line).group(1)
                action = tuple(int(x) for x in action_str.split(', '))
                current_sample["worst_action"] = action
        # 处理空行
        elif line == "":
            if current_sample and current_sample["goal_state"] and \
                    (current_sample["best_action"] or current_sample["worst_action"]):
                samples.append(current_sample)
            current_sample = None

    # 处理最后一个节点
    if current_sample and current_sample["goal_state"] and \
            (current_sample["best_action"] or current_sample["worst_action"]):
        samples.append(current_sample)

    return samples


def apply_amplification(matrix_to_modify, original_reference_matrix, row_index, coefficient):
    """
    辅助函数：对指定矩阵的指定行，根据原始矩阵的值进行特征放大。
    只放大原始值为1（表示存在关系）的特征。
    """
    if 0 <= row_index < matrix_to_modify.shape[0]:
        matrix_to_modify[row_index, :] = np.where(
            original_reference_matrix[row_index, :] == 1,
            coefficient,
            original_reference_matrix[row_index, :]
        )
    else:
        print(f"row_index{row_index} 有误！")


def get_stable_prefix_and_suffixes(current_blocks, goal_blocks):
    """比较当前栈和目标栈，返回稳定前缀、当前问题后缀、未来目标后缀"""
    stable_prefix = []
    len_stable = 0
    # 确保 current_blocks 和 goal_blocks 都是列表类型
    cb = current_blocks if isinstance(current_blocks, list) else []
    gb = goal_blocks if isinstance(goal_blocks, list) else []

    for i in range(min(len(cb), len(gb))):
        if cb[i] == gb[i]:
            stable_prefix.append(cb[i])
            len_stable += 1
        else:
            break

    current_problematic_suffix = cb[len_stable:]
    future_target_suffix = gb[len_stable:]

    return stable_prefix, current_problematic_suffix, future_target_suffix


def generate_sample_matrix(log_samples,
                           current_state_layer_idx=1, # 当前状态在22层中的索引
                           goal_state_layer_idx=0,    # 目标状态在22层中的索引
                           coeffs=None):              # 放大系数的字典
    """
    生成多维矩阵样本，并在当前状态层应用特征放大。
    放大逻辑简化为：高亮优先任务块、当前修复栈中的块、目标中应在但缺失的块。
    不显式编码复杂的堆叠规则。
    """
    # 定义默认的简化放大系数
    if coeffs is None:
        coeffs = {
            "PRIORITY_TASK_BLOCK": 15,
            "OTHER_PROBLEMATIC_BLOCKS": 10,
            "FIX_STACK_STABLE_PART": 7,
            "NEXT_TARGET_TO_ADD": 8,
            "FUTURE_TARGET_AWARENESS_LOW": 3
        }


    temp_planner = GraphPlanningBlocksWorld(log_samples[0]["current_state"], log_samples[0]["goal_state"])

    samples_matrix = np.zeros((len(log_samples), temp_planner.n_layers, 
                               temp_planner.nn_n_rows, temp_planner.nn_n_cols))
    labels = np.full((len(log_samples), 2), -1, dtype=int)  # 初始化为 -1 表示不确定

    all_system_block_names = temp_planner.blocks
    all_system_stack_names = temp_planner.stacks
    
    for i, sample in enumerate(log_samples):
        # 确保当前样本有状态信息
        if not sample.get("current_state") or not sample.get("goal_state"):
            print(f"警告: 样本 {i} (Node {sample.get('node')}) 缺少 current_state 或 goal_state，跳过特征放大和标签生成。")
            # 对于这个样本，samples_matrix[i] 会是全零，labels[i] 会是 [-1, -1]
            continue
        planner = GraphPlanningBlocksWorld(sample["current_state"], sample["goal_state"])

        # 1. 生成原始的 (n_layers, nn_n_rows, nn_n_cols) 矩阵
        raw_layers_matrix = planner._generate_n_layers_matrix()

        original_current_state_matrix = np.copy(raw_layers_matrix[current_state_layer_idx, :, :])
        modified_current_state_matrix = raw_layers_matrix[current_state_layer_idx, :, :]

        current_state_dict = sample.get("current_state")
        goal_state_dict = sample.get("goal_state")
        fix_stack_id = sample.get("fix_stack_id")
        attention_block_name = sample.get("attention_block_name")
        priority_task_desc = sample.get("priority_task_description", "")

        # 使用从 temp_planner 获取的全局 block 列表，或者从当前 planner 获取
        block_to_row_idx = {name: idx for idx, name in
                            enumerate(all_system_block_names)}

        if fix_stack_id is not None:
            fix_stack_key = f"Stack{fix_stack_id}"
            current_blocks_in_fix_stack = current_state_dict.get(fix_stack_key, [])
            goal_blocks_for_fix_stack = goal_state_dict.get(fix_stack_key, [])

            stable_prefix, current_problem_suffix, future_target_suffix = \
                get_stable_prefix_and_suffixes(current_blocks_in_fix_stack, goal_blocks_for_fix_stack)

            # 用一个集合记录已被高优（问题或优先任务）放大的块，避免被低优覆盖
            amplified_as_problem_or_priority = set()

            for block_name in stable_prefix:
                if block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[block_name], coeffs["FIX_STACK_STABLE_PART"])
                    # 稳定块通常不是问题块，但如果它也是优先任务块（不太可能），会被后续覆盖

            task_type = "unknown"
            if "移走" in priority_task_desc:
                task_type = "remove"
            elif "移入" in priority_task_desc:
                task_type = "add"

            if task_type == "remove":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])
                    amplified_as_problem_or_priority.add(attention_block_name)

                for block_name in current_problem_suffix:
                    if block_name != attention_block_name and block_name in block_to_row_idx:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["OTHER_PROBLEMATIC_BLOCKS"])
                        amplified_as_problem_or_priority.add(block_name)

                for block_name in future_target_suffix:
                    if block_name in block_to_row_idx and block_name not in amplified_as_problem_or_priority:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["FUTURE_TARGET_AWARENESS_LOW"])


            elif task_type == "add":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])

                if attention_block_name and future_target_suffix and \
                        attention_block_name == future_target_suffix[0] and \
                        len(future_target_suffix) > 1 and \
                        future_target_suffix[1] in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[future_target_suffix[1]], coeffs["NEXT_TARGET_TO_ADD"])
        # --- 特征放大结束 ---
        samples_matrix[i] = raw_layers_matrix  # 保存已包含修改后当前状态层的矩阵

        successors = planner.get_successors()

        # 如果 LLM 未提供动作，保持 -1
        best_action = sample["best_action"]
        worst_action = sample["worst_action"]
        
        for j, (next_state, action) in enumerate(successors[:planner.n_blocks]):
            if best_action is not None and action == best_action:
                labels[i, 0] = 2 + j # labels记录的是最佳或最差动作的层索引，故需加2
            if worst_action is not None and action == worst_action:
                labels[i, 1] = 2 + j
        
        # 调试信息
        if labels[i, 0] == -1:
            print(f"Sample {i}: Best action '{best_action}' not determined or not found")
            print(sample)
        if labels[i, 1] == -1:
            print(f"Sample {i}: Worst action '{worst_action}' not determined or not found")
            print(sample)
    
    return samples_matrix, labels, temp_planner.blocks, temp_planner.stacks


def save_to_csv(samples_matrix, labels, blocks, stacks, output_prefix="samples"):
    """将样本矩阵和标签保存为CSV文件"""
    n_samples, n_layers, n_rows, n_cols = samples_matrix.shape

    # 生成列名
    column_names = []
    all_cols = blocks + stacks
    for layer in range(n_layers):
        for row in range(n_rows):
            for col in range(n_cols):
                # 层名称
                if layer == 0:
                    layer_name = "goal"
                elif layer == 1:
                    layer_name = "current"
                else:
                    layer_name = f"action_{layer - 2}"
                # 行名称
                if row < len(blocks):
                    row_name = f"on_{blocks[row]}"
                else:  # row == len(blocks)
                    row_name = "clear"
                # 列名称
                col_name = all_cols[col] if col < len(all_cols) else "none"
                column_names.append(f"{layer_name}_{row_name}_{col_name}")

    # 展平矩阵并保存
    flat_matrix = samples_matrix.reshape(n_samples, -1)
    matrix_df = pd.DataFrame(flat_matrix, columns=column_names)
    labels_df = pd.DataFrame(labels, columns=["best_action_idx", "worst_action_idx"])

    matrix_df.to_csv(f"{output_prefix}_matrix.csv", index=False)
    labels_df.to_csv(f"{output_prefix}_labels.csv", index=False)

def process_log_to_matrix(log_content, output_prefix="samples"):
    """主处理函数"""
    log_samples = parse_log(log_content)
    samples_matrix, labels, blocks, stacks = generate_sample_matrix(log_samples)
    save_to_csv(samples_matrix, labels, blocks, stacks, output_prefix)
    return samples_matrix, labels, blocks, stacks

# 示例使用
# if __name__ == "__main__":
#     with open(r"C:\Users\<USER>\PycharmProjects\blocks_world\Pre-Marshalling\Data\Train_data\data.txt", "r", encoding="utf-8") as f:
#         log_content = f.read()
#
#     samples_matrix, labels, blocks, stacks = process_log_to_matrix(log_content,
#             output_prefix=r"C:\Users\<USER>\PycharmProjects\blocks_world\Pre-Marshalling\Data\Train_data\data")
#
#     print("Samples Matrix Shape:", samples_matrix.shape)
#     print("Labels Shape:", labels.shape)
#     print("Blocks:", blocks)
#     print("Tables:", stacks)
#     print("Sample 0, Goal State:\n", samples_matrix[0, 0])
#     print("Sample 0, Current State:\n", samples_matrix[0, 1])
#     print("Sample 0, Labels:", labels[0])
