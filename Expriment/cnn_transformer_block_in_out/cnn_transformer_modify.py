import torch
import torch.nn as nn
import pandas as pd
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import random
from sklearn.model_selection import KFold
from torch.utils.data import random_split
import re
import torch.nn.functional as F # <-- Add this line

class BlocksWorldDataset(Dataset):
    def __init__(self, matrix_file, label_file, orig_shape, new_shape):
        #  (n_samples, 22, 21, 25)
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签并验证范围
        labels = pd.read_csv(label_file).values
        print(f"Labels: min={labels.min()}, max={labels.max()}")

        self.labels = labels

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels


class CNNTransformerClassifier(nn.Module):
    def __init__(self, n_layers=22, n_rows=21, n_cols=25, embed_dim=64, n_heads=8, n_hidden=256, n_classes=20,
                 num_transformer_layers=6, classifier_dropout_rate=0.1):  # 增加分类器 dropout率参数
        super().__init__()
        self.n_layers = n_layers
        self.n_rows = n_rows
        self.n_cols = n_cols
        self.embed_dim = embed_dim
        self.classifier_dropout_rate = classifier_dropout_rate  # 保存dropout率

        self.max_actions_layers = n_layers - 2

        self.cnn = nn.Sequential(
            nn.Conv2d(n_layers, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            nn.MaxPool2d(2)
            # Output shape: (batch_size, 64, 10, 12) calculated from (21,25) input
        )

        # Calculate sequence length after CNN
        with torch.no_grad():
            dummy_input = torch.zeros(1, n_layers, n_rows, n_cols)
            cnn_output_shape = self.cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]  # 64
            self.cnn_out_h = cnn_output_shape[2]  # (n_rows + 1) // 2 = 11 for n_rows=21
            self.cnn_out_w = cnn_output_shape[3]  # (n_cols + 1) // 2 = 13 for n_cols=25
            self.sequence_length = self.cnn_out_h * self.cnn_out_w  # 11 * 13 = 143

        # Linear projection from CNN channel dim to Transformer embed_dim (if needed)
        # If cnn_out_channels == embed_dim, this could potentially be an Identity layer
        self.input_proj = nn.Linear(self.cnn_out_channels, embed_dim)

        # Positional embedding for the sequence
        # Shape: (1, sequence_length, embed_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.sequence_length, embed_dim))

        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim, nhead=n_heads, dim_feedforward=n_hidden, batch_first=True,
            dropout=0.3,  # 提升泛化性
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # 分类头部之前的Dropout层
        self.classifier_dropout = nn.Dropout(p=self.classifier_dropout_rate)

        # Classification heads (using the aggregated output)
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)

        self._initialize_weights()

    def _initialize_weights(self):
        # Initialize positional embedding
        nn.init.normal_(self.pos_embed, std=.02)  # Common initialization for pos embed

        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Slightly different init for Linear layers is common
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            # Note: Initialization within TransformerEncoderLayer might be handled internally
            # or you can customize it further if needed, but PyTorch defaults are often okay.

    def forward(self, x):
        batch_size = x.size(0)

        # 1. Pass through CNN
        x = self.cnn(x)  # Shape: (batch_size, 64, 11, 13)

        # 2. Reshape for Transformer
        x = x.flatten(2)  # Shape: (batch_size, 64, 143)  # 11 * 13 = 143
        x = x.transpose(1, 2)  # Shape: (batch_size, 143, 64)

        # 3. Project to embed_dim
        x = self.input_proj(x)  # Shape: (batch_size, 143, 64)  # embed_dim = 64

        # 4. Add positional embedding
        x = x + self.pos_embed  # Shape: (batch_size, 143, 64)  # pos_embed: (1, 143, 64)

        # 5. Pass through Transformer Encoder
        x = self.transformer(x)  # Shape: (batch_size, 143, 64)

        # 6. Mean pooling
        x = x.mean(dim=1)  # Shape: (batch_size, 64)  # Mean over sequence_length (143)

        # 应用分类头部之前的Dropout
        x = self.classifier_dropout(x)

        # 7. Classification heads
        best_logits = self.fc_best(x)  # Shape: (batch_size, 20)  # n_classes = 20
        worst_logits = self.fc_worst(x)  # Shape: (batch_size, 20)  # n_classes = 20

        return best_logits, worst_logits

    def freeze_cnn_layers(self, freeze_first=True, freeze_second=False):
        # CNN层命名基于 nn.Sequential 的索引:
        # 0: Conv2d, 1: BatchNorm2d, 2: ReLU, 3: Dropout
        # 4: Conv2d, 5: BatchNorm2d, 6: ReLU, 7: Dropout, 8: MaxPool2d
        # 因此第一个Conv2d是 self.cnn[0], 第二个Conv2d是 self.cnn[4]
        for name, param in self.cnn.named_parameters():
            #冻结第一个卷积层 (self.cnn[0])
            if freeze_first and (name.startswith("0.")): # "0.weight" or "0.bias"
                param.requires_grad = False
            #冻结第二个卷积层 (self.cnn[4])
            if freeze_second and (name.startswith("4.")): # "4.weight" or "4.bias"
                param.requires_grad = False

    # --- New evaluate_actions method ---
    # @torch.no_grad() # Ensure no gradients are computed during evaluation
    # def evaluate_actions(self, current_state, goal_state, planner, successors):
    #     """
    #     使用神经网络评估给定状态下的后继动作。

    #     Args:
    #         current_state (State): 当前状态对象。
    #         goal_state (State): 目标状态对象。
    #         planner (GraphPlanningBlocksWorld): 规划器实例，用于访问 state_to_matrix,
    #                                         blocks, tables, n_rows, n_cols 等。
    #         successors (list): 后继状态和动作的列表 [(State, action_str), ...]。

    #     Returns:
    #         dict: 包含预测的最优/最劣动作索引及其置信度的字典。
    #             {
    #                 'best_action_idx': int,
    #                 'worst_action_idx': int,
    #                 'best_confidence': float,
    #                 'worst_confidence': float,
    #                 'best_probs': np.ndarray,  # 可选：所有动作的最佳概率
    #                 'worst_probs': np.ndarray  # 可选：所有动作的最差概率
    #             }
    #             索引对应于 successors 列表。如果无法预测则返回 -1 和 0.0。
    #     """
    #     self.eval()  # Ensure model is in evaluation mode

    #     # 1. Prepare the input tensor using the planner's context
    #     # Input shape expected by model's forward: (1, n_layers, n_rows, n_cols)
    #     n_layers = 2 + self.max_actions_layers
    #     n_rows, n_cols = planner.nn_n_rows, planner.nn_n_cols

    #     # Check if planner has necessary attributes
    #     if not all(hasattr(planner, attr) for attr in ['state_to_matrix', 'nn_n_rows', 'nn_n_cols', 'device']):
    #         print("Error: Planner object missing required attributes for NN input preparation.")
    #         return {
    #             "best_action_idx": -1,
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }

    #     n_input_layers = 2 + self.max_actions_layers
    #     input_tensor_np = np.zeros((1, n_input_layers, planner.nn_n_rows, planner.nn_n_cols), dtype=np.float32)

    #     try:
    #         # Layer 0: Goal State
    #         input_tensor_np[0, 0] = planner.state_to_matrix(goal_state.config)
    #         # Layer 1: Current State
    #         input_tensor_np[0, 1] = planner.state_to_matrix(current_state.config)
    #         # Layer 2 onwards: Successor States (up to max_actions_layers)
    #         num_successors_to_encode = min(len(successors), self.max_actions_layers)
    #         for i in range(num_successors_to_encode):
    #             next_state, _ = successors[i]
    #             input_tensor_np[0, 2 + i] = planner.state_to_matrix(next_state.config)
    #     except Exception as e:
    #         print(f"Error preparing state matrices for NN input: {e}")
    #         return {
    #             "best_action_idx": -1,
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }

    #     # Convert to torch tensor and move to the correct device
    #     nn_input = torch.from_numpy(input_tensor_np).to(planner.device)  # Use planner's device

    #     # 2. Perform inference using the model's forward method
    #     try:
    #         best_logits, worst_logits = self.forward(nn_input)  # Call the model itself
    #     except Exception as e:
    #         print(f"Error during model forward pass in evaluate_actions: {e}")
    #         return {
    #             "best_action_idx": -1,
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }

    #     # 3. Process logits to get indices and probabilities
    #     predicted_best_idx = -1
    #     predicted_worst_idx = -1
    #     best_confidence = 0.0
    #     worst_confidence = 0.0
    #     num_valid_successors = len(successors)  # Actual number of successors generated

    #     # Consider predictions only up to the number of actual successors OR max_actions_layers,
    #     # whichever is smaller, as logits correspond to the first max_actions_layers slots.
    #     num_predictions_possible = min(num_valid_successors, self.max_actions_layers)

    #     if num_predictions_possible > 0:
    #         # Slice logits to match the number of possible predictions
    #         valid_best_logits = best_logits[0, :num_predictions_possible]
    #         valid_worst_logits = worst_logits[0, :num_predictions_possible]

    #         # Compute probabilities using softmax
    #         best_probs = torch.softmax(valid_best_logits, dim=-1)
    #         worst_probs = torch.softmax(valid_worst_logits, dim=-1)

    #         # Get indices
    #         predicted_best_idx = torch.argmax(best_probs).item() # Highest probability is best action
    #         predicted_worst_idx = torch.argmax(worst_probs).item()  # Highest probability is worst action

    #         # Get confidences
    #         best_confidence = best_probs[predicted_best_idx].item()
    #         worst_confidence = worst_probs[predicted_worst_idx].item()

    #     return {
    #         "best_action_idx": predicted_best_idx,
    #         "worst_action_idx": predicted_worst_idx,
    #         "best_confidence": best_confidence,
    #         "worst_confidence": worst_confidence,
    #         "best_probs": best_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([]),  # Optional
    #         "worst_probs": worst_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([])  # Optional
    #     }

     # --------------------------------------------------------------------------
    # >>> 新增的方法，用于处理动态加载 <<<
    # --------------------------------------------------------------------------
    def load_state_dict(self, state_dict,strict=True,orig_n_rows= 21,orig_n_cols= 25):
        """
        自定义的 state_dict 加载器，可以对位置编码和分类头进行自适应处理。
        """
        # --- 1. 处理位置编码 (Positional Embedding) ---
        ckpt_pos_embed = state_dict.get('pos_embed', None)
        if ckpt_pos_embed is not None and self.pos_embed.shape != ckpt_pos_embed.shape:
            print(f"位置编码尺寸不匹配。 Checkpoint: {ckpt_pos_embed.shape}, Model: {self.pos_embed.shape}")
            print("正在尝试对位置编码进行插值...")
            # (这部分代码保持不变...)
            with torch.no_grad():
                device = next(self.parameters()).device
                dummy_cnn = self.cnn
                dummy_input = torch.zeros(1, self.n_layers, orig_n_rows, orig_n_cols).to(device)
                orig_cnn_out_shape = dummy_cnn(dummy_input).shape
                orig_h, orig_w = orig_cnn_out_shape[2], orig_cnn_out_shape[3]
            
            pos_embed_ckpt_2d = ckpt_pos_embed.reshape(1, orig_h, orig_w, self.embed_dim).permute(0, 3, 1, 2)
            new_h, new_w = self.cnn_out_h, self.cnn_out_w
            print(f"正在从 {orig_h}x{orig_w} 插值到 {new_h}x{new_w}")
            resized_pos_embed = F.interpolate(pos_embed_ckpt_2d, size=(new_h, new_w), mode='bicubic', align_corners=False)
            resized_pos_embed = resized_pos_embed.permute(0, 2, 3, 1).flatten(1, 2)
            state_dict['pos_embed'] = resized_pos_embed
        
        # --- 2. 新增：处理分类头 (Classifier Head) ---
        # 获取当前模型和checkpoint中分类头的权重形状
        model_head_shape = self.fc_best.weight.shape
        ckpt_head_shape = state_dict.get('fc_best.weight', None).shape if 'fc_best.weight' in state_dict else None
        
        # 如果形状不匹配，则从 state_dict 中移除分类头的权重，不加载它们
        if ckpt_head_shape is not None and model_head_shape != ckpt_head_shape:
            print(f"分类头尺寸不匹配。 Checkpoint: {ckpt_head_shape[0]} classes, Model: {model_head_shape[0]} classes.")
            print("将不加载预训练的分类头权重，使用新模型的随机初始化权重。")
            # 使用 pop 将这些键从字典中移除
            state_dict.pop('fc_best.weight', None)
            state_dict.pop('fc_best.bias', None)
            state_dict.pop('fc_worst.weight', None)
            state_dict.pop('fc_worst.bias', None)
        
        # --- 3. 最终加载 ---
        # 使用 strict=False 来加载。这会优雅地忽略掉我们手动 pop 掉的键，
        # 以及其他任何可能不匹配的键，而不会报错。
        # 这是迁移学习中的标准做法。
        super().load_state_dict(state_dict, strict=False)

    @torch.no_grad()
    def evaluate_actions(self,
                         current_state,  # 当前状态对象 (包含 .stacks)
                         goal_state,  # 目标状态对象 (包含 .stacks)
                         planner,  # 规划器实例
                         successors,  # 后继状态列表
                         current_fix_stack_name_str,  # 例如 "Stack3"
                         priority_task_description_str,  # 优先任务的完整描述
                         current_state_layer_idx=1,  # !!! 关键：当前状态层在输入张量中的索引
                         goal_state_layer_idx=0,  # !!! 关键：目标状态层在输入张量中的索引
                         coeffs=None,  # 放大系数
                         random_perturb_prob=0.):
        """
        使用神经网络评估给定状态下的后继动作，支持随机扰动以测试框架健壮性。
        Returns:
            dict: 包含预测的最优/最劣动作索引及其置信度的字典。
                {
                    'best_action_idx': int,
                    'worst_action_idx': int,
                    'best_confidence': float,
                    'worst_confidence': float,
                    'best_probs': np.ndarray,  # 可选：所有动作的最佳概率
                    'worst_probs': np.ndarray  # 可选：所有动作的最差概率
                }
                索引对应于 successors 列表。如果无法预测则返回 -1 和 0.0。
        """
        self.eval()  # Ensure model is in evaluation mode

        # --- 定义与训练时一致的放大系数 ---
        if coeffs is None:
            coeffs = {
                "PRIORITY_TASK_BLOCK": 15,
                "OTHER_PROBLEMATIC_BLOCKS": 10,
                "FIX_STACK_STABLE_PART": 7,
                "NEXT_TARGET_TO_ADD": 8,
                "FUTURE_TARGET_AWARENESS_LOW": 3
            }


        if not all(hasattr(planner, attr) for attr in ['state_to_matrix', 'nn_n_rows', 'nn_n_cols', 'device']):
            print("Error: Planner object missing required attributes for NN input preparation.")
            return {
                "best_action_idx": -1,
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }

        n_input_layers = 2 + self.max_actions_layers
        input_tensor_np = np.zeros((1, n_input_layers, planner.nn_n_rows, planner.nn_n_cols), dtype=np.float32)

        try:
            input_tensor_np[0, 0] = planner.state_to_matrix(goal_state.stacks)
            input_tensor_np[0, 1] = planner.state_to_matrix(current_state.stacks)
            num_successors_to_encode = min(len(successors), self.max_actions_layers)
            for i in range(num_successors_to_encode):
                next_state, _ = successors[i]
                input_tensor_np[0, 2 + i] = planner.state_to_matrix(next_state.stacks)
        except Exception as e:
            print(f"Error preparing state matrices for NN input: {e}")
            return {
                "best_action_idx": -1,
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }

        # --- 2. 应用特征放大到当前状态层 ---
        original_current_state_matrix = np.copy(input_tensor_np[0, current_state_layer_idx, :, :])
        modified_current_state_matrix = input_tensor_np[0, current_state_layer_idx, :, :]  # 直接修改

        current_state_dict = current_state.stacks  # 假设 .stacks 返回字典
        goal_state_dict = goal_state.stacks  # 假设 .stacks 返回字典

        fix_stack_id = get_fix_stack_id_from_name(current_fix_stack_name_str)
        attention_block_name = parse_attention_block_from_task_desc(priority_task_description_str)

        # planner.blocks 应该提供所有积木块的列表，顺序与矩阵行对应
        all_block_names = planner.blocks
        block_to_row_idx = {name: idx for idx, name in enumerate(all_block_names)}

        if fix_stack_id is not None and current_state_dict and goal_state_dict:
            fix_stack_key = f"Stack{fix_stack_id}"
            current_blocks_in_fix_stack = current_state_dict.get(fix_stack_key, [])
            goal_blocks_for_fix_stack = goal_state_dict.get(fix_stack_key, [])

            stable_prefix, current_problem_suffix, future_target_suffix = \
                get_stable_prefix_and_suffixes(current_blocks_in_fix_stack, goal_blocks_for_fix_stack)

            amplified_as_problem_or_priority = set()

            for block_name in stable_prefix:
                if block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[block_name], coeffs["FIX_STACK_STABLE_PART"])

            task_type = "unknown"
            if "移走" in priority_task_description_str:
                task_type = "remove"
            elif "移入" in priority_task_description_str:
                task_type = "add"

            if task_type == "remove":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])
                    amplified_as_problem_or_priority.add(attention_block_name)

                for block_name in current_problem_suffix:
                    if block_name != attention_block_name and block_name in block_to_row_idx:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["OTHER_PROBLEMATIC_BLOCKS"])
                        amplified_as_problem_or_priority.add(block_name)

                for block_name in future_target_suffix:
                    if block_name in block_to_row_idx and block_name not in amplified_as_problem_or_priority:
                        apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                            block_to_row_idx[block_name], coeffs["FUTURE_TARGET_AWARENESS_LOW"])

            elif task_type == "add":
                if attention_block_name and attention_block_name in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[attention_block_name], coeffs["PRIORITY_TASK_BLOCK"])

                if attention_block_name and future_target_suffix and \
                        attention_block_name == future_target_suffix[0] and \
                        len(future_target_suffix) > 1 and \
                        future_target_suffix[1] in block_to_row_idx:
                    apply_amplification(modified_current_state_matrix, original_current_state_matrix,
                                        block_to_row_idx[future_target_suffix[1]], coeffs["NEXT_TARGET_TO_ADD"])

        # 特征放大结束，modified_current_state_matrix (即 input_tensor_np[0, current_state_layer_idx, :, :]) 已更新

        nn_input = torch.from_numpy(input_tensor_np).to(planner.device)

        # 2. Perform inference
        try:
            best_logits, worst_logits = self.forward(nn_input)
        except Exception as e:
            print(f"Error during model forward pass in evaluate_actions: {e}")
            return {
                "best_action_idx": -1,
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }

        # 3. Process logits to get indices and probabilities
        predicted_best_idx = -1
        predicted_worst_idx = -1
        best_confidence = 0.0
        worst_confidence = 0.0
        num_valid_successors = len(successors)
        num_predictions_possible = min(num_valid_successors, self.max_actions_layers)

        if num_predictions_possible > 0:
            # Slice logits to match the number of possible predictions
            valid_best_logits = best_logits[0, :num_predictions_possible]
            valid_worst_logits = worst_logits[0, :num_predictions_possible]

            # Compute probabilities using softmax
            best_probs = torch.softmax(valid_best_logits, dim=-1)
            worst_probs = torch.softmax(valid_worst_logits, dim=-1)

            # Get model-predicted indices
            model_best_idx = torch.argmax(best_probs).item()  # Highest probability is best action
            model_worst_idx = torch.argmax(worst_probs).item()  # Highest probability is worst action

            # Get confidences based on model predictions
            best_confidence = best_probs[model_best_idx].item()
            worst_confidence = worst_probs[model_worst_idx].item()

            # Apply random perturbation with specified probability
            if random.random() < random_perturb_prob:
                # Randomly select best action index
                predicted_best_idx = random.randint(0, num_predictions_possible - 1)
                # Ensure worst action is different from best action (optional, to avoid conflicts)
                available_worst_indices = [i for i in range(num_predictions_possible) if i != predicted_best_idx]
                if available_worst_indices:
                    predicted_worst_idx = random.choice(available_worst_indices)
                else:
                    predicted_worst_idx = predicted_best_idx  # Fallback, though unlikely
                print(f"Random perturbation applied: best_idx={predicted_best_idx}, worst_idx={predicted_worst_idx}")
            else:
                # Use model predictions
                predicted_best_idx = model_best_idx
                predicted_worst_idx = model_worst_idx

        return {
            "best_action_idx": predicted_best_idx,
            "worst_action_idx": predicted_worst_idx,
            "best_confidence": best_confidence,
            "worst_confidence": worst_confidence,
            "best_probs": best_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([]),
            "worst_probs": worst_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([])
        }

def apply_amplification(matrix_to_modify, original_reference_matrix, row_index, coefficient):
    """辅助函数：对指定矩阵的指定行，根据原始矩阵的值进行特征放大。"""
    if 0 <= row_index < matrix_to_modify.shape[0]:
        matrix_to_modify[row_index, :] = np.where(
            original_reference_matrix[row_index, :] == 1,
            coefficient,
            original_reference_matrix[row_index, :]
        )

def get_stable_prefix_and_suffixes(current_blocks, goal_blocks):
    """比较当前栈和目标栈，返回稳定前缀、当前问题后缀、未来目标后缀"""
    stable_prefix = []
    len_stable = 0
    cb = current_blocks if isinstance(current_blocks, list) else []
    gb = goal_blocks if isinstance(goal_blocks, list) else []

    for i in range(min(len(cb), len(gb))):
        if cb[i] == gb[i]:
            stable_prefix.append(cb[i])
            len_stable += 1
        else:
            break

    current_problematic_suffix = cb[len_stable:]
    future_target_suffix = gb[len_stable:]

    return stable_prefix, current_problematic_suffix, future_target_suffix

def parse_attention_block_from_task_desc(task_description):
    """从任务描述中解析关注块名称"""
    if not task_description:
        return None
    attention_block_name = None
    match_move_to = re.search(r"将(\w+)移入", task_description)
    if match_move_to:
        attention_block_name = match_move_to.group(1)
    else:
        match_remove_top = re.search(r"移走Stack\d+顶部错误块(\w+)", task_description)
        if match_remove_top:
            attention_block_name = match_remove_top.group(1)
        else:
            match_move_next = re.search(r"移入目标状态的下一块(\w+)", task_description)
            if match_move_next:
                attention_block_name = match_move_next.group(1)
    return attention_block_name

def get_fix_stack_id_from_name(fix_stack_name_str):
    """从 'StackN' 格式的字符串中提取数字ID N"""
    if not fix_stack_name_str:
        return None
    match = re.search(r'Stack(\d+)', fix_stack_name_str)
    if match:
        return int(match.group(1))
    return None


def train_model(model,matrix_file,label_file, epochs=10, batch_size=32, lr=1e-3,weight_decay=1e-4,
                model_path="cnn_transformer_model.pth", orig_shape=(22, 21, 25), new_shape=(22, 21, 25),
                val_split_ratio=0.1, patience=5, label_smoothing_epsilon=0.1):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape=orig_shape, new_shape=new_shape)
    # 划分训练集和验证集
    val_size = int(len(dataset) * val_split_ratio)
    train_size = len(dataset) - val_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)  # 验证集不需要打乱

    # # 初始化模型
    # # n_classes是输出分类数目，应为矩阵行数减1（clear）占据的行
    # model = CNNTransformerClassifier(n_layers=new_shape[0],
    #                                  n_rows=new_shape[1],
    #                                  n_cols=new_shape[2],
    #                                  embed_dim=new_shape[3],
    #                                  classifier_dropout_rate=classifier_dropout_rate,
    #                                  n_classes=new_shape[1] - 1).to(device)
    model = model.to(device)

    # 检查是否存在已有模型文件，若存在则加载
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"Loaded existing model from {model_path} for incremental training")
    else:
        print(f"No existing model found at {model_path}, starting training from scratch")

    # 使用标签平滑的损失函数
    criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing_epsilon)
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=patience // 2, factor=0.5,
                                                               verbose=True)
    # 早停相关变量
    best_val_loss = float('inf')
    epochs_no_improve = 0
    best_model_state_dict = model.state_dict()  # 初始化为当前模型状态

    for epoch in range(epochs):
        model.train() # 设置为训练模式
        total_train_loss = 0
        total_train_batches = 0
        for batch_matrix, batch_labels in train_dataloader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device) # 标签形状 (batch_size, 2)

            optimizer.zero_grad()
            best_logits, worst_logits = model(batch_matrix) # (batch, n_classes), (batch, n_classes)

            # 标签有效性掩码，-1表示无效标签
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1

            loss = 0
            current_loss_value = 0 # 用于累计当前批次的损失值

            if best_mask.any():
                # 标签减2使其从0开始索引，与n_classes匹配
                loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                loss += loss_best
                current_loss_value += loss_best.item()
            if worst_mask.any():
                # 标签减2使其从0开始索引
                loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                loss += loss_worst
                current_loss_value += loss_worst.item()

            if loss != 0: # 只有当存在有效标签时才反向传播
                loss.backward()
                optimizer.step()
                total_train_loss += current_loss_value # 使用加权后的损失值
                total_train_batches += 1

        avg_train_loss = total_train_loss / total_train_batches if total_train_batches > 0 else 0
        print(f"Epoch {epoch + 1}/{epochs}, Training Loss: {avg_train_loss:.4f}")

        # --- 验证阶段 ---
        model.eval()  # 设置为评估模式
        total_val_loss = 0
        total_val_batches = 0
        with torch.no_grad():
            for batch_matrix, batch_labels in val_dataloader:
                batch_matrix = batch_matrix.to(device)
                batch_labels = batch_labels.to(device)

                best_logits, worst_logits = model(batch_matrix)

                best_mask = batch_labels[:, 0] != -1
                worst_mask = batch_labels[:, 1] != -1

                loss = 0
                current_loss_value = 0
                if best_mask.any():
                    loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                    loss += loss_best
                    current_loss_value += loss_best.item()
                if worst_mask.any():
                    loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                    loss += loss_worst
                    current_loss_value += loss_worst.item()

                if loss != 0:
                    total_val_loss += current_loss_value
                    total_val_batches += 1

        avg_val_loss = total_val_loss / total_val_batches if total_val_batches > 0 else float('inf')
        print(f"Epoch {epoch + 1}/{epochs}, Validation Loss: {avg_val_loss:.4f}")

        # 更新学习率
        scheduler.step(avg_val_loss)

        # 早停判断
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            epochs_no_improve = 0
            best_model_state_dict = model.state_dict()  # 保存表现最好的模型状态
            print(f"Validation loss improved. Saving model state.")
        else:
            epochs_no_improve += 1
            print(f"Validation loss did not improve for {epochs_no_improve} epoch(s).")

        if epochs_no_improve >= patience:
            print(f"Early stopping triggered after {epoch + 1} epochs.")
            model.load_state_dict(best_model_state_dict)  # 加载最佳模型状态
            break
    # 如果没有触发早停，或者早停时当前模型就是最好的，则保存当前（或最佳）模型
    if not (epochs_no_improve >= patience):  # 如果不是因为早停结束的，或者早停时当前就是最优
        best_model_state_dict = model.state_dict()  # 确保保存的是最后一轮（如果它更好或早停没触发）

    torch.save(best_model_state_dict, model_path)
    print(f"Model saved to {model_path}")

    # 加载回最佳模型用于返回（如果早停，确保返回的是最佳的）
    model.load_state_dict(best_model_state_dict)
    return model


def evaluate_model(model, matrix_file, label_file, orig_shape=(22, 21, 25), new_shape=(22, 21, 25), batch_size=32):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)  # 确保模型在正确的设备上
    model.eval()

    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape=orig_shape, new_shape=new_shape)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)

    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0

    with torch.no_grad():
        for batch_matrix, batch_labels in dataloader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)

            best_logits, worst_logits = model(batch_matrix)
            best_preds = torch.argmax(best_logits, dim=1)
            worst_preds = torch.argmax(worst_logits, dim=1)

            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1

            if best_mask.any():
                true_best = batch_labels[best_mask, 0] - 2
                correct_best += (best_preds[best_mask] == true_best).sum().item()
                total_best += best_mask.sum().item()

            if worst_mask.any():
                true_worst = batch_labels[worst_mask, 1] - 2
                correct_worst += (worst_preds[worst_mask] == true_worst).sum().item()
                total_worst += worst_mask.sum().item()

    acc_best = correct_best / total_best if total_best > 0 else 0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0
    print(f"Best Action Accuracy: {acc_best:.4f} ({correct_best}/{total_best})")
    print(f"Worst Action Accuracy: {acc_worst:.4f} ({correct_worst}/{total_worst})")


def train_model_with_kfold(matrix_file, label_file, shape=(22, 21, 25), n_folds=5, epochs=500,
                           batch_size=32, lr=1e-3, patience=10, model_path="cnn_transformer_model.pth"):
    """
    使用n折交叉验证和早停机制训练模型。

    Args:
        matrix_file (str): 输入矩阵文件路径。
        label_file (str): 标签文件路径。
        shape (tuple): 输入矩阵的形状 (n_layers, n_rows, n_cols)。
        n_folds (int): 交叉验证的折数。
        epochs (int): 最大训练轮数。
        batch_size (int): 批大小。
        lr (float): 学习率。
        patience (int): 早停的耐心值（验证损失不再下降的轮数）。
        model_path (str): 模型保存路径。

    Returns:
        list: 每折的最佳模型和其验证准确率。
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 加载完整数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, shape, shape)

    # 初始化 KFold
    kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)
    fold_results = []

    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        print(f"\nFold {fold + 1}/{n_folds}")

        # 创建训练和验证子集
        train_subset = torch.utils.data.Subset(dataset, train_idx)
        val_subset = torch.utils.data.Subset(dataset, val_idx)

        train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)

        # 初始化模型
        model = CNNTransformerClassifier(
            n_layers=shape[0],
            n_rows=shape[1],
            n_cols=shape[2],
            n_classes=shape[1] - 1
        ).to(device)

        # 检查是否加载已有模型
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            print(f"Loaded existing model from {model_path} for fold {fold + 1}")
        else:
            print(f"Starting training from scratch for fold {fold + 1}")

        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)

        # 早停参数
        best_val_loss = float('inf')
        epochs_no_improve = 0
        best_model_state = None

        for epoch in range(epochs):
            # 训练阶段
            model.train()
            total_train_loss = 0
            total_batches = 0

            for batch_matrix, batch_labels in train_loader:
                batch_matrix = batch_matrix.to(device)
                batch_labels = batch_labels.to(device)

                optimizer.zero_grad()
                best_logits, worst_logits = model(batch_matrix)

                best_mask = batch_labels[:, 0] != -1
                worst_mask = batch_labels[:, 1] != -1

                loss = 0
                if best_mask.any():
                    loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                    loss += loss_best
                if worst_mask.any():
                    loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                    loss += loss_worst

                if loss != 0:
                    loss.backward()
                    optimizer.step()
                    total_train_loss += loss.item()
                    total_batches += 1

            avg_train_loss = total_train_loss / total_batches if total_batches > 0 else 0

            # 验证阶段
            model.eval()
            total_val_loss = 0
            total_val_batches = 0
            correct_best = 0
            correct_worst = 0
            total_best = 0
            total_worst = 0

            with torch.no_grad():
                for batch_matrix, batch_labels in val_loader:
                    batch_matrix = batch_matrix.to(device)
                    batch_labels = batch_labels.to(device)

                    best_logits, worst_logits = model(batch_matrix)

                    best_mask = batch_labels[:, 0] != -1
                    worst_mask = batch_labels[:, 1] != -1

                    val_loss = 0
                    if best_mask.any():
                        val_loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                        val_loss += val_loss_best
                        true_best = batch_labels[best_mask, 0] - 2
                        best_preds = torch.argmax(best_logits[best_mask], dim=1)
                        correct_best += (best_preds == true_best).sum().item()
                        total_best += best_mask.sum().item()
                    if worst_mask.any():
                        val_loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                        val_loss += val_loss_worst
                        true_worst = batch_labels[worst_mask, 1] - 2
                        worst_preds = torch.argmax(worst_logits[worst_mask], dim=1)
                        correct_worst += (worst_preds == true_worst).sum().item()
                        total_worst += worst_mask.sum().item()

                    if val_loss != 0:
                        total_val_loss += val_loss.item()
                        total_val_batches += 1

            avg_val_loss = total_val_loss / total_val_batches if total_val_batches > 0 else 0
            acc_best = correct_best / total_best if total_best > 0 else 0
            acc_worst = correct_worst / total_worst if total_worst > 0 else 0

            print(f"Epoch {epoch + 1}/{epochs}, Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"Best Acc: {acc_best:.4f}, Worst Acc: {acc_worst:.4f}")

            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                best_model_state = model.state_dict()
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1

            if epochs_no_improve >= patience:
                print(f"Early stopping triggered after {epoch + 1} epochs")
                break

        # 保存该折的最佳模型
        fold_model_path = f"{model_path}_fold_{fold + 1}.pth"
        torch.save(best_model_state, fold_model_path)
        print(f"Best model for fold {fold + 1} saved to {fold_model_path}")

        # 记录该折的结果
        fold_results.append({
            'fold': fold + 1,
            'model_path': fold_model_path,
            'val_loss': best_val_loss,
            'best_acc': acc_best,
            'worst_acc': acc_worst
        })

    # 打印交叉验证结果
    print("\nCross-Validation Results:")
    for result in fold_results:
        print(f"Fold {result['fold']}: Val Loss = {result['val_loss']:.4f}, "
              f"Best Acc = {result['best_acc']:.4f}, Worst Acc = {result['worst_acc']:.4f}")

    # 返回所有折的结果
    return fold_results


def prepare_data(data, orig_shape, new_shape):
    batch_size = data.shape[0]
    new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
    new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data  # data: (batch_size, 22, 21, 25)
    return new_data

# if __name__ == "__main__":
# # 生成样本
# with open("astar_with_llm_2025-04-01-195134.log", "r", encoding="utf-8") as f:
#     log_content = f.read()
# process_log_to_matrix(log_content)

# # 训练和评估
# matrix_file = "data/samples0417_sorted_matrix.csv"
# label_file = "data/samples0417_sorted_labels.csv"
# shape = (17,18,17)
# model_path="model/cnn_transformer_model_dual_stack.pth"
# lr = 1e-4 if os.path.exists(model_path) else 3e-4
# model = train_model(matrix_file, label_file, epochs=100, batch_size=32, lr=lr,
#                     model_path=model_path, shape=shape)
# evaluate_model(model, matrix_file, label_file, shape=shape, batch_size=32)

# # 进行5折交叉验证训练
# matrix_file = "data/samples0422_matrix.csv"
# label_file = "data/samples0422_labels.csv"
# shape = (17,18,17)
# model_path="cnn_transformer_model_dual_staccks.pth"
# lr = 1e-4 if os.path.exists(model_path) else 3e-4
#
# fold_results = train_model_with_kfold(
#     matrix_file=matrix_file,
#     label_file=label_file,
#     shape=shape,
#     n_folds=5,
#     epochs=100,
#     batch_size=32,
#     lr=lr,
#     patience=10,
#     model_path=model_path
# )
#
# # 评估每个折的模型
# device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# for result in fold_results:
#     print(f"\nEvaluating model from fold {result['fold']}")
#     model = CNNTransformerClassifier(
#         n_layers=shape[0],
#         n_rows=shape[1],
#         n_cols=shape[2],
#         n_classes=shape[1]-3
#     ).to(device)
#     model.load_state_dict(torch.load(result['model_path'], map_location=device))
#     evaluate_model(model, matrix_file, label_file, orig_shape=shape, new_shape=shape, batch_size=32)

# # 扩展学习设计
# # 训练和评估
# matrix_file = "data/samples0422_sorted_matrix.csv"
# label_file = "data/samples0422_sorted_labels.csv"
# # orig_shape = (12,13,11)
# orig_shape = (17,18,17)
# new_shape = (17,18,17)
# model_path="model/cnn_transformer_model_15_blocks_dual_staccks.pth"
# lr = 1e-4 if os.path.exists(model_path) else 3e-4
# model = train_model(matrix_file, label_file, epochs=100, batch_size=32, lr=lr,
#                     model_path=model_path, orig_shape=orig_shape, new_shape=new_shape)
# evaluate_model(model, matrix_file, label_file, orig_shape=orig_shape, new_shape=new_shape, batch_size=32)
