import heapq
import time
from datetime import datetime

import heapq
import time
from datetime import datetime
from pre_marshalling_llm import LLMGuidance

class State:
    def __init__(self, stacks, current_fix_stack, g=0, h=0, parent=None, action=None):
        self.stacks = stacks  # 栈字典
        self.current_fix_stack = current_fix_stack  # 当前修复栈
        self.g = g  # 到当前状态的实际代价
        self.h = h  # 估计剩余代价
        self.cost = g + h  # 总代价
        self.parent = parent  # 父状态
        self.action = action  # 导致该状态的动作
        self.h_adjusted = None  # 调整后的启发式值

    def __lt__(self, other):
        return self.cost < other.cost

    def __eq__(self, other):
        if not isinstance(other, State):
            return False
        # 仅比较 stacks，不比较 current_fix_stack
        return tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())) == \
               tuple(sorted((k, tuple(v)) for k, v in other.stacks.items()))

    def __hash__(self):
        # 仅基于 stacks 计算哈希值
        return hash(tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())))

    def __str__(self):
        return str(self.stacks)

class GraphPlanningBlocksWorld:
    def __init__(self, start_state, goal_state, fix_order, log_file="astar_log_llm.txt"):
        self.state = State(start_state, fix_order[0])  # 初始状态指定第一个修复栈
        self.goal = State(goal_state, None)  # 目标状态无需修复栈
        self.log_file = log_file
        self.check = []
        self.fix_order = fix_order  # 保存修复顺序

    def is_goal(self, state):
        return state.stacks == self.goal.stacks

    def get_successors(self):
        successors = []
        stack_names = list(self.state.stacks.keys())
        for i in range(len(stack_names)):
            for j in range(len(stack_names)):
                if i != j and self.state.stacks[stack_names[i]]:
                    new_stacks = {k: v.copy() for k, v in self.state.stacks.items()}
                    block = new_stacks[stack_names[i]].pop()
                    new_stacks[stack_names[j]].append(block)
                    # 更新 current_fix_stack
                    new_fix_stack = self.update_fix_stack(new_stacks)
                    new_state = State(new_stacks, new_fix_stack)
                    action = (i + 1, j + 1)
                    successors.append((new_state, action))
        return successors

    def update_fix_stack(self, stacks):
        """根据当前栈状态更新 current_fix_stack"""
        for stack_name in self.fix_order:
            if not self.is_stack_fixed(stack_name, stacks, self.goal.stacks):
                return stack_name
        return None  # 所有栈都修复完成

    def is_stack_fixed(self, stack_name, current_stacks, goal_stacks):
        current_stack = current_stacks.get(stack_name, [])
        goal_stack = goal_stacks.get(stack_name, [])
        # 检查当前栈是否至少包含目标栈的全部块，且顺序从底部开始匹配
        if len(current_stack) < len(goal_stack):
            return False
        for i in range(len(goal_stack)):
            if current_stack[i] != goal_stack[i]:
                return False
        return True

    def heuristic(self, state):
        cost = 0
        goal_on_block = {}
        for stack_name, blocks in self.goal.stacks.items():
            for i in range(len(blocks)):
                if i == 0:
                    goal_on_block[blocks[i]] = stack_name
                else:
                    goal_on_block[blocks[i]] = blocks[i-1]
        current_on_block = {}
        for stack_name, blocks in state.stacks.items():
            for i in range(len(blocks)):
                if i == 0:
                    current_on_block[blocks[i]] = stack_name
                else:
                    current_on_block[blocks[i]] = blocks[i-1]
        for block, target in goal_on_block.items():
            current_pos = current_on_block.get(block)
            if current_pos != target:
                cost += 1
        return cost

    def log(self, message):
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')

    def a_star_search(self, llm=None):
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h

        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []

        open(self.log_file, 'a', encoding='utf-8').close()

        if llm is None:
            self.log("运行 A* 搜索（无 LLM）：")
        else:
            self.log(f"运行 A* 搜索（使用 LLM {llm.model}）：")

        while queue:

            # # 收集当前队列中的所有路径和 cost
            # queue_items = []
            # for item in queue:
            #     cost, _, state, path = item
            #     queue_items.append((path, cost))
            #
            # # 按 cost 升序排序
            # queue_items.sort(key=lambda x: x[1])
            #
            # # 记录到日志文件
            # self.log(f"================ 扩展节点 {count} ================")
            # for p, c in queue_items:
            #     self.log(f"path: {p} — cost: {c}")
            # self.log("==============================================")

            _, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if len(self.check) < count:
                self.check.append(True)



            if self.is_goal(current_state):
                self.log(f"找到解决方案：{path}")
                self.log(f"搜索节点数：{count}")
                self.log(f"路径长度：{len(path)}\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            h_n_adjusted = current_state.h_adjusted if current_state.h_adjusted is not None else current_state.h

            if self.is_goal(current_state):
                self.log(f"Solution found: {path}")
                self.log(f"Nodes searched: {count}")
                self.log(f"Path length: {len(path)}\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            h_n_adjusted = current_state.h_adjusted if current_state.h_adjusted is not None else current_state.h

            if llm is not None:
                llm_result, actions, current_issue, priority = llm.evaluate_actions(current_state, self.goal, self,
                                                                                    successors)
                self.log(
                    f"\nNode {count}: Current fix stack: **{current_state.current_fix_stack}** Current issue: {current_issue} Priority task: {priority}")
                self.log(f"Node {count}: Current state: {current_state.stacks}")
                self.log(f"Node {count}: Goal state: {self.goal.stacks}")
                actions = [action for _, action in successors]
                # self.log(f"Node {count}: Current valid actions: {actions}")

                best_action = llm_result["best_action"]
                best_reason = llm_result["best_reason"]
                worst_action = llm_result["worst_action"]
                worst_reason = llm_result["worst_reason"]

                for i, (next_state, action) in enumerate(successors):
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    h_original = next_state.h
                    if best_action != "uncertain" and i == best_action - 1:
                        next_state.g -= 1
                        next_state.h_adjusted = h_original * 0.667
                        next_state.cost = next_state.g + next_state.h_adjusted
                        self.log(
                            f"Node {count}: LLM suggests Best Action '{action}'")
                        self.log(f"Best Reason: {best_reason}")
                    elif worst_action != "uncertain" and i == worst_action - 1:
                        next_state.h_adjusted = h_original * 1.5
                        next_state.cost = next_state.g + next_state.h_adjusted
                        self.log(
                            f"Node {count}: LLM suggests Worst Action '{action}'")
                        self.log(f"Worst Reason: {worst_reason}")
                    else:
                        next_state.h_adjusted = h_original
                        next_state.cost = next_state.g + next_state.h_adjusted

                # action_cost_list = [
                #     f"'{action}': cost={next_state.cost}, h_orig={next_state.h}, h_adj={next_state.h_adjusted}"
                #     for next_state, action in successors
                # ]
                # formatted_list = f"[{', '.join(action_cost_list)}]"
                # self.log(f"节点 {count}：调整后的动作代价：{formatted_list}")
            else:
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.h_adjusted = next_state.h
                    next_state.cost = next_state.g + next_state.h

            for next_state, action in successors:
                h_m_adjusted = next_state.h_adjusted
                if h_n_adjusted > 1 + h_m_adjusted:
                    self.check[count - 1] = False
                    self.log(f"节点 {count} 检测到不一致："
                             f"动作={action}==={h_n_adjusted} > 1 + {h_m_adjusted}")

            for next_state, action in successors:
                next_state.parent = current_state
                next_state.action = action
                heapq.heappush(queue, (next_state.cost, count, next_state, path + [action]))

        self.log("未找到解决方案\n")
        return None, -1
start_state_1 = {'Stack1': ['H', 'A', 'C'], 'Stack2': ['E', 'F'], 'Stack3': ['I', 'G', 'B', 'D']}
goal_state_1 = {
    "Stack1": ["B","H"],
    "Stack2": ["C","D","E"],
    "Stack3": ["A","F","G","I"],
}

# log_file='qwen3_test.txt'
# # start = time.time()
# # planner = GraphPlanningBlocksWorld(start_state_1, goal_state_1)
# # solution, nodes_count = planner.a_star_search(llm=None)
# # print("Without LLM - Time consumption:", time.time() - start, "seconds")
# # print("Without LLM - Solution found:", solution)
# # print("Without LLM - Nodes searched:", nodes_count)
#
# llm = LLMGuidance()
# start = time.time()
# planner = GraphPlanningBlocksWorld(start_state_1, goal_state_1,list(start_state_1.keys()),log_file=log_file)
# solution, nodes_count = planner.a_star_search(llm=llm)
# print("With LLM - Time consumption:", time.time() - start, "seconds")
# print("With LLM - Solution found:", solution)
# print("With LLM - Nodes searched:", nodes_count)
# import time
#
#
# #
# log_file='qwq.txt'
#
#
# llm = LLMGuidance()
# start = time.time()
# planner = GraphPlanningBlocksWorld(start_state_1, goal_state_1,['Stack2','Stack1','Stack3'],log_file=log_file)
# solution, nodes_count = planner.a_star_search(llm=llm)
# print("With LLM - Time consumption:", time.time() - start, "seconds")
# print("With LLM - Solution found:", solution)
# print("With LLM - Nodes searched:", nodes_count)
