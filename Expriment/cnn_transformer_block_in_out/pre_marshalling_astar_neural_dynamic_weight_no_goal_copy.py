# -*- coding: utf-8 -*-

import torch
import heapq
import time
from datetime import datetime
# 假设 cnn_transformer_modify.py 在同一目录下或在 sys.path 中
from cnn_transformer_modify import CNNTransformerClassifier 
import numpy as np
from pre_marshalling_llm import LLMGuidance,get_priority_task

class State:
    def __init__(self, stacks, current_fix_stack, g=0, h=0, parent=None, action=None):
        self.stacks = stacks  # 栈字典
        self.current_fix_stack = current_fix_stack  # 当前修复栈
        self.g = g  # 到当前状态的实际代价
        self.h = h  # 估计剩余代价
        self.cost = g + h  # 总代价
        self.parent = parent  # 父状态
        self.action = action  # 导致该状态的动作
        self.h_adjusted = None  # 调整后的启发式值

    def __lt__(self, other):
        return self.cost < other.cost

    def __eq__(self, other):
        if not isinstance(other, State):
            return False
        # 仅比较 stacks，不比较 current_fix_stack
        return tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())) == \
               tuple(sorted((k, tuple(v)) for k, v in other.stacks.items()))

    def __hash__(self):
        # 仅基于 stacks 计算哈希值
        return hash(tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())))

    def __str__(self):
        return str(self.stacks)

class GraphPlanningBlocksWorld:
    """
    规划器类。
    关键修改:
    - __init__ 方法不再接收 model_path，而是直接接收一个预先加载好的 model 对象。
    - 不再由自身在初始化时负责加载模型，提高了复用性。
    """
    # --- 修改点 1: 构造函数签名 ---
    # 不再需要 model_path，直接接收 model 对象
    def __init__(self, start_state, goal_state, fix_order, log_file=None, model=None, ppo_model_path=None):
        self.state = State(start_state, fix_order[0])  # 初始状态指定第一个修复栈
        self.goal = State(goal_state, None)  # 目标状态无需修复栈
        self.log_file = log_file
        self.check = []
        self.fix_order = fix_order  # 保存修复顺序

        # Neural network related attributes
        self.blocks, self.stacks = self._get_blocks_and_stacks(start_state)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.nn_n_rows = len(self.blocks) + 1
        self.nn_n_cols = len(self.blocks) + len(self.stacks)
        self.n_blocks = len(self.blocks)
        self.n_stacks = len(self.stacks)
        self.n_layers = 22 # 假设此值固定或根据需要调整

        # --- 修改点 2: 模型赋值 ---
        # 直接使用传入的 model 对象
        self.model = model

        # self.ppo_model = None
        # if ppo_model_path:
        #     self.load_ppo_model(ppo_model_path)

        self._successors_cache = {}
        
    def state_to_matrix(self, state):
        """将状态转换为矩阵形式，仅包含on-block和clear信息"""
        n_blocks = len(self.blocks)
        n_stacks = len(self.stacks)
        matrix = np.zeros((n_blocks + 1, n_blocks + n_stacks))

        for stack_name, stack in state.items():
            if not stack:
                continue
            stack_idx = self.stacks.index(stack_name) + n_blocks
            bottom_block = stack[0]
            bottom_idx = self.blocks.index(bottom_block)
            matrix[bottom_idx, stack_idx] = 1
            for i in range(1, len(stack)):
                current_block = stack[i]
                below_block = stack[i - 1]
                current_idx = self.blocks.index(current_block)
                below_idx = self.blocks.index(below_block)
                matrix[current_idx, below_idx] = 1
            top_block = stack[-1]
            top_idx = self.blocks.index(top_block)
            matrix[n_blocks, top_idx] = 1
        return matrix

    def _generate_n_layers_matrix(self):
        """生成多层矩阵，作为CNN输入和RL Obs的依据"""
        n_layers_matrix = np.zeros((self.n_layers, self.nn_n_rows, self.nn_n_cols), dtype=np.float32)
        n_layers_matrix[0] = self.state_to_matrix(self.goal.stacks)
        n_layers_matrix[1] = self.state_to_matrix(self.state.stacks)
        successors = self.get_successors()
        for i, (next_state, action) in enumerate(successors[:len(self.stacks)*(len(self.stacks)-1)]):
            n_layers_matrix[2 + i] = self.state_to_matrix(next_state.stacks)
        return n_layers_matrix

    def _get_blocks_and_stacks(self, state):
        """从状态中提取块和栈列表"""
        stacks = sorted(state.keys())
        blocks = set()
        for stack in state.values():
            blocks.update(stack)
        blocks = sorted(list(blocks))
        return blocks, stacks
        
    # 注意: load_model 方法的逻辑已移至主脚本。
    # 此方法在这里不再被 __init__ 调用，但保留它以备不时之需或用于调试。
    def load_model(self, model_path, orig_n_rows=21, orig_n_cols=25):
        try:
            print(f"为当前问题实例化模型。尺寸 (L,R,C,Classes): ({self.n_layers}, {self.nn_n_rows}, {self.nn_n_cols}, {len(self.blocks)})")
            self.model = CNNTransformerClassifier(
                n_layers=self.n_layers,
                n_rows=self.nn_n_rows,
                n_cols=self.nn_n_cols,
                n_classes=len(self.blocks),
                embed_dim=64,
                n_heads=4,
                n_hidden=256,
                num_transformer_layers=6,
                classifier_dropout_rate=0.1
            ).to(self.device)
            checkpoint = torch.load(model_path, map_location=self.device)
            print(f"加载预训练模型... 原始训练尺寸: R={orig_n_rows}, C={orig_n_cols}")
            self.model.load_state_dict(
                checkpoint,
                orig_n_rows=orig_n_rows,
                orig_n_cols=orig_n_cols
            )
            self.model.eval()
            self.log(f"成功从 {model_path} 加载并自适应调整了 CNN+Transformer 模型。")
        except Exception as e:
            self.log(f"加载模型时发生错误: {e}")
            import traceback
            traceback.print_exc()
            self.model = None

    def is_goal(self, state):
        """检查当前状态是否是 CPMP 问题的一个最终解。"""
        for stack in state.stacks.values():
            if len(stack) <= 1:
                continue
            for i in range(len(stack) - 1):
                if stack[i] < stack[i+1]:
                    return False
        return True

    def get_successors(self):
        # ... (此方法代码不变) ...
        successors = []
        stack_names = list(self.state.stacks.keys())
        for i in range(len(stack_names)):
            for j in range(len(stack_names)):
                if i != j and self.state.stacks[stack_names[i]]:
                    new_stacks = {k: v.copy() for k, v in self.state.stacks.items()}
                    block = new_stacks[stack_names[i]].pop()
                    new_stacks[stack_names[j]].append(block)
                    new_fix_stack = self.update_fix_stack(new_stacks)
                    new_state = State(new_stacks, new_fix_stack)
                    action = (i + 1, j + 1)
                    successors.append((new_state, action))
        return successors

    def update_fix_stack(self, stacks):
        # ... (此方法代码不变) ...
        for stack_name in self.fix_order:
            if not self.is_stack_fixed(stack_name, stacks, self.goal.stacks):
                return stack_name
        return None

    def is_stack_fixed(self, stack_name, current_stacks, goal_stacks):
        # ... (此方法代码不变) ...
        current_stack = current_stacks.get(stack_name, [])
        goal_stack = goal_stacks.get(stack_name, [])
        if len(current_stack) < len(goal_stack):
            return False
        for i in range(len(goal_stack)):
            if current_stack[i] != goal_stack[i]:
                return False
        return True

    def heuristic(self, state):
        # ... (此方法代码不变) ...
        cost = 0
        for stack in state.stacks.values():
            if len(stack) <= 1:
                continue
            for i in range(len(stack)):
                for j in range(i + 1, len(stack)):
                    if stack[i] < stack[j]:
                        cost += 1
        return cost

    def log(self, message):
        # ... (此方法代码不变) ...
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')

    def calculate_dynamic_weights(self, parent_h, child_h, child_successors, best_confidence, worst_confidence):
        # ... (此方法代码不变) ...
        k_min_threshold = 0.2
        k_max_threshold = 5
        confidence_threshold_high = 0.8
        confidence_threshold_low = 0.5
        delta = 0.01
        if child_h == 0:
            return 1.0, 1.0
        if parent_h is not None and child_h > 0:
            k_min_candidate = max(0, (parent_h - 1) / child_h)
        child_h_values = [self.heuristic(s[0]) for s in child_successors]
        if child_h_values:
            k_max_candidate = min([round((1 + h_child) / child_h, 3) for h_child in child_h_values])
        if best_confidence > confidence_threshold_high:
            k_best = k_min_threshold
        elif best_confidence < confidence_threshold_low:
            k_best = 1
        else:
            k_best = k_min_candidate + delta
        if worst_confidence > confidence_threshold_high:
            k_worst = k_max_threshold
        elif best_confidence < confidence_threshold_low:
            k_worst = 1
        else:
            k_worst = k_max_candidate - delta
        return k_best, k_worst

    def a_star_search(self, llm=None, max_iterations=100000, is_consistency=False):
        # ... (此方法代码不变，它会直接使用 self.model，而 self.model 是从外部传入的) ...
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h

        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []

        open(self.log_file, 'a', encoding='utf-8').close()

        if llm is None and self.model is None:
            self.log("Running A* without guidance:")
        elif self.model is not None:
            self.log("Running A* with CNN+Transformer model:")
        else:
            self.log(f"Running A* with LLM {llm.model}:")

        while queue:
            if count >= max_iterations:
                self.log(f"搜索达到最大迭代次数 {max_iterations}，未找到解决方案。")
                self.log(f"已搜索节点数：{count}")
                return None, count

            _, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if len(self.check) < count:
                self.check.append(True)

            if self.is_goal(current_state):
                self.log(f"Solution found: {path}")
                self.log(f"Nodes searched: {count}")
                self.log(f"Path length: {len(path)}\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()
            h_n_adjusted = current_state.h_adjusted if current_state.h_adjusted is not None else current_state.h

            if self.model is not None or llm is not None:
                if self.model is not None:
                    fix_stack_curr = current_state.stacks[current_state.current_fix_stack]
                    fix_stack_goal = self.goal.stacks.get(current_state.current_fix_stack, [])
                    priority=get_priority_task(current_state.current_fix_stack,fix_stack_curr,fix_stack_goal)
                    nn_result = self.model.evaluate_actions(current_state,self.goal,self,successors,current_state.current_fix_stack, priority)
                    best_action_idx = nn_result["best_action_idx"]
                    worst_action_idx = nn_result["worst_action_idx"]
                    best_confidence = nn_result["best_confidence"]
                    worst_confidence = nn_result["worst_confidence"]
                else: # llm is not None
                    # ... llm logic ...
                    pass 

                for i, (next_state, action) in enumerate(successors):
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    h_original = next_state.h
                    
                    original_state = self.state
                    self.state = next_state
                    next_successors = self.get_successors()
                    self.state = original_state

                    if is_consistency:
                        k_best, k_worst = self.calculate_dynamic_weights(h_n_adjusted, next_state.h, next_successors, best_confidence=nn_result["best_confidence"], worst_confidence=nn_result["worst_confidence"])
                    else:
                        k_best, k_worst=0.667,1

                    if best_action_idx != -1 and i == best_action_idx:
                        next_state.g -= 1
                        next_state.h_adjusted = h_original * k_best
                        next_state.cost = next_state.g + next_state.h_adjusted
                    elif worst_action_idx != -1 and i == worst_action_idx:
                        next_state.h_adjusted = h_original * k_worst
                        next_state.cost = next_state.g + next_state.h_adjusted
                    else:
                        next_state.h_adjusted = h_original
                        next_state.cost = next_state.g + next_state.h_adjusted
            else: # Pure A*
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.h_adjusted = next_state.h
                    next_state.cost = next_state.g + next_state.h
            
            for next_state, action in successors:
                next_state.parent = current_state
                next_state.action = action
                heapq.heappush(queue, (next_state.cost, count, next_state, path + [action]))

        self.log("未找到解决方案\n")
        return None, -1