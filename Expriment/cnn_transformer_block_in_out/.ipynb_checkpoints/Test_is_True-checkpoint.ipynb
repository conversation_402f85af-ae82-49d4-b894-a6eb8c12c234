{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fc327744-0ca7-49ed-9121-16a9a9a75456", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/envs/pre_marshalling/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pre_marshalling_astar_neural_dynamic_weights import GraphPlanningBlocksWorld\n", "from pre_marshalling_llm import LLMGuidance\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "id": "463f1f55-d6ad-4407-9d06-64c4c78dc0f5", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Running permutation 1/24: ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 445.8534231185913 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 2/24: ['Stack4', 'Stack1', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 436.9290192127228 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 3/24: ['Stack4', 'Stack2', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 437.467524766922 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 4/24: ['Stack4', 'Stack2', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 440.0894899368286 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 5/24: ['Stack4', 'Stack3', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 435.22261357307434 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 6/24: ['Stack4', 'Stack3', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 434.776978969574 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 7/24: ['Stack1', 'Stack4', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 436.3761684894562 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 8/24: ['Stack1', 'Stack4', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 432.0599956512451 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 9/24: ['Stack1', 'Stack2', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 438.18624806404114 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 10/24: ['Stack1', 'Stack2', 'Stack3', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 430.8322949409485 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 11/24: ['Stack1', 'Stack3', 'Stack4', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 434.41586804389954 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 12/24: ['Stack1', 'Stack3', 'Stack2', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 432.8919131755829 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 13/24: ['Stack2', 'Stack4', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 440.3376359939575 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 14/24: ['Stack2', 'Stack4', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 444.9611032009125 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 15/24: ['Stack2', 'Stack1', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "KeyboardInterrupt\n", "\n"]}], "source": ["#is_consistency=True k_best 0.1\n", "\n", "import time\n", "from itertools import permutations\n", "\n", "# Define the initial and goal states\n", "# Define the initial and goal states\n", "start_state = {\n", "    'Stack1': ['M', 'G', 'T', 'B', 'K'],\n", "    'Stack2': ['J', 'E', 'H', 'S', 'P'],\n", "    'Stack3': ['L', 'C', 'F', 'I', 'N'],\n", "    'Stack4': ['D', 'O', 'Q', 'A', 'R'],\n", "    'Stack5': []\n", "}\n", "goal_state = {\n", "    'Stack1': ['L', 'M', 'Q', 'T', 'R'],\n", "    'Stack2': ['J', 'A', 'K', 'H', 'O'],\n", "    'Stack3': ['C', 'D', 'P', 'G', 'F'],\n", "    'Stack4': ['S', 'E', 'I', 'B', 'N'],\n", "    'Stack5': []\n", "}\n", "stack_order = ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "model_path = \"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'trained_model_test.txt'\n", "\n", "# Generate all permutations of stack_order\n", "stack_permutations = list(permutations(stack_order))\n", "\n", "# Loop through each permutation\n", "for idx, order in enumerate(stack_permutations, 1):\n", "    print(f\"\\nRunning permutation {idx}/{len(stack_permutations)}: {list(order)}\")\n", "    start = time.time()\n", "    \n", "    # Initialize the planner with the current stack order\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state, list(order), model_path=model_path, log_file=log_file)\n", "    \n", "    # Run A* search\n", "    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000, is_consistency=True)\n", "    \n", "    # Print results\n", "    print(\"Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Solution found:\", solution)\n", "    print(\"Nodes searched:\", nodes_count)\n", "    print(\"Path.Length:\", 0 if solution is None else len(solution))"]}, {"cell_type": "code", "execution_count": 4, "id": "d15f154c-663d-4a69-a087-95c436f39520", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Running permutation 1/6: ['Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 326.7627580165863 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 2/6: ['Stack1', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 329.18635630607605 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 3/6: ['Stack2', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 330.9144892692566 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 4/6: ['Stack2', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 322.15880250930786 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 5/6: ['Stack3', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 327.8972225189209 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n", "\n", "Running permutation 6/6: ['Stack3', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 11, 14, 10)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "位置编码尺寸不匹配。 Checkpoint: torch.<PERSON><PERSON>([1, 120, 64]), Model: torch.<PERSON><PERSON>([1, 35, 64])\n", "正在尝试对位置编码进行插值...\n", "正在从 10x12 插值到 5x7\n", "分类头尺寸不匹配。 Checkpoint: 20 classes, Model: 10 classes.\n", "将不加载预训练的分类头权重，使用新模型的随机初始化权重。\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "搜索达到最大迭代次数 100000，未找到解决方案。\n", "已搜索节点数：100000\n", "Time consumption: 330.1150748729706 seconds\n", "Solution found: None\n", "Nodes searched: 100000\n", "Path.Length: 0\n"]}], "source": ["#is_consistency=True k_best 0.1\n", "\n", "import time\n", "from itertools import permutations\n", "\n", "# Define the initial and goal states\n", "# Define the initial and goal states\n", "start_state={'Stack1': ['I', 'G'],\n", "  'Stack2': ['J', 'A', 'B', 'E'],\n", "  'Stack3': ['F', 'D', 'H', 'C'],\n", "  'Stack4': []}\n", "goal_state={'Stack1': ['J', 'I', 'H', 'B'],\n", "  'Stack2': ['A', 'G'],\n", "  'Stack3': ['F', 'C', 'D', 'E'],\n", "  'Stack4': []}\n", "stack_order = ['Stack1', 'Stack2', 'Stack3']\n", "model_path = \"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'trained_model_test.txt'\n", "\n", "# Generate all permutations of stack_order\n", "stack_permutations = list(permutations(stack_order))\n", "\n", "# Loop through each permutation\n", "for idx, order in enumerate(stack_permutations, 1):\n", "    print(f\"\\nRunning permutation {idx}/{len(stack_permutations)}: {list(order)}\")\n", "    start = time.time()\n", "    \n", "    # Initialize the planner with the current stack order\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state, list(order), model_path=model_path, log_file=log_file)\n", "    \n", "    # Run A* search\n", "    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000, is_consistency=True)\n", "    \n", "    # Print results\n", "    print(\"Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Solution found:\", solution)\n", "    print(\"Nodes searched:\", nodes_count)\n", "    print(\"Path.Length:\", 0 if solution is None else len(solution))"]}, {"cell_type": "code", "execution_count": null, "id": "3bf700a3-95a1-4121-9d2a-aa0de0052a58", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Running permutation 1/24: ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (5, 3), (5, 1), (5, 2), (5, 4), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (4, 1), (5, 3), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (5, 1), (5, 2), (5, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 3), (1, 3), (1, 5), (1, 3), (5, 3)]\n", "Nodes searched: 1112\n", "Path length: 62\n", "\n", "Time consumption: 8.985775709152222 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (5, 3), (5, 1), (5, 2), (5, 4), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (4, 1), (5, 3), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (5, 1), (5, 2), (5, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 3), (1, 3), (1, 5), (1, 3), (5, 3)]\n", "Nodes searched: 1112\n", "Path.Length: 62\n", "\n", "Running permutation 2/24: ['Stack4', 'Stack1', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (5, 3), (5, 1), (5, 2), (5, 4), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (4, 1), (5, 3), (5, 1), (2, 1), (3, 5), (2, 3), (2, 3), (2, 4), (1, 5), (2, 1), (5, 1), (2, 5), (2, 3), (4, 3), (5, 1), (5, 2), (1, 5), (5, 2), (1, 5), (1, 2), (5, 2)]\n", "Nodes searched: 1480\n", "Path length: 60\n", "\n", "Time consumption: 10.806867599487305 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (5, 3), (5, 1), (5, 2), (5, 4), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (4, 1), (5, 3), (5, 1), (2, 1), (3, 5), (2, 3), (2, 3), (2, 4), (1, 5), (2, 1), (5, 1), (2, 5), (2, 3), (4, 3), (5, 1), (5, 2), (1, 5), (5, 2), (1, 5), (1, 2), (5, 2)]\n", "Nodes searched: 1480\n", "Path.Length: 60\n", "\n", "Running permutation 3/24: ['Stack4', 'Stack2', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (3, 5), (3, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 2), (3, 2), (5, 3), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (4, 5), (4, 1), (5, 1), (5, 2), (3, 2), (5, 3), (5, 2), (5, 2), (5, 2), (5, 1), (3, 1), (5, 1), (2, 3), (2, 3), (2, 3), (2, 5), (2, 3), (5, 3)]\n", "Nodes searched: 1182\n", "Path length: 62\n", "\n", "Time consumption: 8.31545376777649 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (3, 5), (3, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 2), (3, 2), (5, 3), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (4, 5), (4, 1), (5, 1), (5, 2), (3, 2), (5, 3), (5, 2), (5, 2), (5, 2), (5, 1), (3, 1), (5, 1), (2, 3), (2, 3), (2, 3), (2, 5), (2, 3), (5, 3)]\n", "Nodes searched: 1182\n", "Path.Length: 62\n", "\n", "Running permutation 4/24: ['Stack4', 'Stack2', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (3, 5), (3, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 2), (3, 2), (5, 3), (5, 1), (5, 1), (5, 2), (3, 5), (5, 1), (5, 3), (1, 2), (1, 3), (1, 3), (1, 5), (1, 3), (2, 3), (1, 5), (4, 1), (5, 1), (5, 2), (5, 1), (2, 1), (5, 1)]\n", "Nodes searched: 317\n", "Path length: 55\n", "\n", "Time consumption: 2.2108452320098877 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (3, 5), (3, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 2), (3, 2), (5, 3), (5, 1), (5, 1), (5, 2), (3, 5), (5, 1), (5, 3), (1, 2), (1, 3), (1, 3), (1, 5), (1, 3), (2, 3), (1, 5), (4, 1), (5, 1), (5, 2), (5, 1), (2, 1), (5, 1)]\n", "Nodes searched: 317\n", "Path.Length: 55\n", "\n", "Running permutation 5/24: ['Stack4', 'Stack3', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 2), (3, 4), (1, 5), (1, 5), (1, 4), (2, 4), (3, 5), (3, 5), (3, 5), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (5, 1), (5, 1), (5, 2), (5, 4), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (1, 5), (1, 5), (1, 4), (1, 3), (5, 1), (5, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (4, 1), (1, 5), (4, 1), (5, 1), (4, 1)]\n", "Nodes searched: 49830\n", "Path length: 52\n", "\n", "Time consumption: 367.1866087913513 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 2), (3, 4), (1, 5), (1, 5), (1, 4), (2, 4), (3, 5), (3, 5), (3, 5), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (5, 1), (5, 1), (5, 2), (5, 4), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (1, 5), (1, 5), (1, 4), (1, 3), (5, 1), (5, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (4, 1), (1, 5), (4, 1), (5, 1), (4, 1)]\n", "Nodes searched: 49830\n", "Path.Length: 52\n", "\n", "Running permutation 6/24: ['Stack4', 'Stack3', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 2), (3, 4), (1, 5), (1, 5), (1, 4), (2, 4), (3, 5), (3, 5), (3, 5), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (5, 1), (5, 1), (5, 2), (5, 4), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (1, 5), (1, 5), (1, 4), (1, 3), (5, 1), (5, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (4, 1), (1, 5), (4, 1), (5, 1), (4, 1)]\n", "Nodes searched: 49830\n", "Path length: 52\n", "\n", "Time consumption: 362.74457263946533 seconds\n", "Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 2), (3, 4), (1, 5), (1, 5), (1, 4), (2, 4), (3, 5), (3, 5), (3, 5), (5, 1), (5, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (5, 1), (5, 1), (5, 2), (5, 4), (1, 4), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (1, 5), (1, 5), (1, 4), (1, 3), (5, 1), (5, 3), (1, 5), (1, 5), (5, 2), (5, 1), (2, 1), (4, 1), (1, 5), (4, 1), (5, 1), (4, 1)]\n", "Nodes searched: 49830\n", "Path.Length: 52\n", "\n", "Running permutation 7/24: ['Stack1', 'Stack4', 'Stack2', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (3, 5), (4, 3), (4, 3), (4, 1), (2, 1), (4, 5), (3, 5), (3, 1), (2, 5), (2, 4), (4, 5), (4, 5), (2, 4), (5, 1), (5, 1), (5, 1), (2, 1), (2, 4), (3, 4), (5, 2), (5, 1), (5, 1), (5, 4), (1, 4), (5, 2), (1, 5), (1, 2), (5, 2), (3, 2), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (2, 3)]\n", "Nodes searched: 9692\n", "Path length: 52\n", "\n", "Time consumption: 59.23725986480713 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (3, 5), (4, 3), (4, 3), (4, 1), (2, 1), (4, 5), (3, 5), (3, 1), (2, 5), (2, 4), (4, 5), (4, 5), (2, 4), (5, 1), (5, 1), (5, 1), (2, 1), (2, 4), (3, 4), (5, 2), (5, 1), (5, 1), (5, 4), (1, 4), (5, 2), (1, 5), (1, 2), (5, 2), (3, 2), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (2, 3)]\n", "Nodes searched: 9692\n", "Path.Length: 52\n", "\n", "Running permutation 8/24: ['Stack1', 'Stack4', 'Stack3', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (3, 5), (4, 3), (4, 3), (4, 1), (2, 1), (4, 5), (3, 5), (3, 1), (2, 5), (2, 4), (4, 5), (4, 5), (2, 4), (5, 1), (5, 1), (5, 1), (2, 1), (2, 4), (3, 4), (5, 2), (5, 1), (5, 1), (5, 4), (1, 4), (5, 2), (3, 5), (1, 5), (1, 2), (5, 2), (5, 4), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (4, 3)]\n", "Nodes searched: 9825\n", "Path length: 53\n", "\n", "Time consumption: 63.04652810096741 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (3, 5), (4, 3), (4, 3), (4, 1), (2, 1), (4, 5), (3, 5), (3, 1), (2, 5), (2, 4), (4, 5), (4, 5), (2, 4), (5, 1), (5, 1), (5, 1), (2, 1), (2, 4), (3, 4), (5, 2), (5, 1), (5, 1), (5, 4), (1, 4), (5, 2), (3, 5), (1, 5), (1, 2), (5, 2), (5, 4), (1, 5), (1, 5), (1, 3), (5, 3), (5, 3), (4, 3)]\n", "Nodes searched: 9825\n", "Path.Length: 53\n", "\n", "Running permutation 9/24: ['Stack1', 'Stack2', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 3), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (4, 1), (4, 5), (4, 2), (1, 5), (4, 5), (1, 5), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (3, 5), (3, 5), (1, 3), (1, 3), (5, 1), (5, 3), (1, 3)]\n", "Nodes searched: 13015\n", "Path length: 73\n", "\n", "Time consumption: 93.38584089279175 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 3), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (4, 1), (4, 5), (4, 2), (1, 5), (4, 5), (1, 5), (1, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (3, 5), (3, 5), (1, 3), (1, 3), (5, 1), (5, 3), (1, 3)]\n", "Nodes searched: 13015\n", "Path.Length: 73\n", "\n", "Running permutation 10/24: ['Stack1', 'Stack2', 'Stack3', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 3), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (4, 1), (4, 5), (4, 2), (3, 5), (3, 5), (4, 3), (1, 2), (1, 3), (5, 4), (5, 3), (4, 3), (1, 4), (1, 4), (5, 1), (5, 4), (1, 4), (2, 4)]\n", "Nodes searched: 13009\n", "Path length: 68\n", "\n", "Time consumption: 92.70282888412476 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 3), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (4, 1), (4, 5), (4, 2), (3, 5), (3, 5), (4, 3), (1, 2), (1, 3), (5, 4), (5, 3), (4, 3), (1, 4), (1, 4), (5, 1), (5, 4), (1, 4), (2, 4)]\n", "Nodes searched: 13009\n", "Path.Length: 68\n", "\n", "Running permutation 11/24: ['Stack1', 'Stack3', 'Stack4', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (3, 2), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 1), (5, 1), (2, 1), (2, 1), (2, 3), (1, 3), (1, 3), (4, 5), (2, 4), (5, 1), (5, 1), (2, 5), (2, 4), (1, 4), (1, 5), (1, 5), (1, 4), (5, 4), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (5, 2), (1, 2), (1, 2)]\n", "Nodes searched: 1963\n", "Path length: 62\n", "\n", "Time consumption: 12.712430477142334 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (3, 2), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 1), (5, 1), (2, 1), (2, 1), (2, 3), (1, 3), (1, 3), (4, 5), (2, 4), (5, 1), (5, 1), (2, 5), (2, 4), (1, 4), (1, 5), (1, 5), (1, 4), (5, 4), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (5, 2), (1, 2), (1, 2)]\n", "Nodes searched: 1963\n", "Path.Length: 62\n", "\n", "Running permutation 12/24: ['Stack1', 'Stack3', 'Stack2', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (3, 2), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 1), (5, 1), (2, 1), (2, 1), (2, 3), (1, 3), (1, 3), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (4, 1), (5, 4), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (1, 4), (5, 4), (1, 5), (1, 4), (5, 4)]\n", "Nodes searched: 1976\n", "Path length: 63\n", "\n", "Time consumption: 13.063899278640747 seconds\n", "Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 2), (4, 2), (5, 2), (5, 1), (4, 1), (1, 5), (1, 5), (4, 1), (2, 4), (3, 4), (3, 5), (2, 5), (2, 1), (5, 1), (3, 2), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 1), (5, 1), (2, 1), (2, 1), (2, 3), (1, 3), (1, 3), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (4, 1), (5, 4), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 5), (1, 2), (5, 2), (1, 4), (5, 4), (1, 5), (1, 4), (5, 4)]\n", "Nodes searched: 1976\n", "Path.Length: 63\n", "\n", "Running permutation 13/24: ['Stack2', 'Stack4', 'Stack1', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 1), (5, 1), (4, 1), (4, 2), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 5), (1, 4), (3, 4), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 3)]\n", "Nodes searched: 3193\n", "Path length: 58\n", "\n", "Time consumption: 22.260414361953735 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 1), (5, 1), (4, 1), (4, 2), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 5), (1, 4), (3, 4), (1, 5), (1, 5), (1, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 3)]\n", "Nodes searched: 3193\n", "Path.Length: 58\n", "\n", "Running permutation 14/24: ['Stack2', 'Stack4', 'Stack3', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 1), (5, 1), (4, 1), (4, 2), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 5), (1, 4), (3, 4), (1, 3), (5, 3), (1, 3), (3, 5), (3, 5), (3, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 3), (5, 3)]\n", "Nodes searched: 3321\n", "Path length: 63\n", "\n", "Time consumption: 22.833157777786255 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 1), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 1), (5, 1), (4, 1), (4, 2), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 5), (1, 4), (3, 4), (1, 3), (5, 3), (1, 3), (3, 5), (3, 5), (3, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 3), (5, 3)]\n", "Nodes searched: 3321\n", "Path.Length: 63\n", "\n", "Running permutation 15/24: ['Stack2', 'Stack1', 'Stack4', 'Stack3']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (5, 3), (5, 1), (3, 1), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 4), (3, 4), (1, 3), (5, 1), (5, 3), (2, 3), (1, 3)]\n", "Nodes searched: 1499\n", "Path length: 55\n", "\n", "Time consumption: 10.560304403305054 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (5, 3), (5, 1), (3, 1), (5, 2), (5, 2), (5, 1), (2, 1), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 4), (3, 4), (1, 3), (5, 1), (5, 3), (2, 3), (1, 3)]\n", "Nodes searched: 1499\n", "Path.Length: 55\n", "\n", "Running permutation 16/24: ['Stack2', 'Stack1', 'Stack3', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (5, 3), (5, 1), (3, 1), (5, 2), (5, 2), (5, 1), (2, 1), (3, 5), (4, 3), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (4, 5), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 4), (5, 4)]\n", "Nodes searched: 861\n", "Path length: 62\n", "\n", "Time consumption: 6.197592258453369 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (5, 3), (5, 1), (3, 1), (5, 2), (5, 2), (5, 1), (2, 1), (3, 5), (4, 3), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (4, 5), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (1, 4), (5, 4)]\n", "Nodes searched: 861\n", "Path.Length: 62\n", "\n", "Running permutation 17/24: ['Stack2', 'Stack3', 'Stack4', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (3, 5), (4, 5), (5, 3), (5, 2), (5, 4), (5, 1), (4, 1), (5, 2), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (4, 2), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 4), (2, 1), (2, 4)]\n", "Nodes searched: 1266\n", "Path length: 63\n", "\n", "Time consumption: 9.191447257995605 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (3, 5), (4, 5), (5, 3), (5, 2), (5, 4), (5, 1), (4, 1), (5, 2), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (4, 2), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 4), (2, 1), (2, 4)]\n", "Nodes searched: 1266\n", "Path.Length: 63\n", "\n", "Running permutation 18/24: ['Stack2', 'Stack3', 'Stack1', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (3, 5), (4, 5), (5, 3), (5, 2), (5, 4), (5, 1), (4, 1), (5, 2), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (4, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 4), (2, 4)]\n", "Nodes searched: 1261\n", "Path length: 62\n", "\n", "Time consumption: 8.924801588058472 seconds\n", "Solution found: [(2, 5), (2, 5), (2, 1), (2, 5), (4, 1), (4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (3, 4), (1, 5), (1, 3), (3, 2), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (1, 5), (1, 5), (4, 1), (4, 5), (4, 2), (3, 5), (4, 5), (5, 3), (5, 2), (5, 4), (5, 1), (4, 1), (5, 2), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (4, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 4), (2, 4)]\n", "Nodes searched: 1261\n", "Path.Length: 62\n", "\n", "Running permutation 19/24: ['Stack3', 'Stack4', 'Stack1', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 1), (2, 3), (1, 3), (1, 5), (1, 2), (1, 2), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (2, 5), (2, 4), (5, 4), (5, 4), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 2), (5, 3), (5, 1), (3, 1), (1, 5), (3, 1), (3, 1), (5, 1), (3, 2), (2, 5), (3, 5), (3, 2), (5, 2), (5, 2)]\n", "Nodes searched: 84413\n", "Path length: 84\n", "\n", "Time consumption: 596.3766286373138 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 1), (2, 3), (1, 3), (1, 5), (1, 2), (1, 2), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (2, 5), (2, 4), (5, 4), (5, 4), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 2), (5, 3), (5, 1), (3, 1), (1, 5), (3, 1), (3, 1), (5, 1), (3, 2), (2, 5), (3, 5), (3, 2), (5, 2), (5, 2)]\n", "Nodes searched: 84413\n", "Path.Length: 84\n", "\n", "Running permutation 20/24: ['Stack3', 'Stack4', 'Stack2', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 1), (2, 3), (1, 3), (1, 5), (1, 2), (1, 2), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (2, 5), (2, 4), (5, 4), (5, 4), (5, 4), (1, 3), (1, 5), (1, 2), (1, 3), (1, 3), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (3, 2), (3, 5), (3, 1), (5, 1), (2, 1), (5, 1)]\n", "Nodes searched: 84735\n", "Path length: 73\n", "\n", "Time consumption: 589.6476011276245 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 1), (2, 3), (1, 3), (1, 5), (1, 2), (1, 2), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (2, 5), (2, 4), (5, 4), (5, 4), (5, 4), (1, 3), (1, 5), (1, 2), (1, 3), (1, 3), (1, 5), (1, 5), (1, 2), (5, 2), (5, 2), (3, 2), (3, 5), (3, 1), (5, 1), (2, 1), (5, 1)]\n", "Nodes searched: 84735\n", "Path.Length: 73\n", "\n", "Running permutation 21/24: ['Stack3', 'Stack1', 'Stack4', 'Stack2']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 4), (3, 5), (3, 5), (3, 1), (3, 1), (2, 3), (5, 3), (5, 1), (2, 4), (2, 1), (4, 5), (4, 5), (2, 4), (3, 2), (3, 5), (3, 4), (5, 4), (5, 4), (3, 2), (1, 2), (5, 2)]\n", "Nodes searched: 57567\n", "Path length: 83\n", "\n", "Time consumption: 406.24219131469727 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 4), (3, 5), (3, 5), (3, 1), (3, 1), (2, 3), (5, 3), (5, 1), (2, 4), (2, 1), (4, 5), (4, 5), (2, 4), (3, 2), (3, 5), (3, 4), (5, 4), (5, 4), (3, 2), (1, 2), (5, 2)]\n", "Nodes searched: 57567\n", "Path.Length: 83\n", "\n", "Running permutation 22/24: ['Stack3', 'Stack1', 'Stack2', 'Stack4']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 1), (3, 5), (1, 5), (5, 2), (3, 1), (1, 5), (3, 1), (3, 1), (5, 3), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (3, 5), (3, 2), (5, 1), (5, 1), (5, 2), (5, 2), (1, 4), (1, 4), (5, 4), (5, 4)]\n", "Nodes searched: 55872\n", "Path length: 88\n", "\n", "Time consumption: 392.96163845062256 seconds\n", "Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 3), (4, 1), (4, 5), (5, 1), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (2, 3), (2, 4), (5, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 1), (3, 5), (1, 5), (5, 2), (3, 1), (1, 5), (3, 1), (3, 1), (5, 3), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (3, 5), (3, 2), (5, 1), (5, 1), (5, 2), (5, 2), (1, 4), (1, 4), (5, 4), (5, 4)]\n", "Nodes searched: 55872\n", "Path.Length: 88\n", "\n", "Running permutation 23/24: ['Stack3', 'Stack2', 'Stack4', 'Stack1']\n", "为当前问题实例化模型。尺寸 (L,R,C,Classes): (22, 21, 25, 20)\n", "加载预训练模型... 原始训练尺寸: R=21, C=25\n", "成功从 /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。\n", "Running A* with CNN+Transformer model:\n"]}], "source": ["#is_consistency=True  calculate_dynamic_weights 置信度\n", "import time\n", "from itertools import permutations\n", "\n", "# Define the initial and goal states\n", "start_state = {\n", "    'Stack1': ['M', 'G', 'T', 'B', 'K'],\n", "    'Stack2': ['J', 'E', 'H', 'S', 'P'],\n", "    'Stack3': ['L', 'C', 'F', 'I', 'N'],\n", "    'Stack4': ['D', 'O', 'Q', 'A', 'R'],\n", "    'Stack5': []\n", "}\n", "goal_state = {\n", "    'Stack1': ['L', 'M', 'Q', 'T', 'R'],\n", "    'Stack2': ['J', 'A', 'K', 'H', 'O'],\n", "    'Stack3': ['C', 'D', 'P', 'G', 'F'],\n", "    'Stack4': ['S', 'E', 'I', 'B', 'N'],\n", "    'Stack5': []\n", "}\n", "stack_order = ['Stack4', 'Stack1', 'Stack2', 'Stack3']\n", "model_path = \"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'trained_model_test.txt'\n", "\n", "# Generate all permutations of stack_order\n", "stack_permutations = list(permutations(stack_order))\n", "\n", "# Loop through each permutation\n", "for idx, order in enumerate(stack_permutations, 1):\n", "    print(f\"\\nRunning permutation {idx}/{len(stack_permutations)}: {list(order)}\")\n", "    start = time.time()\n", "    \n", "    # Initialize the planner with the current stack order\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state, list(order), model_path=model_path, log_file=log_file)\n", "    \n", "    # Run A* search\n", "    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000, is_consistency=True)\n", "    \n", "    # Print results\n", "    print(\"Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Solution found:\", solution)\n", "    print(\"Nodes searched:\", nodes_count)\n", "    print(\"Path.Length:\", 0 if solution is None else len(solution))"]}, {"cell_type": "code", "execution_count": null, "id": "cf30519b-d7bb-4405-91d1-1268c7625e02", "metadata": {}, "outputs": [], "source": ["#is_consistency=True  calculate_dynamic_weights 置信度\n", "import time\n", "from itertools import permutations\n", "\n", "# Define the initial and goal states\n", "start_state={'Stack1': ['I', 'G'],\n", "  'Stack2': ['J', 'A', 'B', 'E'],\n", "  'Stack3': ['F', 'D', 'H', 'C'],\n", "  'Stack4': []}\n", "goal_state={'Stack1': ['J', 'I', 'H', 'B'],\n", "  'Stack2': ['A', 'G'],\n", "  'Stack3': ['F', 'C', 'D', 'E'],\n", "  'Stack4': []}\n", "stack_order = ['Stack1', 'Stack2', 'Stack3']\n", "model_path = \"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "log_file = 'trained_model_test.txt'\n", "\n", "# Generate all permutations of stack_order\n", "stack_permutations = list(permutations(stack_order))\n", "\n", "# Loop through each permutation\n", "for idx, order in enumerate(stack_permutations, 1):\n", "    print(f\"\\nRunning permutation {idx}/{len(stack_permutations)}: {list(order)}\")\n", "    start = time.time()\n", "    \n", "    # Initialize the planner with the current stack order\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state, list(order), model_path=model_path, log_file=log_file)\n", "    \n", "    # Run A* search\n", "    solution, nodes_count = planner.a_star_search(llm=None, max_iterations=100000, is_consistency=True)\n", "    \n", "    # Print results\n", "    print(\"Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Solution found:\", solution)\n", "    print(\"Nodes searched:\", nodes_count)\n", "    print(\"Path.Length:\", 0 if solution is None else len(solution))"]}, {"cell_type": "code", "execution_count": null, "id": "c7a4ec92-9600-42e0-ba15-cd68d0beea63", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "pre_marshalling"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}