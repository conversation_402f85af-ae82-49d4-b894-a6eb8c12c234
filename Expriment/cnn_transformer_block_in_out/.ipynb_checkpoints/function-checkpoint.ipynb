{"cells": [{"cell_type": "code", "execution_count": 1, "id": "900b908a-8ede-467b-975d-ead740c2268c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Samples Matrix Shape: (30802, 22, 21, 25)\n", "Labels Shape: (30802, 2)\n", "\n", "Blocks: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T']\n", "Tables: ['Stack1', 'Stack2', 'Stack3', 'Stack4', 'Stack5']\n", "Sample 0, Goal State:\n", " [[0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1.\n", "  0.]\n", " [0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 1. 0. 1. 0. 1. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]]\n", "Sample 0, Current State:\n", " [[ 0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  3.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0. 15.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  1.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0. 10.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  1.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  3.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  3.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "  10.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  7.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  3.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0. 10.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  1.\n", "   0.  1.  0.  0.  0.  0.  0.]]\n", "Sample 0, Labels: [13  3]\n"]}], "source": ["from generate_samples_from_log import process_log_to_matrix\n", "\n", "with open(r\"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    log_content = f.read()\n", "\n", "samples_matrix, labels,blocks, stacks = process_log_to_matrix(log_content,\n", "        output_prefix=r\"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data\")\n", "\n", "print(\"Samples Matrix Shape:\", samples_matrix.shape)\n", "print(\"Labels Shape:\", labels.shape)\n", "print()\n", "print(\"Blocks:\", blocks)\n", "print(\"Tables:\", stacks)\n", "print(\"Sample 0, Goal State:\\n\", samples_matrix[0, 0])\n", "print(\"Sample 0, Current State:\\n\", samples_matrix[0, 1])\n", "print(\"Sample 0, Labels:\", labels[0])"]}, {"cell_type": "code", "execution_count": 2, "id": "59dcb519-4a58-440f-ac71-d2a726b205f3", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Samples Matrix Shape: (4472, 22, 21, 25)\n", "Labels Shape: (4472, 2)\n", "\n", "Blocks: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T']\n", "Tables: ['Stack1', 'Stack2', 'Stack3', 'Stack4', 'Stack5']\n", "Sample 0, Goal State:\n", " [[0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0.\n", "  0.]\n", " [1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0.\n", "  0.]\n", " [0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]\n", " [0. 1. 1. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 0.\n", "  0.]]\n", "Sample 0, Current State:\n", " [[ 0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  1.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  1.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0. 10.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0. 10.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  1.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0. 10.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0. 10.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 3.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  3.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0. 15.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.  0.  0.  0.  3.  0.  0.  0.  0.  0.  0.  0.  0.  0.\n", "   0.  0.  0.  0.  0.  0.  0.]\n", " [ 0.  0.  1.  0.  0.  0.  0.  0.  0.  1.  0.  0.  0.  0.  0.  0.  1.  0.\n", "   1.  0.  0.  0.  0.  0.  0.]]\n", "Sample 0, Labels: [5 6]\n"]}], "source": ["from generate_samples_from_log import process_log_to_matrix\n", "\n", "with open(r\"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    log_content = f.read()\n", "\n", "samples_matrix, labels,blocks, stacks = process_log_to_matrix(log_content,\n", "        output_prefix=r\"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data\")\n", "\n", "print(\"Samples Matrix Shape:\", samples_matrix.shape)\n", "print(\"Labels Shape:\", labels.shape)\n", "print()\n", "print(\"Blocks:\", blocks)\n", "print(\"Tables:\", stacks)\n", "print(\"Sample 0, Goal State:\\n\", samples_matrix[0, 0])\n", "print(\"Sample 0, Current State:\\n\", samples_matrix[0, 1])\n", "print(\"Sample 0, Labels:\", labels[0])"]}, {"cell_type": "code", "execution_count": 3, "id": "e9695f53-c08c-49e9-8608-d70283caa6f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goal_on_A_A</th>\n", "      <th>goal_on_A_B</th>\n", "      <th>goal_on_A_C</th>\n", "      <th>goal_on_A_D</th>\n", "      <th>goal_on_A_E</th>\n", "      <th>goal_on_A_F</th>\n", "      <th>goal_on_A_G</th>\n", "      <th>goal_on_A_H</th>\n", "      <th>goal_on_A_I</th>\n", "      <th>goal_on_A_J</th>\n", "      <th>...</th>\n", "      <th>action_19_clear_P</th>\n", "      <th>action_19_clear_Q</th>\n", "      <th>action_19_clear_R</th>\n", "      <th>action_19_clear_S</th>\n", "      <th>action_19_clear_T</th>\n", "      <th>action_19_clear_Stack1</th>\n", "      <th>action_19_clear_Stack2</th>\n", "      <th>action_19_clear_Stack3</th>\n", "      <th>action_19_clear_Stack4</th>\n", "      <th>action_19_clear_Stack5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 11550 columns</p>\n", "</div>"], "text/plain": ["   goal_on_A_A  goal_on_A_B  goal_on_A_C  goal_on_A_D  goal_on_A_E  \\\n", "0          0.0          0.0          0.0          0.0          0.0   \n", "1          0.0          0.0          0.0          0.0          0.0   \n", "2          0.0          0.0          0.0          0.0          0.0   \n", "3          0.0          0.0          0.0          0.0          0.0   \n", "4          0.0          0.0          0.0          0.0          0.0   \n", "\n", "   goal_on_A_F  goal_on_A_G  goal_on_A_H  goal_on_A_I  goal_on_A_J  ...  \\\n", "0          0.0          0.0          0.0          0.0          0.0  ...   \n", "1          0.0          0.0          0.0          0.0          0.0  ...   \n", "2          0.0          0.0          0.0          0.0          0.0  ...   \n", "3          0.0          0.0          0.0          0.0          0.0  ...   \n", "4          0.0          0.0          0.0          0.0          0.0  ...   \n", "\n", "   action_19_clear_P  action_19_clear_Q  action_19_clear_R  action_19_clear_S  \\\n", "0                0.0                0.0                0.0                0.0   \n", "1                0.0                0.0                0.0                0.0   \n", "2                0.0                1.0                1.0                0.0   \n", "3                0.0                1.0                1.0                0.0   \n", "4                0.0                1.0                1.0                1.0   \n", "\n", "   action_19_clear_T  action_19_clear_Stack1  action_19_clear_Stack2  \\\n", "0                0.0                     0.0                     0.0   \n", "1                0.0                     0.0                     0.0   \n", "2                1.0                     0.0                     0.0   \n", "3                1.0                     0.0                     0.0   \n", "4                1.0                     0.0                     0.0   \n", "\n", "   action_19_clear_Stack3  action_19_clear_Stack4  action_19_clear_Stack5  \n", "0                     0.0                     0.0                     0.0  \n", "1                     0.0                     0.0                     0.0  \n", "2                     0.0                     0.0                     0.0  \n", "3                     0.0                     0.0                     0.0  \n", "4                     0.0                     0.0                     0.0  \n", "\n", "[5 rows x 11550 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "data=pd.read_csv('/root/Train/cnn_transformer_block_in_out/Data/Train_data/data_matrix.csv')\n", "data[:5]"]}, {"cell_type": "code", "execution_count": 4, "id": "b1b449f3-58d8-40b8-81ac-2af847b1868e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一行中列名开头为'current'的所有数据 (全部显示):\n", "current_on_A_A           0.0\n", "current_on_A_B           0.0\n", "current_on_A_C           1.0\n", "current_on_A_D           0.0\n", "current_on_A_E           0.0\n", "current_on_A_F           0.0\n", "current_on_A_G           0.0\n", "current_on_A_H           0.0\n", "current_on_A_I           0.0\n", "current_on_A_J           0.0\n", "current_on_A_K           0.0\n", "current_on_A_L           0.0\n", "current_on_A_M           0.0\n", "current_on_A_N           0.0\n", "current_on_A_O           0.0\n", "current_on_A_P           0.0\n", "current_on_A_Q           0.0\n", "current_on_A_R           0.0\n", "current_on_A_S           0.0\n", "current_on_A_T           0.0\n", "current_on_A_Stack1      0.0\n", "current_on_A_Stack2      0.0\n", "current_on_A_Stack3      0.0\n", "current_on_A_Stack4      0.0\n", "current_on_A_Stack5      0.0\n", "current_on_B_A           0.0\n", "current_on_B_B           0.0\n", "current_on_B_C           0.0\n", "current_on_B_D           0.0\n", "current_on_B_E           0.0\n", "current_on_B_F           0.0\n", "current_on_B_G           0.0\n", "current_on_B_H           0.0\n", "current_on_B_I           0.0\n", "current_on_B_J           0.0\n", "current_on_B_K           0.0\n", "current_on_B_L           0.0\n", "current_on_B_M           0.0\n", "current_on_B_N           0.0\n", "current_on_B_O           0.0\n", "current_on_B_P           0.0\n", "current_on_B_Q           0.0\n", "current_on_B_R           0.0\n", "current_on_B_S           0.0\n", "current_on_B_T           0.0\n", "current_on_B_Stack1      0.0\n", "current_on_B_Stack2      3.0\n", "current_on_B_Stack3      0.0\n", "current_on_B_Stack4      0.0\n", "current_on_B_Stack5      0.0\n", "current_on_C_A           0.0\n", "current_on_C_B           0.0\n", "current_on_C_C           0.0\n", "current_on_C_D           0.0\n", "current_on_C_E           0.0\n", "current_on_C_F           0.0\n", "current_on_C_G           0.0\n", "current_on_C_H           0.0\n", "current_on_C_I           0.0\n", "current_on_C_J           0.0\n", "current_on_C_K           0.0\n", "current_on_C_L           0.0\n", "current_on_C_M           0.0\n", "current_on_C_N           1.0\n", "current_on_C_O           0.0\n", "current_on_C_P           0.0\n", "current_on_C_Q           0.0\n", "current_on_C_R           0.0\n", "current_on_C_S           0.0\n", "current_on_C_T           0.0\n", "current_on_C_Stack1      0.0\n", "current_on_C_Stack2      0.0\n", "current_on_C_Stack3      0.0\n", "current_on_C_Stack4      0.0\n", "current_on_C_Stack5      0.0\n", "current_on_D_A           0.0\n", "current_on_D_B           0.0\n", "current_on_D_C           0.0\n", "current_on_D_D           0.0\n", "current_on_D_E           0.0\n", "current_on_D_F           0.0\n", "current_on_D_G           0.0\n", "current_on_D_H           0.0\n", "current_on_D_I           0.0\n", "current_on_D_J           0.0\n", "current_on_D_K           0.0\n", "current_on_D_L           0.0\n", "current_on_D_M           0.0\n", "current_on_D_N           0.0\n", "current_on_D_O           0.0\n", "current_on_D_P           1.0\n", "current_on_D_Q           0.0\n", "current_on_D_R           0.0\n", "current_on_D_S           0.0\n", "current_on_D_T           0.0\n", "current_on_D_Stack1      0.0\n", "current_on_D_Stack2      0.0\n", "current_on_D_Stack3      0.0\n", "current_on_D_Stack4      0.0\n", "current_on_D_Stack5      0.0\n", "current_on_E_A           0.0\n", "current_on_E_B           0.0\n", "current_on_E_C           0.0\n", "current_on_E_D           0.0\n", "current_on_E_E           0.0\n", "current_on_E_F           0.0\n", "current_on_E_G           0.0\n", "current_on_E_H           0.0\n", "current_on_E_I           0.0\n", "current_on_E_J           0.0\n", "current_on_E_K           0.0\n", "current_on_E_L           0.0\n", "current_on_E_M          15.0\n", "current_on_E_N           0.0\n", "current_on_E_O           0.0\n", "current_on_E_P           0.0\n", "current_on_E_Q           0.0\n", "current_on_E_R           0.0\n", "current_on_E_S           0.0\n", "current_on_E_T           0.0\n", "current_on_E_Stack1      0.0\n", "current_on_E_Stack2      0.0\n", "current_on_E_Stack3      0.0\n", "current_on_E_Stack4      0.0\n", "current_on_E_Stack5      0.0\n", "current_on_F_A           0.0\n", "current_on_F_B           1.0\n", "current_on_F_C           0.0\n", "current_on_F_D           0.0\n", "current_on_F_E           0.0\n", "current_on_F_F           0.0\n", "current_on_F_G           0.0\n", "current_on_F_H           0.0\n", "current_on_F_I           0.0\n", "current_on_F_J           0.0\n", "current_on_F_K           0.0\n", "current_on_F_L           0.0\n", "current_on_F_M           0.0\n", "current_on_F_N           0.0\n", "current_on_F_O           0.0\n", "current_on_F_P           0.0\n", "current_on_F_Q           0.0\n", "current_on_F_R           0.0\n", "current_on_F_S           0.0\n", "current_on_F_T           0.0\n", "current_on_F_Stack1      0.0\n", "current_on_F_Stack2      0.0\n", "current_on_F_Stack3      0.0\n", "current_on_F_Stack4      0.0\n", "current_on_F_Stack5      0.0\n", "current_on_G_A           0.0\n", "current_on_G_B           0.0\n", "current_on_G_C           0.0\n", "current_on_G_D           0.0\n", "current_on_G_E           0.0\n", "current_on_G_F           0.0\n", "current_on_G_G           0.0\n", "current_on_G_H           0.0\n", "current_on_G_I           0.0\n", "current_on_G_J           0.0\n", "current_on_G_K           0.0\n", "current_on_G_L           0.0\n", "current_on_G_M           0.0\n", "current_on_G_N           0.0\n", "current_on_G_O           0.0\n", "current_on_G_P           0.0\n", "current_on_G_Q           0.0\n", "current_on_G_R           0.0\n", "current_on_G_S           0.0\n", "current_on_G_T           0.0\n", "current_on_G_Stack1      0.0\n", "current_on_G_Stack2      0.0\n", "current_on_G_Stack3      0.0\n", "current_on_G_Stack4      1.0\n", "current_on_G_Stack5      0.0\n", "current_on_H_A           0.0\n", "current_on_H_B           0.0\n", "current_on_H_C           0.0\n", "current_on_H_D           0.0\n", "current_on_H_E           0.0\n", "current_on_H_F           0.0\n", "current_on_H_G           0.0\n", "current_on_H_H           0.0\n", "current_on_H_I           0.0\n", "current_on_H_J           0.0\n", "current_on_H_K           0.0\n", "current_on_H_L           0.0\n", "current_on_H_M           0.0\n", "current_on_H_N           0.0\n", "current_on_H_O          10.0\n", "current_on_H_P           0.0\n", "current_on_H_Q           0.0\n", "current_on_H_R           0.0\n", "current_on_H_S           0.0\n", "current_on_H_T           0.0\n", "current_on_H_Stack1      0.0\n", "current_on_H_Stack2      0.0\n", "current_on_H_Stack3      0.0\n", "current_on_H_Stack4      0.0\n", "current_on_H_Stack5      0.0\n", "current_on_I_A           0.0\n", "current_on_I_B           0.0\n", "current_on_I_C           0.0\n", "current_on_I_D           0.0\n", "current_on_I_E           0.0\n", "current_on_I_F           0.0\n", "current_on_I_G           0.0\n", "current_on_I_H           0.0\n", "current_on_I_I           0.0\n", "current_on_I_J           0.0\n", "current_on_I_K           0.0\n", "current_on_I_L           0.0\n", "current_on_I_M           0.0\n", "current_on_I_N           0.0\n", "current_on_I_O           0.0\n", "current_on_I_P           0.0\n", "current_on_I_Q           0.0\n", "current_on_I_R           0.0\n", "current_on_I_S           0.0\n", "current_on_I_T           0.0\n", "current_on_I_Stack1      1.0\n", "current_on_I_Stack2      0.0\n", "current_on_I_Stack3      0.0\n", "current_on_I_Stack4      0.0\n", "current_on_I_Stack5      0.0\n", "current_on_J_A           0.0\n", "current_on_J_B           0.0\n", "current_on_J_C           0.0\n", "current_on_J_D           0.0\n", "current_on_J_E           0.0\n", "current_on_J_F           0.0\n", "current_on_J_G           0.0\n", "current_on_J_H           0.0\n", "current_on_J_I           0.0\n", "current_on_J_J           0.0\n", "current_on_J_K           3.0\n", "current_on_J_L           0.0\n", "current_on_J_M           0.0\n", "current_on_J_N           0.0\n", "current_on_J_O           0.0\n", "current_on_J_P           0.0\n", "current_on_J_Q           0.0\n", "current_on_J_R           0.0\n", "current_on_J_S           0.0\n", "current_on_J_T           0.0\n", "current_on_J_Stack1      0.0\n", "current_on_J_Stack2      0.0\n", "current_on_J_Stack3      0.0\n", "current_on_J_Stack4      0.0\n", "current_on_J_Stack5      0.0\n", "current_on_K_A           0.0\n", "current_on_K_B           0.0\n", "current_on_K_C           0.0\n", "current_on_K_D           0.0\n", "current_on_K_E           0.0\n", "current_on_K_F           1.0\n", "current_on_K_G           0.0\n", "current_on_K_H           0.0\n", "current_on_K_I           0.0\n", "current_on_K_J           0.0\n", "current_on_K_K           0.0\n", "current_on_K_L           0.0\n", "current_on_K_M           0.0\n", "current_on_K_N           0.0\n", "current_on_K_O           0.0\n", "current_on_K_P           0.0\n", "current_on_K_Q           0.0\n", "current_on_K_R           0.0\n", "current_on_K_S           0.0\n", "current_on_K_T           0.0\n", "current_on_K_Stack1      0.0\n", "current_on_K_Stack2      0.0\n", "current_on_K_Stack3      0.0\n", "current_on_K_Stack4      0.0\n", "current_on_K_Stack5      0.0\n", "current_on_L_A           0.0\n", "current_on_L_B           0.0\n", "current_on_L_C           0.0\n", "current_on_L_D           0.0\n", "current_on_L_E           0.0\n", "current_on_L_F           0.0\n", "current_on_L_G           0.0\n", "current_on_L_H           0.0\n", "current_on_L_I           3.0\n", "current_on_L_J           0.0\n", "current_on_L_K           0.0\n", "current_on_L_L           0.0\n", "current_on_L_M           0.0\n", "current_on_L_N           0.0\n", "current_on_L_O           0.0\n", "current_on_L_P           0.0\n", "current_on_L_Q           0.0\n", "current_on_L_R           0.0\n", "current_on_L_S           0.0\n", "current_on_L_T           0.0\n", "current_on_L_Stack1      0.0\n", "current_on_L_Stack2      0.0\n", "current_on_L_Stack3      0.0\n", "current_on_L_Stack4      0.0\n", "current_on_L_Stack5      0.0\n", "current_on_M_A           0.0\n", "current_on_M_B           0.0\n", "current_on_M_C           0.0\n", "current_on_M_D           0.0\n", "current_on_M_E           0.0\n", "current_on_M_F           0.0\n", "current_on_M_G           0.0\n", "current_on_M_H           0.0\n", "current_on_M_I           0.0\n", "current_on_M_J           0.0\n", "current_on_M_K           0.0\n", "current_on_M_L           0.0\n", "current_on_M_M           0.0\n", "current_on_M_N           0.0\n", "current_on_M_O           0.0\n", "current_on_M_P           0.0\n", "current_on_M_Q           0.0\n", "current_on_M_R           0.0\n", "current_on_M_S          10.0\n", "current_on_M_T           0.0\n", "current_on_M_Stack1      0.0\n", "current_on_M_Stack2      0.0\n", "current_on_M_Stack3      0.0\n", "current_on_M_Stack4      0.0\n", "current_on_M_Stack5      0.0\n", "current_on_N_A           0.0\n", "current_on_N_B           0.0\n", "current_on_N_C           0.0\n", "current_on_N_D           0.0\n", "current_on_N_E           0.0\n", "current_on_N_F           0.0\n", "current_on_N_G           1.0\n", "current_on_N_H           0.0\n", "current_on_N_I           0.0\n", "current_on_N_J           0.0\n", "current_on_N_K           0.0\n", "current_on_N_L           0.0\n", "current_on_N_M           0.0\n", "current_on_N_N           0.0\n", "current_on_N_O           0.0\n", "current_on_N_P           0.0\n", "current_on_N_Q           0.0\n", "current_on_N_R           0.0\n", "current_on_N_S           0.0\n", "current_on_N_T           0.0\n", "current_on_N_Stack1      0.0\n", "current_on_N_Stack2      0.0\n", "current_on_N_Stack3      0.0\n", "current_on_N_Stack4      0.0\n", "current_on_N_Stack5      0.0\n", "current_on_O_A           0.0\n", "current_on_O_B           0.0\n", "current_on_O_C           0.0\n", "current_on_O_D           0.0\n", "current_on_O_E           0.0\n", "current_on_O_F           0.0\n", "current_on_O_G           0.0\n", "current_on_O_H           0.0\n", "current_on_O_I           0.0\n", "current_on_O_J           0.0\n", "current_on_O_K           0.0\n", "current_on_O_L           0.0\n", "current_on_O_M           0.0\n", "current_on_O_N           0.0\n", "current_on_O_O           0.0\n", "current_on_O_P           0.0\n", "current_on_O_Q           0.0\n", "current_on_O_R           0.0\n", "current_on_O_S           0.0\n", "current_on_O_T           0.0\n", "current_on_O_Stack1      0.0\n", "current_on_O_Stack2      0.0\n", "current_on_O_Stack3      7.0\n", "current_on_O_Stack4      0.0\n", "current_on_O_Stack5      0.0\n", "current_on_P_A           0.0\n", "current_on_P_B           0.0\n", "current_on_P_C           0.0\n", "current_on_P_D           0.0\n", "current_on_P_E           0.0\n", "current_on_P_F           0.0\n", "current_on_P_G           0.0\n", "current_on_P_H           0.0\n", "current_on_P_I           0.0\n", "current_on_P_J           0.0\n", "current_on_P_K           0.0\n", "current_on_P_L           1.0\n", "current_on_P_M           0.0\n", "current_on_P_N           0.0\n", "current_on_P_O           0.0\n", "current_on_P_P           0.0\n", "current_on_P_Q           0.0\n", "current_on_P_R           0.0\n", "current_on_P_S           0.0\n", "current_on_P_T           0.0\n", "current_on_P_Stack1      0.0\n", "current_on_P_Stack2      0.0\n", "current_on_P_Stack3      0.0\n", "current_on_P_Stack4      0.0\n", "current_on_P_Stack5      0.0\n", "current_on_Q_A           0.0\n", "current_on_Q_B           0.0\n", "current_on_Q_C           0.0\n", "current_on_Q_D           0.0\n", "current_on_Q_E           0.0\n", "current_on_Q_F           0.0\n", "current_on_Q_G           0.0\n", "current_on_Q_H           0.0\n", "current_on_Q_I           0.0\n", "current_on_Q_J           3.0\n", "current_on_Q_K           0.0\n", "current_on_Q_L           0.0\n", "current_on_Q_M           0.0\n", "current_on_Q_N           0.0\n", "current_on_Q_O           0.0\n", "current_on_Q_P           0.0\n", "current_on_Q_Q           0.0\n", "current_on_Q_R           0.0\n", "current_on_Q_S           0.0\n", "current_on_Q_T           0.0\n", "current_on_Q_Stack1      0.0\n", "current_on_Q_Stack2      0.0\n", "current_on_Q_Stack3      0.0\n", "current_on_Q_Stack4      0.0\n", "current_on_Q_Stack5      0.0\n", "current_on_R_A           1.0\n", "current_on_R_B           0.0\n", "current_on_R_C           0.0\n", "current_on_R_D           0.0\n", "current_on_R_E           0.0\n", "current_on_R_F           0.0\n", "current_on_R_G           0.0\n", "current_on_R_H           0.0\n", "current_on_R_I           0.0\n", "current_on_R_J           0.0\n", "current_on_R_K           0.0\n", "current_on_R_L           0.0\n", "current_on_R_M           0.0\n", "current_on_R_N           0.0\n", "current_on_R_O           0.0\n", "current_on_R_P           0.0\n", "current_on_R_Q           0.0\n", "current_on_R_R           0.0\n", "current_on_R_S           0.0\n", "current_on_R_T           0.0\n", "current_on_R_Stack1      0.0\n", "current_on_R_Stack2      0.0\n", "current_on_R_Stack3      0.0\n", "current_on_R_Stack4      0.0\n", "current_on_R_Stack5      0.0\n", "current_on_S_A           0.0\n", "current_on_S_B           0.0\n", "current_on_S_C           0.0\n", "current_on_S_D           0.0\n", "current_on_S_E           0.0\n", "current_on_S_F           0.0\n", "current_on_S_G           0.0\n", "current_on_S_H          10.0\n", "current_on_S_I           0.0\n", "current_on_S_J           0.0\n", "current_on_S_K           0.0\n", "current_on_S_L           0.0\n", "current_on_S_M           0.0\n", "current_on_S_N           0.0\n", "current_on_S_O           0.0\n", "current_on_S_P           0.0\n", "current_on_S_Q           0.0\n", "current_on_S_R           0.0\n", "current_on_S_S           0.0\n", "current_on_S_T           0.0\n", "current_on_S_Stack1      0.0\n", "current_on_S_Stack2      0.0\n", "current_on_S_Stack3      0.0\n", "current_on_S_Stack4      0.0\n", "current_on_S_Stack5      0.0\n", "current_on_T_A           0.0\n", "current_on_T_B           0.0\n", "current_on_T_C           0.0\n", "current_on_T_D           1.0\n", "current_on_T_E           0.0\n", "current_on_T_F           0.0\n", "current_on_T_G           0.0\n", "current_on_T_H           0.0\n", "current_on_T_I           0.0\n", "current_on_T_J           0.0\n", "current_on_T_K           0.0\n", "current_on_T_L           0.0\n", "current_on_T_M           0.0\n", "current_on_T_N           0.0\n", "current_on_T_O           0.0\n", "current_on_T_P           0.0\n", "current_on_T_Q           0.0\n", "current_on_T_R           0.0\n", "current_on_T_S           0.0\n", "current_on_T_T           0.0\n", "current_on_T_Stack1      0.0\n", "current_on_T_Stack2      0.0\n", "current_on_T_Stack3      0.0\n", "current_on_T_Stack4      0.0\n", "current_on_T_Stack5      0.0\n", "current_clear_A          0.0\n", "current_clear_B          0.0\n", "current_clear_C          0.0\n", "current_clear_D          0.0\n", "current_clear_E          1.0\n", "current_clear_F          0.0\n", "current_clear_G          0.0\n", "current_clear_H          0.0\n", "current_clear_I          0.0\n", "current_clear_J          0.0\n", "current_clear_K          0.0\n", "current_clear_L          0.0\n", "current_clear_M          0.0\n", "current_clear_N          0.0\n", "current_clear_O          0.0\n", "current_clear_P          0.0\n", "current_clear_Q          1.0\n", "current_clear_R          1.0\n", "current_clear_S          0.0\n", "current_clear_T          1.0\n", "current_clear_Stack1     0.0\n", "current_clear_Stack2     0.0\n", "current_clear_Stack3     0.0\n", "current_clear_Stack4     0.0\n", "current_clear_Stack5     0.0\n", "Name: 0, dtype: float64\n", "\n", "(Pandas 显示选项已恢复默认)\n"]}], "source": ["import pandas as pd\n", "import numpy as np # 如果你的代码里用到了np，虽然这里主要改pd的显示选项\n", "\n", "# 假设 data 已经加载\n", "# data = pd.read_csv('/root/Train/Data/Train_data/data_matrix.csv')\n", "\n", "# --- 之前获取第一行中列名开头为'current'的所有数据的代码 ---\n", "all_columns = data.columns\n", "current_columns_mask = all_columns.str.startswith('current')\n", "first_row_current_data = data.loc[0, current_columns_mask]\n", "# ------------------------------------------------------------\n", "\n", "# --- 新增：修改 Pandas 显示选项以显示所有行 ---\n", "# 保存当前设置，以便之后恢复 (可选，但推荐)\n", "# default_max_rows = pd.get_option('display.max_rows')\n", "\n", "# 将最大显示行数设置为 None，表示不限制\n", "pd.set_option('display.max_rows', None)\n", "\n", "# --- 打印数据 ---\n", "print(\"第一行中列名开头为'current'的所有数据 (全部显示):\")\n", "print(first_row_current_data)\n", "\n", "# --- 恢复 Pandas 显示选项到默认值或之前的值 (可选，但推荐) ---\n", "# 如果之前保存了默认值，可以用这个恢复\n", "# pd.set_option('display.max_rows', default_max_rows)\n", "# 或者直接重置到 Pandas 的默认值\n", "pd.reset_option('display.max_rows')\n", "\n", "print(\"\\n(Pandas 显示选项已恢复默认)\")"]}, {"cell_type": "code", "execution_count": 5, "id": "f4e07d9a-45ae-45ce-b8cb-608bff577bb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前 Jupyter 运行路径: /root/Train/cnn_transformer_block_in_out\n"]}], "source": ["import os\n", "\n", "# 获取当前工作目录\n", "current_directory = os.getcwd()\n", "\n", "# 打印路径\n", "print(\"当前 Jupyter 运行路径:\", current_directory)"]}, {"cell_type": "code", "execution_count": 6, "id": "a1b6ed77-435b-48d8-8f83-f90be37de86d", "metadata": {}, "outputs": [], "source": ["from generate_samples_from_log import parse_log,generate_sample_matrix"]}, {"cell_type": "code", "execution_count": 7, "id": "e1167e24-36ce-4f68-beeb-677459edb56f", "metadata": {}, "outputs": [], "source": ["with open(r\"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    log_content = f.read()\n", "log_samples = parse_log(log_content)"]}, {"cell_type": "code", "execution_count": 8, "id": "b675b81e-0b01-4f57-9224-eb4397646ebd", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'node': 1,\n", " 'current_state': {'Stack1': ['I', 'L', 'P', 'D', 'T'],\n", "  'Stack2': ['B', 'F', 'K', 'J', 'Q'],\n", "  'Stack3': ['O', 'H', 'S', 'M', 'E'],\n", "  'Stack4': ['G', 'N', 'C', 'A', 'R'],\n", "  'Stack5': []},\n", " 'goal_state': {'Stack1': ['A', 'T', 'C', 'N', 'F'],\n", "  'Stack2': ['E', 'R', 'S', 'I', 'K'],\n", "  'Stack3': ['O', 'B', 'L', 'Q', 'J'],\n", "  'Stack4': ['M', 'D', 'G', 'P', 'H'],\n", "  'Stack5': []},\n", " 'best_action': (3, 5),\n", " 'worst_action': (1, 3),\n", " 'fix_stack_id': 3,\n", " 'attention_block_name': 'E',\n", " 'priority_task_description': '移走Stack3顶部错误块E'}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["log_samples[0]"]}, {"cell_type": "code", "execution_count": 9, "id": "f360b949-64ca-4922-ac22-4a686099b806", "metadata": {}, "outputs": [], "source": ["samples_matrix, labels, blocks, stacks = generate_sample_matrix(log_samples[0:2])"]}, {"cell_type": "code", "execution_count": 10, "id": "76aee681-72af-4064-93f2-1748f22fd116", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  3.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0., 15.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0., 10.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  3.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  3.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0., 10.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  7.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  1.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  3.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  0.,  0.,  0., 10.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.],\n", "       [ 0.,  0.,  0.,  0.,  1.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,  0.,\n", "         0.,  0.,  0.,  1.,  1.,  0.,  1.,  0.,  0.,  0.,  0.,  0.]])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["samples_matrix[0][1]"]}, {"cell_type": "code", "execution_count": 11, "id": "a3628ba3-ea6f-450f-9ba8-60bb3f329f89", "metadata": {}, "outputs": [], "source": ["from generate_samples_from_log import parse_log,generate_sample_matrix,save_to_csv\n", "save_to_csv(samples_matrix, labels, blocks, stacks, \"/root/Train/cnn_transformer_block_pro/data\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8e0b554-83e2-432f-a85c-ccdd140890d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "pre_marshalling"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}