{"cells": [{"cell_type": "code", "execution_count": 3, "id": "31285500-1bfc-45ff-a1a4-5f6616ce2a75", "metadata": {}, "outputs": [], "source": ["import os\n", "from cnn_transformer_modify import train_model,evaluate_model,CNNTransformerClassifier"]}, {"cell_type": "code", "execution_count": 4, "id": "1812ac30-af6c-40ec-9dda-732301479885", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n", "Labels: min=2, max=21\n", "No existing model found at /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth, starting training from scratch\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/envs/pre_marshalling/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/500, Training Loss: 5.0286\n", "Epoch 1/500, Validation Loss: 4.4664\n", "Validation loss improved. Saving model state.\n", "Epoch 2/500, Training Loss: 4.2904\n", "Epoch 2/500, Validation Loss: 3.9818\n", "Validation loss improved. Saving model state.\n", "Epoch 3/500, Training Loss: 3.7914\n", "Epoch 3/500, Validation Loss: 3.7997\n", "Validation loss improved. Saving model state.\n", "Epoch 4/500, Training Loss: 3.5616\n", "Epoch 4/500, Validation Loss: 3.5696\n", "Validation loss improved. Saving model state.\n", "Epoch 5/500, Training Loss: 3.4564\n", "Epoch 5/500, Validation Loss: 3.4783\n", "Validation loss improved. Saving model state.\n", "Epoch 6/500, Training Loss: 3.3350\n", "Epoch 6/500, Validation Loss: 3.4452\n", "Validation loss improved. Saving model state.\n", "Epoch 7/500, Training Loss: 3.2513\n", "Epoch 7/500, Validation Loss: 3.3428\n", "Validation loss improved. Saving model state.\n", "Epoch 8/500, Training Loss: 3.2079\n", "Epoch 8/500, Validation Loss: 3.4180\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 9/500, Training Loss: 3.1760\n", "Epoch 9/500, Validation Loss: 3.3266\n", "Validation loss improved. Saving model state.\n", "Epoch 10/500, Training Loss: 3.1489\n", "Epoch 10/500, Validation Loss: 3.2774\n", "Validation loss improved. Saving model state.\n", "Epoch 11/500, Training Loss: 3.1218\n", "Epoch 11/500, Validation Loss: 3.2637\n", "Validation loss improved. Saving model state.\n", "Epoch 12/500, Training Loss: 3.1058\n", "Epoch 12/500, Validation Loss: 3.2617\n", "Validation loss improved. Saving model state.\n", "Epoch 13/500, Training Loss: 3.0828\n", "Epoch 13/500, Validation Loss: 3.2816\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 14/500, Training Loss: 3.0561\n", "Epoch 14/500, Validation Loss: 3.1487\n", "Validation loss improved. Saving model state.\n", "Epoch 15/500, Training Loss: 3.0131\n", "Epoch 15/500, Validation Loss: 3.1681\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 16/500, Training Loss: 2.9448\n", "Epoch 16/500, Validation Loss: 3.0184\n", "Validation loss improved. Saving model state.\n", "Epoch 17/500, Training Loss: 2.8971\n", "Epoch 17/500, Validation Loss: 3.0124\n", "Validation loss improved. Saving model state.\n", "Epoch 18/500, Training Loss: 2.8583\n", "Epoch 18/500, Validation Loss: 2.9489\n", "Validation loss improved. Saving model state.\n", "Epoch 19/500, Training Loss: 2.8488\n", "Epoch 19/500, Validation Loss: 2.9570\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 20/500, Training Loss: 2.8343\n", "Epoch 20/500, Validation Loss: 2.9710\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 21/500, Training Loss: 2.8167\n", "Epoch 21/500, Validation Loss: 2.9114\n", "Validation loss improved. Saving model state.\n", "Epoch 22/500, Training Loss: 2.7936\n", "Epoch 22/500, Validation Loss: 2.9107\n", "Validation loss improved. Saving model state.\n", "Epoch 23/500, Training Loss: 2.7710\n", "Epoch 23/500, Validation Loss: 2.8727\n", "Validation loss improved. Saving model state.\n", "Epoch 24/500, Training Loss: 2.7679\n", "Epoch 24/500, Validation Loss: 2.9117\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 25/500, Training Loss: 2.7483\n", "Epoch 25/500, Validation Loss: 2.8341\n", "Validation loss improved. Saving model state.\n", "Epoch 26/500, Training Loss: 2.7239\n", "Epoch 26/500, Validation Loss: 2.8358\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 27/500, Training Loss: 2.7162\n", "Epoch 27/500, Validation Loss: 2.8302\n", "Validation loss improved. Saving model state.\n", "Epoch 28/500, Training Loss: 2.7102\n", "Epoch 28/500, Validation Loss: 2.8040\n", "Validation loss improved. Saving model state.\n", "Epoch 29/500, Training Loss: 2.7042\n", "Epoch 29/500, Validation Loss: 2.8218\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 30/500, Training Loss: 2.6962\n", "Epoch 30/500, Validation Loss: 2.8304\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 31/500, Training Loss: 2.6847\n", "Epoch 31/500, Validation Loss: 2.8348\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 32/500, Training Loss: 2.6812\n", "Epoch 32/500, Validation Loss: 2.8075\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 33/500, Training Loss: 2.6738\n", "Epoch 33/500, Validation Loss: 2.8021\n", "Validation loss improved. Saving model state.\n", "Epoch 34/500, Training Loss: 2.6690\n", "Epoch 34/500, Validation Loss: 2.7912\n", "Validation loss improved. Saving model state.\n", "Epoch 35/500, Training Loss: 2.6681\n", "Epoch 35/500, Validation Loss: 2.7956\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 36/500, Training Loss: 2.6565\n", "Epoch 36/500, Validation Loss: 2.7643\n", "Validation loss improved. Saving model state.\n", "Epoch 37/500, Training Loss: 2.6537\n", "Epoch 37/500, Validation Loss: 2.7908\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 38/500, Training Loss: 2.6496\n", "Epoch 38/500, Validation Loss: 2.7554\n", "Validation loss improved. Saving model state.\n", "Epoch 39/500, Training Loss: 2.6403\n", "Epoch 39/500, Validation Loss: 2.7783\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 40/500, Training Loss: 2.6323\n", "Epoch 40/500, Validation Loss: 2.7564\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 41/500, Training Loss: 2.6278\n", "Epoch 41/500, Validation Loss: 2.7781\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 42/500, Training Loss: 2.6273\n", "Epoch 42/500, Validation Loss: 2.7788\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 43/500, Training Loss: 2.6305\n", "Epoch 43/500, Validation Loss: 2.7873\n", "Validation loss did not improve for 5 epoch(s).\n", "Epoch 44/500, Training Loss: 2.6238\n", "Epoch 44/500, Validation Loss: 2.7544\n", "Validation loss improved. Saving model state.\n", "Epoch 45/500, Training Loss: 2.6322\n", "Epoch 45/500, Validation Loss: 2.7296\n", "Validation loss improved. Saving model state.\n", "Epoch 46/500, Training Loss: 2.6098\n", "Epoch 46/500, Validation Loss: 2.7515\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 47/500, Training Loss: 2.6156\n", "Epoch 47/500, Validation Loss: 2.7582\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 48/500, Training Loss: 2.6060\n", "Epoch 48/500, Validation Loss: 2.7869\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 49/500, Training Loss: 2.6052\n", "Epoch 49/500, Validation Loss: 2.8002\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 50/500, Training Loss: 2.6073\n", "Epoch 50/500, Validation Loss: 2.7534\n", "Validation loss did not improve for 5 epoch(s).\n", "Epoch 51/500, Training Loss: 2.5949\n", "Epoch 51/500, Validation Loss: 2.7769\n", "Validation loss did not improve for 6 epoch(s).\n", "Epoch 52/500, Training Loss: 2.5962\n", "Epoch 52/500, Validation Loss: 2.7613\n", "Validation loss did not improve for 7 epoch(s).\n", "Epoch 53/500, Training Loss: 2.5928\n", "Epoch 53/500, Validation Loss: 2.7491\n", "Validation loss did not improve for 8 epoch(s).\n", "Epoch 54/500, Training Loss: 2.5828\n", "Epoch 54/500, Validation Loss: 2.7584\n", "Validation loss did not improve for 9 epoch(s).\n", "Epoch 55/500, Training Loss: 2.5838\n", "Epoch 55/500, Validation Loss: 2.7586\n", "Validation loss did not improve for 10 epoch(s).\n", "Epoch 56/500, Training Loss: 2.5813\n", "Epoch 56/500, Validation Loss: 2.7395\n", "Validation loss did not improve for 11 epoch(s).\n", "Epoch 57/500, Training Loss: 2.5500\n", "Epoch 57/500, Validation Loss: 2.7560\n", "Validation loss did not improve for 12 epoch(s).\n", "Epoch 58/500, Training Loss: 2.5460\n", "Epoch 58/500, Validation Loss: 2.7291\n", "Validation loss improved. Saving model state.\n", "Epoch 59/500, Training Loss: 2.5436\n", "Epoch 59/500, Validation Loss: 2.7099\n", "Validation loss improved. Saving model state.\n", "Epoch 60/500, Training Loss: 2.5350\n", "Epoch 60/500, Validation Loss: 2.7214\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 61/500, Training Loss: 2.5339\n", "Epoch 61/500, Validation Loss: 2.7362\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 62/500, Training Loss: 2.5351\n", "Epoch 62/500, Validation Loss: 2.7286\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 63/500, Training Loss: 2.5329\n", "Epoch 63/500, Validation Loss: 2.7272\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 64/500, Training Loss: 2.5345\n", "Epoch 64/500, Validation Loss: 2.7436\n", "Validation loss did not improve for 5 epoch(s).\n", "Epoch 65/500, Training Loss: 2.5249\n", "Epoch 65/500, Validation Loss: 2.7386\n", "Validation loss did not improve for 6 epoch(s).\n", "Epoch 66/500, Training Loss: 2.5293\n", "Epoch 66/500, Validation Loss: 2.7212\n", "Validation loss did not improve for 7 epoch(s).\n", "Epoch 67/500, Training Loss: 2.5243\n", "Epoch 67/500, Validation Loss: 2.7523\n", "Validation loss did not improve for 8 epoch(s).\n", "Epoch 68/500, Training Loss: 2.5244\n", "Epoch 68/500, Validation Loss: 2.7197\n", "Validation loss did not improve for 9 epoch(s).\n", "Epoch 69/500, Training Loss: 2.5172\n", "Epoch 69/500, Validation Loss: 2.7183\n", "Validation loss did not improve for 10 epoch(s).\n", "Epoch 70/500, Training Loss: 2.5299\n", "Epoch 70/500, Validation Loss: 2.7575\n", "Validation loss did not improve for 11 epoch(s).\n", "Epoch 71/500, Training Loss: 2.5089\n", "Epoch 71/500, Validation Loss: 2.7204\n", "Validation loss did not improve for 12 epoch(s).\n", "Epoch 72/500, Training Loss: 2.4987\n", "Epoch 72/500, Validation Loss: 2.7272\n", "Validation loss did not improve for 13 epoch(s).\n", "Epoch 73/500, Training Loss: 2.4986\n", "Epoch 73/500, Validation Loss: 2.7360\n", "Validation loss did not improve for 14 epoch(s).\n", "Epoch 74/500, Training Loss: 2.4906\n", "Epoch 74/500, Validation Loss: 2.7381\n", "Validation loss did not improve for 15 epoch(s).\n", "Epoch 75/500, Training Loss: 2.4941\n", "Epoch 75/500, Validation Loss: 2.7325\n", "Validation loss did not improve for 16 epoch(s).\n", "Epoch 76/500, Training Loss: 2.4884\n", "Epoch 76/500, Validation Loss: 2.7161\n", "Validation loss did not improve for 17 epoch(s).\n", "Epoch 77/500, Training Loss: 2.4908\n", "Epoch 77/500, Validation Loss: 2.7318\n", "Validation loss did not improve for 18 epoch(s).\n", "Epoch 78/500, Training Loss: 2.4868\n", "Epoch 78/500, Validation Loss: 2.7190\n", "Validation loss did not improve for 19 epoch(s).\n", "Epoch 79/500, Training Loss: 2.4880\n", "Epoch 79/500, Validation Loss: 2.7643\n", "Validation loss did not improve for 20 epoch(s).\n", "Early stopping triggered after 79 epochs.\n", "Model saved to /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\n", "--- 正在评估训练集数据 ---\n", "Labels: min=2, max=21\n", "Best Action Accuracy: 0.7025 (21637/30802)\n", "Worst Action Accuracy: 0.7442 (22922/30802)\n", "\n", "--- 正在评估测试集数据 ---\n", "Labels: min=2, max=21\n", "Best Action Accuracy: 0.6505 (2909/4472)\n", "Worst Action Accuracy: 0.7200 (3220/4472)\n"]}], "source": ["\n", "\n", "# 训练数据路径\n", "train_matrix_file = \"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data_matrix.csv\"\n", "train_label_file = \"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data_labels.csv\"\n", "\n", "# 测试数据路径\n", "test_matrix_file = \"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data_matrix.csv\"\n", "test_label_file = \"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data_labels.csv\"\n", "\n", "\n", "# 基础形状\n", "base_shape=(22,21,25)\n", "\n", "model_path=\"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "\n", "# 实例化模型\n", "model = CNNTransformerClassifier(\n", "    n_layers=base_shape[0],          # 22 (基础图像通道数)\n", "    n_rows=base_shape[1],            # 21\n", "    n_cols=base_shape[2],            # 25\n", "    embed_dim=64,                    # Transformer嵌入维度\n", "    n_heads=4,                       # Transformer头数\n", "    n_hidden=256,                    # Transformer隐藏层维度\n", "    n_classes=20,                    # 分类任务的类别数 (例如动作数量)\n", "    num_transformer_layers=6,        # Transformer层数\n", "    classifier_dropout_rate=0.1\n", ")\n", "\n", "# 训练模型\n", "trained_model = train_model(\n", "    model,\n", "    train_matrix_file,\n", "    train_label_file,\n", "    epochs=500,\n", "    batch_size=64,\n", "    lr= 3e-4,\n", "    weight_decay=1e-4,\n", "    model_path=model_path,\n", "    orig_shape=base_shape,\n", "    new_shape=base_shape,  \n", "    val_split_ratio=0.2,\n", "    patience=20,\n", "    label_smoothing_epsilon=0.1,\n", ")\n", "\n", "print('--- 正在评估训练集数据 ---')\n", "evaluate_model(\n", "    trained_model,\n", "    train_matrix_file,\n", "    train_label_file,\n", "    orig_shape=base_shape,                 # 传递基础形状\n", "    new_shape=base_shape,           # 传递最终输入形状\n", "    batch_size=32,\n", ")\n", "\n", "print('\\n--- 正在评估测试集数据 ---')\n", "evaluate_model(\n", "    trained_model,\n", "    test_matrix_file,\n", "    test_label_file,\n", "    orig_shape=base_shape,                 # 传递基础形状\n", "    new_shape=base_shape,           # 传递最终输入形状\n", "    batch_size=32,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8fddfe6d-839e-448e-8eb0-e22afaba6b7d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "pre_marshalling"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}