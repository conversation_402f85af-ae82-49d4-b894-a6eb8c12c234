{"cells": [{"cell_type": "code", "execution_count": 1, "id": "adb939d5-f2da-409e-a7c5-57f68f4e476d", "metadata": {}, "outputs": [], "source": ["import random\n", "import json\n", "import string\n", "\n", "\n", "def generate_stack_states(blocks):\n", "    \"\"\"\n", "    Generate random start and goal states for a block stacking problem with 4 stacks (max 5 blocks each) and an empty Stack5.\n", "\n", "    Args:\n", "        blocks (int): Number of blocks to distribute (1 to 20).\n", "\n", "    Returns:\n", "        dict: JSON-compatible dictionary with 'start_state' and 'goal_state', each containing 5 stacks.\n", "    \"\"\"\n", "    # Validate blocks input\n", "    if not isinstance(blocks, int) or blocks < 1 or blocks > 20:\n", "        raise ValueError(\"Blocks must be an integer between 1 and 20\")\n", "\n", "    # Generate block labels (A, B, C, ...)\n", "    block_labels = list(string.ascii_uppercase[:blocks])\n", "\n", "    # Initialize stacks\n", "    def create_random_state():\n", "        stacks = {'Stack1': [], 'Stack2': [], 'Stack3': [], 'Stack4': []}\n", "        # Shuffle blocks\n", "        shuffled_blocks = block_labels.copy()\n", "        random.shuffle(shuffled_blocks)\n", "\n", "        # Distribute blocks to Stack1-Stack4, respecting 5-block height limit\n", "        available_stacks = ['Stack1', 'Stack2', 'Stack3']\n", "        for block in shuffled_blocks:\n", "            # Filter stacks with less than 5 blocks\n", "            valid_stacks = [s for s in available_stacks if len(stacks[s]) < 4]\n", "            if not valid_stacks:\n", "                break\n", "            # Randomly choose a stack\n", "            chosen_stack = random.choice(valid_stacks)\n", "            stacks[chosen_stack].append(block)\n", "\n", "        return stacks\n", "\n", "    # Generate start and goal states\n", "    start_state = create_random_state()\n", "    goal_state = create_random_state()\n", "\n", "    # Ensure goal state has the same blocks by redistributing if necessary\n", "    # (In case some blocks were not placed due to stack limits)\n", "    start_blocks = sorted([b for stack in start_state.values() for b in stack])\n", "    goal_blocks = sorted([b for stack in goal_state.values() for b in stack])\n", "    if start_blocks != goal_blocks:\n", "        goal_state = create_random_state()\n", "\n", "    return {\n", "        'start_state': start_state,\n", "        'goal_state': goal_state\n", "    }"]}, {"cell_type": "code", "execution_count": 1, "id": "2db875f4-0dc2-4b02-9524-29e02658508b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/envs/pre_marshalling/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pre_marshalling_astar_neural_dynamic_weights import GraphPlanningBlocksWorld\n", "from pre_marshalling_llm import LLMGuidance\n", "import time"]}, {"cell_type": "code", "execution_count": 16, "id": "bf115a28-0245-424d-8a7e-c36a00bdf178", "metadata": {}, "outputs": [], "source": ["start_state={'Stack1': ['I', 'G'],\n", "  'Stack2': ['J', 'A', 'B', 'E'],\n", "  'Stack3': ['F', 'D', 'H', 'C'],\n", "  'Stack4': []}\n", "goal_state={'Stack1': ['J', 'I', 'H', 'B'],\n", "  'Stack2': ['A', 'G'],\n", "  'Stack3': ['F', 'C', 'D', 'E'],\n", "  'Stack4': []}"]}, {"cell_type": "code", "execution_count": 17, "id": "93b08db5-8c1d-4f5a-814e-f162784b4a7a", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(2, 4), (2, 4), (2, 4), (2, 3), (4, 2), (1, 2), (1, 4), (3, 1), (4, 1), (3, 2), (3, 1), (4, 1), (3, 4), (2, 3), (4, 3), (4, 3)]\n", "Nodes searched: 381319\n", "Path length: 16\n", "\n", "Without LLM - Time consumption: 102.59563636779785 seconds\n", "Without LLM - Solution found: [(2, 4), (2, 4), (2, 4), (2, 3), (4, 2), (1, 2), (1, 4), (3, 1), (4, 1), (3, 2), (3, 1), (4, 1), (3, 4), (2, 3), (4, 3), (4, 3)]\n", "Without LLM - Nodes searched: 381319\n"]}], "source": ["log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 3, "id": "6a36495e-91e0-4b40-89f8-31982a8e31ca", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['C', 'A', 'E', 'F'], 'Stack2': ['D', 'I', 'G'], 'Stack3': ['J', 'B', 'H'], 'Stack4': []}\n", "goal_state={'Stack1': ['H', 'A', 'C', 'D'], 'Stack2': ['G', 'E', 'F', 'B'], 'Stack3': ['J', 'I'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(2, 1), (2, 4), (2, 4), (1, 2), (1, 4), (1, 2), (4, 2), (1, 4), (1, 2), (3, 1), (4, 1), (2, 1), (3, 2), (4, 1), (4, 3)]\n", "Nodes searched: 125002\n", "Path length: 15\n", "\n", "Without LLM - Time consumption: 37.41545915603638 seconds\n", "Without LLM - Solution found: [(2, 1), (2, 4), (2, 4), (1, 2), (1, 4), (1, 2), (4, 2), (1, 4), (1, 2), (3, 1), (4, 1), (2, 1), (3, 2), (4, 1), (4, 3)]\n", "Without LLM - Nodes searched: 125002\n", "start_state={'Stack1': ['D', 'C', 'I'], 'Stack2': ['E', 'A', 'J'], 'Stack3': ['G', 'B', 'F', 'H'], 'Stack4': []}\n", "goal_state={'Stack1': ['A', 'I', 'B', 'E'], 'Stack2': ['J', 'G'], 'Stack3': ['H', 'D', 'F', 'C'], 'Stack4': []}\n", "Running A* without guidance:\n", "搜索达到最大迭代次数 500000，未找到解决方案。\n", "已搜索节点数：500000\n", "Without LLM - Time consumption: 147.98489356040955 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 500000\n", "start_state={'Stack1': ['I', 'J', 'B', 'A'], 'Stack2': ['F', 'C', 'D', 'E'], 'Stack3': ['H', 'G'], 'Stack4': []}\n", "goal_state={'Stack1': ['C', 'I', 'E', 'G'], 'Stack2': ['J', 'H', 'D', 'A'], 'Stack3': ['F', 'B'], 'Stack4': []}\n", "Running A* without guidance:\n", "搜索达到最大迭代次数 500000，未找到解决方案。\n", "已搜索节点数：500000\n", "Without LLM - Time consumption: 130.83210229873657 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 500000\n", "start_state={'Stack1': ['I', 'A', 'F', 'E'], 'Stack2': ['C', 'D'], 'Stack3': ['J', 'H', 'B', 'G'], 'Stack4': []}\n", "goal_state={'Stack1': ['F', 'E', 'G'], 'Stack2': ['C', 'H', 'I', 'B'], 'Stack3': ['D', 'A', 'J'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(2, 1), (3, 4), (3, 4), (3, 2), (3, 4), (1, 3), (1, 4), (1, 4), (1, 3), (1, 2), (4, 1), (4, 1), (4, 3), (4, 2), (4, 1)]\n", "Nodes searched: 96872\n", "Path length: 15\n", "\n", "Without LLM - Time consumption: 22.352869987487793 seconds\n", "Without LLM - Solution found: [(2, 1), (3, 4), (3, 4), (3, 2), (3, 4), (1, 3), (1, 4), (1, 4), (1, 3), (1, 2), (4, 1), (4, 1), (4, 3), (4, 2), (4, 1)]\n", "Without LLM - Nodes searched: 96872\n"]}], "source": ["for i in range(4):\n", "    start_state, goal_state=generate_stack_states(10)['start_state'],generate_stack_states(10)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    log_file='A*_test.txt'\n", "    start = time.time()\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Without LLM - Solution found:\", solution)\n", "    print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 4, "id": "3f74d397-539b-4419-bf35-fdeb1d1729d5", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['D', 'B', 'E', 'G'], 'Stack2': ['H', 'A', 'J', 'I'], 'Stack3': ['C', 'F'], 'Stack4': []}\n", "goal_state={'Stack1': ['G', 'E', 'F'], 'Stack2': ['B', 'A', 'D', 'I'], 'Stack3': ['C', 'J', 'H'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(3, 4), (2, 4), (2, 3), (1, 4), (2, 4), (2, 3), (1, 3), (1, 2), (4, 2), (1, 2), (4, 1), (3, 1), (4, 2), (4, 1)]\n", "Nodes searched: 26362\n", "Path length: 14\n", "\n", "Without LLM - Time consumption: 5.726274490356445 seconds\n", "Without LLM - Solution found: [(3, 4), (2, 4), (2, 3), (1, 4), (2, 4), (2, 3), (1, 3), (1, 2), (4, 2), (1, 2), (4, 1), (3, 1), (4, 2), (4, 1)]\n", "Without LLM - Nodes searched: 26362\n", "start_state={'Stack1': ['F', 'J'], 'Stack2': ['E', 'A', 'G', 'B'], 'Stack3': ['I', 'H', 'D', 'C'], 'Stack4': []}\n", "goal_state={'Stack1': ['A', 'D', 'I'], 'Stack2': ['B', 'F', 'H'], 'Stack3': ['G', 'E', 'C', 'J'], 'Stack4': []}\n", "Running A* without guidance:\n", "搜索达到最大迭代次数 500000，未找到解决方案。\n", "已搜索节点数：500000\n", "Without LLM - Time consumption: 148.40306615829468 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 500000\n", "start_state={'Stack1': ['G', 'C', 'F'], 'Stack2': ['J', 'I', 'D'], 'Stack3': ['A', 'E', 'B', 'H'], 'Stack4': []}\n", "goal_state={'Stack1': ['C', 'A', 'G'], 'Stack2': ['B', 'H', 'E'], 'Stack3': ['I', 'J', 'F', 'D'], 'Stack4': []}\n", "Running A* without guidance:\n", "搜索达到最大迭代次数 500000，未找到解决方案。\n", "已搜索节点数：500000\n", "Without LLM - Time consumption: 134.2378363609314 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 500000\n", "start_state={'Stack1': ['C', 'I'], 'Stack2': ['B', 'E', 'G', 'A'], 'Stack3': ['J', 'D', 'F', 'H'], 'Stack4': []}\n", "goal_state={'Stack1': ['I', 'G', 'C', 'E'], 'Stack2': ['D', 'H', 'F', 'A'], 'Stack3': ['J', 'B'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(1, 2), (1, 3), (2, 1), (2, 4), (2, 1), (3, 1), (2, 1), (3, 4), (3, 1), (3, 1), (2, 3), (1, 2), (4, 2), (1, 2), (4, 2)]\n", "Nodes searched: 132752\n", "Path length: 15\n", "\n", "Without LLM - Time consumption: 33.32074761390686 seconds\n", "Without LLM - Solution found: [(1, 2), (1, 3), (2, 1), (2, 4), (2, 1), (3, 1), (2, 1), (3, 4), (3, 1), (3, 1), (2, 3), (1, 2), (4, 2), (1, 2), (4, 2)]\n", "Without LLM - Nodes searched: 132752\n"]}], "source": ["for i in range(4):\n", "    start_state, goal_state=generate_stack_states(10)['start_state'],generate_stack_states(10)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    log_file='A*_test.txt'\n", "    start = time.time()\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Without LLM - Solution found:\", solution)\n", "    print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 6, "id": "d9eabc59-bd96-439f-9455-501e51880daf", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['A', 'J', 'G', 'C'], 'Stack2': ['B', 'H', 'I', 'F'], 'Stack3': ['D', 'E'], 'Stack4': []}\n", "goal_state={'Stack1': ['D', 'A', 'F', 'I'], 'Stack2': ['J', 'E', 'B', 'H'], 'Stack3': ['G', 'C'], 'Stack4': []}\n", "Running A* without guidance:\n", "搜索达到最大迭代次数 500000，未找到解决方案。\n", "已搜索节点数：500000\n", "Without LLM - Time consumption: 141.71612930297852 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 500000\n", "start_state={'Stack1': ['J', 'A', 'H'], 'Stack2': ['E', 'C', 'G', 'F'], 'Stack3': ['I', 'B', 'D'], 'Stack4': []}\n", "goal_state={'Stack1': ['G', 'B', 'J', 'F'], 'Stack2': ['C', 'A'], 'Stack3': ['H', 'I', 'E', 'D'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(3, 4), (2, 4), (3, 4), (3, 4), (1, 3), (4, 3), (2, 4), (2, 4), (2, 3), (4, 2), (1, 2), (1, 3), (4, 1), (4, 1), (3, 1), (4, 1), (4, 3)]\n", "Nodes searched: 487481\n", "Path length: 17\n", "\n", "Without LLM - Time consumption: 129.0499782562256 seconds\n", "Without LLM - Solution found: [(3, 4), (2, 4), (3, 4), (3, 4), (1, 3), (4, 3), (2, 4), (2, 4), (2, 3), (4, 2), (1, 2), (1, 3), (4, 1), (4, 1), (3, 1), (4, 1), (4, 3)]\n", "Without LLM - Nodes searched: 487481\n", "start_state={'Stack1': ['A', 'B', 'D'], 'Stack2': ['E', 'I', 'H', 'C'], 'Stack3': ['J', 'F', 'G'], 'Stack4': []}\n", "goal_state={'Stack1': ['I', 'A', 'E', 'J'], 'Stack2': ['C', 'H', 'G'], 'Stack3': ['B', 'D', 'F'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(2, 3), (1, 4), (1, 4), (2, 4), (1, 4), (2, 1), (4, 1), (2, 1), (3, 2), (4, 2), (3, 2), (3, 2), (3, 1), (4, 3), (4, 3), (2, 3)]\n", "Nodes searched: 196768\n", "Path length: 16\n", "\n", "Without LLM - Time consumption: 48.43359613418579 seconds\n", "Without LLM - Solution found: [(2, 3), (1, 4), (1, 4), (2, 4), (1, 4), (2, 1), (4, 1), (2, 1), (3, 2), (4, 2), (3, 2), (3, 2), (3, 1), (4, 3), (4, 3), (2, 3)]\n", "Without LLM - Nodes searched: 196768\n", "start_state={'Stack1': ['D', 'I'], 'Stack2': ['G', 'A', 'F', 'H'], 'Stack3': ['E', 'C', 'B', 'J'], 'Stack4': []}\n", "goal_state={'Stack1': ['D', 'F', 'G', 'J'], 'Stack2': ['E', 'H', 'B', 'I'], 'Stack3': ['C', 'A'], 'Stack4': []}\n", "Running A* without guidance:\n", "Solution found: [(1, 4), (2, 4), (2, 1), (2, 4), (2, 1), (3, 1), (3, 1), (3, 4), (3, 2), (4, 3), (4, 3), (4, 2), (1, 2), (4, 2)]\n", "Nodes searched: 22203\n", "Path length: 14\n", "\n", "Without LLM - Time consumption: 5.684441089630127 seconds\n", "Without LLM - Solution found: [(1, 4), (2, 4), (2, 1), (2, 4), (2, 1), (3, 1), (3, 1), (3, 4), (3, 2), (4, 3), (4, 3), (4, 2), (1, 2), (4, 2)]\n", "Without LLM - Nodes searched: 22203\n"]}], "source": ["for i in range(4):\n", "    start_state, goal_state=generate_stack_states(10)['start_state'],generate_stack_states(10)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    log_file='A*_test.txt'\n", "    start = time.time()\n", "    planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    print(\"Without LLM - Solution found:\", solution)\n", "    print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 16, "id": "177e3fb2-b7f6-4b77-96d1-fbbbb745374f", "metadata": {}, "outputs": [], "source": ["import random\n", "import json\n", "import string\n", "\n", "\n", "def generate_stack_states(blocks):\n", "    \"\"\"\n", "    Generate random start and goal states for a block stacking problem with 4 stacks (max 5 blocks each) and an empty Stack5.\n", "\n", "    Args:\n", "        blocks (int): Number of blocks to distribute (1 to 20).\n", "\n", "    Returns:\n", "        dict: JSON-compatible dictionary with 'start_state' and 'goal_state', each containing 5 stacks.\n", "    \"\"\"\n", "    # Validate blocks input\n", "    if not isinstance(blocks, int) or blocks < 1 or blocks > 20:\n", "        raise ValueError(\"Blocks must be an integer between 1 and 20\")\n", "\n", "    # Generate block labels (A, B, C, ...)\n", "    block_labels = list(string.ascii_uppercase[:blocks])\n", "\n", "    # Initialize stacks\n", "    def create_random_state():\n", "        stacks = {'Stack1': [], 'Stack2': [], 'Stack3': [], 'Stack4': []}\n", "        # Shuffle blocks\n", "        shuffled_blocks = block_labels.copy()\n", "        random.shuffle(shuffled_blocks)\n", "\n", "        # Distribute blocks to Stack1-Stack4, respecting 5-block height limit\n", "        available_stacks = ['Stack1', 'Stack2', 'Stack3','Stack4']\n", "        for block in shuffled_blocks:\n", "            # Filter stacks with less than 5 blocks\n", "            valid_stacks = [s for s in available_stacks if len(stacks[s]) < 5]\n", "            if not valid_stacks:\n", "                break\n", "            # Randomly choose a stack\n", "            chosen_stack = random.choice(valid_stacks)\n", "            stacks[chosen_stack].append(block)\n", "\n", "        return stacks\n", "\n", "    # Generate start and goal states\n", "    start_state = create_random_state()\n", "    goal_state = create_random_state()\n", "\n", "    # Ensure goal state has the same blocks by redistributing if necessary\n", "    # (In case some blocks were not placed due to stack limits)\n", "    start_blocks = sorted([b for stack in start_state.values() for b in stack])\n", "    goal_blocks = sorted([b for stack in goal_state.values() for b in stack])\n", "    if start_blocks != goal_blocks:\n", "        goal_state = create_random_state()\n", "\n", "    return {\n", "        'start_state': start_state,\n", "        'goal_state': goal_state\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "622d255c-7381-4b1c-97c0-f6afbd67be29", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "c2942fea-f09c-412c-80e1-9a792cd4830b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['A', 'M', 'D', 'I'], 'Stack2': ['N', 'F'], 'Stack3': ['C', 'L', 'G', 'H', 'B'], 'Stack4': ['E', 'K', 'O', 'J', 'P'], 'Stack5': []}\n", "goal_state={'Stack1': ['J', 'O', 'C', 'L'], 'Stack2': ['A', 'F', 'P', 'G', 'D'], 'Stack3': ['B', 'H'], 'Stack4': ['K', 'E', 'I', 'M', 'N'], 'Stack5': []}\n", "------\n", "start_state={'Stack1': ['D', 'C', 'I', 'G', 'K'], 'Stack2': ['H', 'L', 'M'], 'Stack3': ['N', 'F', 'A'], 'Stack4': ['P', 'B', 'J', 'O', 'E'], 'Stack5': []}\n", "goal_state={'Stack1': ['C', 'E', 'D', 'O'], 'Stack2': ['L', 'K'], 'Stack3': ['P', 'B', 'N', 'H', 'I'], 'Stack4': ['A', 'F', 'G', 'M', 'J'], 'Stack5': []}\n", "------\n", "start_state={'Stack1': ['E', 'C', 'L'], 'Stack2': ['P', 'K', 'I', 'H', 'A'], 'Stack3': ['J', 'B', 'G', 'N', 'F'], 'Stack4': ['M', 'D', 'O'], 'Stack5': []}\n", "goal_state={'Stack1': ['A', 'N', 'M', 'I', 'E'], 'Stack2': ['D', 'P', 'H'], 'Stack3': ['J', 'F', 'G'], 'Stack4': ['L', 'K', 'O', 'B', 'C'], 'Stack5': []}\n", "------\n", "start_state={'Stack1': ['D', 'L', 'F', 'P'], 'Stack2': ['J', 'H', 'A', 'I'], 'Stack3': ['E', 'M', 'K', 'C', 'B'], 'Stack4': ['O', 'G', 'N'], 'Stack5': []}\n", "goal_state={'Stack1': ['P', 'O', 'D', 'C', 'I'], 'Stack2': ['B', 'F', 'M', 'L', 'J'], 'Stack3': ['G', 'E', 'H', 'A'], 'Stack4': ['N', 'K'], 'Stack5': []}\n", "------\n", "start_state={'Stack1': ['E', 'J', 'I', 'A'], 'Stack2': ['H', 'B', 'K'], 'Stack3': ['P', 'O', 'M', 'C', 'D'], 'Stack4': ['N', 'G', 'F', 'L'], 'Stack5': []}\n", "goal_state={'Stack1': ['P', 'H', 'D', 'J', 'C'], 'Stack2': ['K'], 'Stack3': ['L', 'N', 'O', 'G', 'M'], 'Stack4': ['F', 'B', 'E', 'I', 'A'], 'Stack5': []}\n", "------\n"]}], "source": ["for i in range(5):\n", "    start_state, goal_state=generate_stack_states(16)['start_state'],generate_stack_states(16)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    print('------')\n", "    # log_file='A*_test.txt'\n", "    # start = time.time()\n", "    # planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    # solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    # print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    # print(\"Without LLM - Solution found:\", solution)\n", "    # print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 18, "id": "0c5a1841-a725-484f-a927-abd0cb9d879c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['D', 'C', 'J', 'H', 'K'], 'Stack2': ['B', 'G'], 'Stack3': ['L', 'F', 'I', 'E'], 'Stack4': ['A']}\n", "goal_state={'Stack1': ['J', 'B', 'E', 'A'], 'Stack2': ['I'], 'Stack3': ['F', 'K', 'G', 'H', 'D'], 'Stack4': ['C', 'L']}\n", "------\n", "start_state={'Stack1': ['I', 'A', 'H', 'G'], 'Stack2': ['B', 'L'], 'Stack3': ['F', 'E', 'J'], 'Stack4': ['C', 'D', 'K']}\n", "goal_state={'Stack1': ['A', 'F', 'J', 'G', 'B'], 'Stack2': ['C', 'D', 'H'], 'Stack3': ['I', 'L'], 'Stack4': ['E', 'K']}\n", "------\n", "start_state={'Stack1': ['H', 'E', 'F'], 'Stack2': ['G', 'J', 'L'], 'Stack3': ['B', 'K', 'D', 'A'], 'Stack4': ['I', 'C']}\n", "goal_state={'Stack1': ['D', 'I', 'E', 'G'], 'Stack2': ['B', 'A'], 'Stack3': ['K', 'F'], 'Stack4': ['L', 'H', 'C', 'J']}\n", "------\n", "start_state={'Stack1': ['C', 'H'], 'Stack2': ['J', 'I', 'F', 'E', 'B'], 'Stack3': ['A'], 'Stack4': ['K', 'D', 'L', 'G']}\n", "goal_state={'Stack1': ['H', 'E', 'K', 'G', 'J'], 'Stack2': ['D', 'B', 'F', 'C'], 'Stack3': [], 'Stack4': ['I', 'A', 'L']}\n", "------\n", "start_state={'Stack1': ['F', 'L'], 'Stack2': ['A', 'D', 'I', 'B', 'H'], 'Stack3': ['K'], 'Stack4': ['J', 'C', 'G', 'E']}\n", "goal_state={'Stack1': ['F', 'J', 'A'], 'Stack2': ['C', 'I'], 'Stack3': ['G', 'E', 'H', 'D', 'B'], 'Stack4': ['L', 'K']}\n", "------\n"]}], "source": ["for i in range(5):\n", "    start_state, goal_state=generate_stack_states(12)['start_state'],generate_stack_states(12)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    print('------')\n", "    # log_file='A*_test.txt'\n", "    # start = time.time()\n", "    # planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    # solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    # print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    # print(\"Without LLM - Solution found:\", solution)\n", "    # print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 24, "id": "a4188e51-3843-49c1-82b4-86dacbc03d2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['B', 'J'], 'Stack2': ['I', 'H'], 'Stack3': ['E', 'G', 'A', 'F', 'C'], 'Stack4': ['D']}\n", "goal_state={'Stack1': ['C', 'F', 'H', 'D', 'E'], 'Stack2': ['B'], 'Stack3': ['J', 'G'], 'Stack4': ['A', 'I']}\n", "------\n", "start_state={'Stack1': ['C', 'E'], 'Stack2': ['F'], 'Stack3': ['J', 'B', 'D'], 'Stack4': ['H', 'G', 'I', 'A']}\n", "goal_state={'Stack1': ['D', 'H'], 'Stack2': ['G', 'B', 'J', 'F', 'E'], 'Stack3': ['C', 'A'], 'Stack4': ['I']}\n", "------\n", "start_state={'Stack1': ['B', 'D', 'E', 'H'], 'Stack2': [], 'Stack3': ['G'], 'Stack4': ['C', 'A', 'F', 'J', 'I']}\n", "goal_state={'Stack1': ['F', 'G', 'C'], 'Stack2': ['A'], 'Stack3': ['H', 'E', 'J'], 'Stack4': ['D', 'B', 'I']}\n", "------\n", "start_state={'Stack1': ['I', 'G', 'D'], 'Stack2': ['E', 'H'], 'Stack3': ['A', 'F', 'B', 'J'], 'Stack4': ['C']}\n", "goal_state={'Stack1': ['F', 'E', 'I', 'B'], 'Stack2': ['G'], 'Stack3': ['D', 'H'], 'Stack4': ['A', 'C', 'J']}\n", "------\n", "start_state={'Stack1': ['F'], 'Stack2': ['C', 'I'], 'Stack3': ['G', 'H', 'D', 'E'], 'Stack4': ['B', 'J', 'A']}\n", "goal_state={'Stack1': ['G'], 'Stack2': [], 'Stack3': ['D', 'B', 'C', 'F'], 'Stack4': ['A', 'H', 'J', 'E', 'I']}\n", "------\n"]}], "source": ["for i in range(5):\n", "    start_state, goal_state=generate_stack_states(10)['start_state'],generate_stack_states(10)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    print('------')\n", "    # log_file='A*_test.txt'\n", "    # start = time.time()\n", "    # planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    # solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    # print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    # print(\"Without LLM - Solution found:\", solution)\n", "    # print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 25, "id": "18ff2305-a10a-42b7-8577-e7891d5311b6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(2, 4), (1, 2), (1, 2), (3, 1), (3, 1), (4, 1), (4, 1), (3, 4), (3, 4), (3, 1), (2, 1), (2, 3), (4, 3), (2, 4), (1, 2)]\n", "Nodes searched: 51388\n", "Path length: 15\n", "\n", "Without LLM - Time consumption: 7.1910905838012695 seconds\n", "Without LLM - Solution found: [(2, 4), (1, 2), (1, 2), (3, 1), (3, 1), (4, 1), (4, 1), (3, 4), (3, 4), (3, 1), (2, 1), (2, 3), (4, 3), (2, 4), (1, 2)]\n", "Without LLM - Nodes searched: 51388\n"]}], "source": ["start_state={'Stack1': ['B', 'J'], 'Stack2': ['I', 'H'], 'Stack3': ['E', 'G', 'A', 'F', 'C'], 'Stack4': ['D']}\n", "goal_state={'Stack1': ['C', 'F', 'H', 'D', 'E'], 'Stack2': ['B'], 'Stack3': ['J', 'G'], 'Stack4': ['A', 'I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 27, "id": "365dc03d-dcf1-4ebf-9198-e1626f151867", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(4, 1), (2, 1), (4, 3), (4, 2), (3, 4), (3, 4), (3, 2), (3, 2), (1, 2), (1, 4), (1, 2), (1, 3), (4, 3), (4, 1), (4, 2), (4, 1), (2, 4)]\n", "Nodes searched: 777673\n", "Path length: 17\n", "\n", "Without LLM - Time consumption: 224.5715069770813 seconds\n", "Without LLM - Solution found: [(4, 1), (2, 1), (4, 3), (4, 2), (3, 4), (3, 4), (3, 2), (3, 2), (1, 2), (1, 4), (1, 2), (1, 3), (4, 3), (4, 1), (4, 2), (4, 1), (2, 4)]\n", "Without LLM - Nodes searched: 777673\n"]}], "source": ["start_state={'Stack1': ['C', 'E'], 'Stack2': ['F'], 'Stack3': ['J', 'B', 'D'], 'Stack4': ['H', 'G', 'I', 'A']}\n", "goal_state={'Stack1': ['D', 'H'], 'Stack2': ['G', 'B', 'J', 'F', 'E'], 'Stack3': ['C', 'A'], 'Stack4': ['I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 28, "id": "b58b28cf-3613-4edb-906d-1eeeb63fe3da", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(3, 2), (1, 3), (1, 3), (4, 2), (4, 3), (4, 2), (4, 3), (4, 3), (1, 4), (1, 4), (2, 1), (2, 4), (2, 1), (3, 1), (3, 2)]\n", "Nodes searched: 32280\n", "Path length: 15\n", "\n", "Without LLM - Time consumption: 4.508391618728638 seconds\n", "Without LLM - Solution found: [(3, 2), (1, 3), (1, 3), (4, 2), (4, 3), (4, 2), (4, 3), (4, 3), (1, 4), (1, 4), (2, 1), (2, 4), (2, 1), (3, 1), (3, 2)]\n", "Without LLM - Nodes searched: 32280\n"]}], "source": ["start_state={'Stack1': ['B', 'D', 'E', 'H'], 'Stack2': [], 'Stack3': ['G'], 'Stack4': ['C', 'A', 'F', 'J', 'I']}\n", "goal_state={'Stack1': ['F', 'G', 'C'], 'Stack2': ['A'], 'Stack3': ['H', 'E', 'J'], 'Stack4': ['D', 'B', 'I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 29, "id": "a214e2e3-5409-43d1-9e6b-5667fb17b0c0", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "搜索达到最大迭代次数 1000000，未找到解决方案。\n", "已搜索节点数：1000000\n", "Without LLM - Time consumption: 280.************ seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 1000000\n"]}], "source": ["start_state={'Stack1': ['I', 'G', 'D'], 'Stack2': ['E', 'H'], 'Stack3': ['A', 'F', 'B', 'J'], 'Stack4': ['C']}\n", "goal_state={'Stack1': ['F', 'E', 'I', 'B'], 'Stack2': ['G'], 'Stack3': ['D', 'H'], 'Stack4': ['A', 'C', 'J']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 30, "id": "60750e9c-69f4-4a95-a2ed-8c17f0f38e44", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(3, 2), (4, 3), (4, 2), (4, 1), (3, 4), (3, 1), (3, 4), (2, 4), (2, 4), (2, 4), (3, 4), (1, 3), (1, 3), (2, 3), (1, 3), (4, 1)]\n", "Nodes searched: 189745\n", "Path length: 16\n", "\n", "Without LLM - Time consumption: 39.02691149711609 seconds\n", "Without LLM - Solution found: [(3, 2), (4, 3), (4, 2), (4, 1), (3, 4), (3, 1), (3, 4), (2, 4), (2, 4), (2, 4), (3, 4), (1, 3), (1, 3), (2, 3), (1, 3), (4, 1)]\n", "Without LLM - Nodes searched: 189745\n"]}], "source": ["start_state={'Stack1': ['F'], 'Stack2': ['C', 'I'], 'Stack3': ['G', 'H', 'D', 'E'], 'Stack4': ['B', 'J', 'A']}\n", "goal_state={'Stack1': ['G'], 'Stack2': [], 'Stack3': ['D', 'B', 'C', 'F'], 'Stack4': ['A', 'H', 'J', 'E', 'I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 31, "id": "b997369c-9a06-470f-b7dc-d8e127d3e6c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['H', 'A', 'E'], 'Stack2': ['G', 'C', 'B', 'K', 'I'], 'Stack3': ['L'], 'Stack4': ['J', 'D', 'F']}\n", "goal_state={'Stack1': ['L', 'K', 'D'], 'Stack2': ['C', 'I', 'E', 'B'], 'Stack3': ['A', 'F', 'G'], 'Stack4': ['J', 'H']}\n", "------\n", "start_state={'Stack1': ['C'], 'Stack2': ['B', 'G', 'K', 'D'], 'Stack3': ['L', 'E', 'A', 'J'], 'Stack4': ['I', 'F', 'H']}\n", "goal_state={'Stack1': ['D', 'A', 'B', 'I', 'E'], 'Stack2': ['H', 'C'], 'Stack3': ['G'], 'Stack4': ['L', 'J', 'K', 'F']}\n", "------\n", "start_state={'Stack1': ['H', 'J', 'C', 'B', 'I'], 'Stack2': ['D', 'A', 'F', 'L'], 'Stack3': [], 'Stack4': ['G', 'E', 'K']}\n", "goal_state={'Stack1': ['D', 'K'], 'Stack2': ['F', 'C', 'J'], 'Stack3': ['B', 'E', 'G', 'A', 'H'], 'Stack4': ['L', 'I']}\n", "------\n", "start_state={'Stack1': ['J', 'B', 'A', 'I', 'E'], 'Stack2': ['K'], 'Stack3': ['C', 'D', 'G', 'L'], 'Stack4': ['F', 'H']}\n", "goal_state={'Stack1': ['F', 'A'], 'Stack2': ['B'], 'Stack3': ['G', 'C', 'L', 'J', 'K'], 'Stack4': ['E', 'H', 'D', 'I']}\n", "------\n", "start_state={'Stack1': ['G', 'A', 'C', 'L'], 'Stack2': ['K', 'D'], 'Stack3': ['E', 'F'], 'Stack4': ['B', 'J', 'I', 'H']}\n", "goal_state={'Stack1': ['K', 'A', 'J', 'E'], 'Stack2': ['G', 'I'], 'Stack3': ['C', 'D', 'B'], 'Stack4': ['F', 'H', 'L']}\n", "------\n"]}], "source": ["for i in range(5):\n", "    start_state, goal_state=generate_stack_states(12)['start_state'],generate_stack_states(12)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    print('------')\n", "    # log_file='A*_test.txt'\n", "    # start = time.time()\n", "    # planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    # solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    # print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    # print(\"Without LLM - Solution found:\", solution)\n", "    # print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 32, "id": "692f7d2c-071d-40b7-a316-ac66fa3807a6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "搜索达到最大迭代次数 1000000，未找到解决方案。\n", "已搜索节点数：1000000\n", "Without LLM - Time consumption: 309.07099652290344 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 1000000\n"]}], "source": ["start_state={'Stack1': ['H', 'A', 'E'], 'Stack2': ['G', 'C', 'B', 'K', 'I'], 'Stack3': ['L'], 'Stack4': ['J', 'D', 'F']}\n", "goal_state={'Stack1': ['L', 'K', 'D'], 'Stack2': ['C', 'I', 'E', 'B'], 'Stack3': ['A', 'F', 'G'], 'Stack4': ['J', 'H']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 33, "id": "1149b9dd-ec47-4a1b-8607-7e215022a8d0", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "搜索达到最大迭代次数 1000000，未找到解决方案。\n", "已搜索节点数：1000000\n", "Without LLM - Time consumption: 300.7076382637024 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 1000000\n"]}], "source": ["start_state={'Stack1': ['C'], 'Stack2': ['B', 'G', 'K', 'D'], 'Stack3': ['L', 'E', 'A', 'J'], 'Stack4': ['I', 'F', 'H']}\n", "goal_state={'Stack1': ['D', 'A', 'B', 'I', 'E'], 'Stack2': ['H', 'C'], 'Stack3': ['G'], 'Stack4': ['L', 'J', 'K', 'F']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 34, "id": "7399fd36-6a30-4c9b-a158-b629e0f4615a", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(1, 2), (1, 3), (4, 1), (4, 3), (4, 3), (2, 3), (2, 4), (3, 4), (1, 4), (2, 1), (2, 3), (2, 4), (1, 2), (1, 2), (1, 2), (1, 3), (4, 1), (4, 1)]\n", "Nodes searched: 301815\n", "Path length: 18\n", "\n", "Without LLM - Time consumption: 80.81282615661621 seconds\n", "Without LLM - Solution found: [(1, 2), (1, 3), (4, 1), (4, 3), (4, 3), (2, 3), (2, 4), (3, 4), (1, 4), (2, 1), (2, 3), (2, 4), (1, 2), (1, 2), (1, 2), (1, 3), (4, 1), (4, 1)]\n", "Without LLM - Nodes searched: 301815\n"]}], "source": ["start_state={'Stack1': ['H', 'J', 'C', 'B', 'I'], 'Stack2': ['D', 'A', 'F', 'L'], 'Stack3': [], 'Stack4': ['G', 'E', 'K']}\n", "goal_state={'Stack1': ['D', 'K'], 'Stack2': ['F', 'C', 'J'], 'Stack3': ['B', 'E', 'G', 'A', 'H'], 'Stack4': ['L', 'I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 35, "id": "9761c65d-71c5-4cf5-80ba-6cdc1cd6465f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "Solution found: [(4, 3), (4, 2), (1, 4), (3, 4), (3, 2), (3, 2), (3, 4), (1, 4), (3, 4), (2, 3), (4, 3), (2, 3), (1, 4), (1, 4), (1, 3), (2, 1), (2, 3), (4, 2), (4, 1)]\n", "Nodes searched: 958022\n", "Path length: 19\n", "\n", "Without LLM - Time consumption: 299.7553973197937 seconds\n", "Without LLM - Solution found: [(4, 3), (4, 2), (1, 4), (3, 4), (3, 2), (3, 2), (3, 4), (1, 4), (3, 4), (2, 3), (4, 3), (2, 3), (1, 4), (1, 4), (1, 3), (2, 1), (2, 3), (4, 2), (4, 1)]\n", "Without LLM - Nodes searched: 958022\n"]}], "source": ["start_state={'Stack1': ['J', 'B', 'A', 'I', 'E'], 'Stack2': ['K'], 'Stack3': ['C', 'D', 'G', 'L'], 'Stack4': ['F', 'H']}\n", "goal_state={'Stack1': ['F', 'A'], 'Stack2': ['B'], 'Stack3': ['G', 'C', 'L', 'J', 'K'], 'Stack4': ['E', 'H', 'D', 'I']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 36, "id": "5ccbbb76-8db7-4195-88c9-1cfc1a075400", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running A* without guidance:\n", "搜索达到最大迭代次数 1000000，未找到解决方案。\n", "已搜索节点数：1000000\n", "Without LLM - Time consumption: 298.18944549560547 seconds\n", "Without LLM - Solution found: None\n", "Without LLM - Nodes searched: 1000000\n"]}], "source": ["start_state={'Stack1': ['G', 'A', 'C', 'L'], 'Stack2': ['K', 'D'], 'Stack3': ['E', 'F'], 'Stack4': ['B', 'J', 'I', 'H']}\n", "goal_state={'Stack1': ['K', 'A', 'J', 'E'], 'Stack2': ['G', 'I'], 'Stack3': ['C', 'D', 'B'], 'Stack4': ['F', 'H', 'L']}\n", "stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "log_file='A*_test.txt'\n", "start = time.time()\n", "planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "solution, nodes_count = planner.a_star_search(llm=None,max_iterations=1000000)\n", "print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "print(\"Without LLM - Solution found:\", solution)\n", "print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": 37, "id": "656ad1f8-dfc2-4cc6-99a3-87d06d3aedf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start_state={'Stack1': ['D', 'H', 'N', 'G'], 'Stack2': ['J', 'C', 'E'], 'Stack3': ['A', 'I', 'K'], 'Stack4': ['B', 'F', 'L', 'M']}\n", "goal_state={'Stack1': ['F'], 'Stack2': ['E', 'N', 'C', 'H', 'G'], 'Stack3': ['D', 'M', 'B', 'J', 'A'], 'Stack4': ['K', 'L', 'I']}\n", "------\n", "start_state={'Stack1': ['N', 'C'], 'Stack2': ['D', 'K', 'F', 'M', 'B'], 'Stack3': ['L', 'H'], 'Stack4': ['A', 'I', 'J', 'G', 'E']}\n", "goal_state={'Stack1': ['L', 'B', 'J', 'G', 'C'], 'Stack2': ['F', 'A', 'K'], 'Stack3': ['H', 'N', 'M', 'I'], 'Stack4': ['E', 'D']}\n", "------\n", "start_state={'Stack1': ['B', 'H', 'C', 'K', 'M'], 'Stack2': ['I', 'N', 'J'], 'Stack3': ['F', 'A', 'E', 'D'], 'Stack4': ['L', 'G']}\n", "goal_state={'Stack1': ['F', 'J', 'G', 'E', 'N'], 'Stack2': ['D', 'B', 'K'], 'Stack3': ['M', 'C', 'H', 'L'], 'Stack4': ['A', 'I']}\n", "------\n", "start_state={'Stack1': ['G', 'K', 'A', 'F', 'I'], 'Stack2': ['E', 'J', 'B', 'N'], 'Stack3': ['L', 'H', 'D'], 'Stack4': ['C', 'M']}\n", "goal_state={'Stack1': ['I', 'A', 'J'], 'Stack2': ['K', 'M', 'F', 'N', 'G'], 'Stack3': ['H', 'D'], 'Stack4': ['L', 'C', 'B', 'E']}\n", "------\n", "start_state={'Stack1': ['I', 'E', 'M', 'A', 'N'], 'Stack2': ['D', 'C', 'J'], 'Stack3': ['B', 'H', 'F', 'K'], 'Stack4': ['L', 'G']}\n", "goal_state={'Stack1': ['F', 'I', 'M', 'A'], 'Stack2': ['H', 'D', 'K', 'L'], 'Stack3': ['N', 'B', 'G', 'E'], 'Stack4': ['J', 'C']}\n", "------\n"]}], "source": ["for i in range(5):\n", "    start_state, goal_state=generate_stack_states(14)['start_state'],generate_stack_states(14)['goal_state']\n", "    stack_order=['Stack1','Stack2', 'Stack3','Stack4']\n", "    print(f'start_state={start_state}\\ngoal_state={goal_state}')\n", "    print('------')\n", "    # log_file='A*_test.txt'\n", "    # start = time.time()\n", "    # planner = GraphPlanningBlocksWorld(start_state, goal_state,stack_order,log_file=log_file)\n", "    # solution, nodes_count = planner.a_star_search(llm=None,max_iterations=500000)\n", "    # print(\"Without LLM - Time consumption:\", time.time() - start, \"seconds\")\n", "    # print(\"Without LLM - Solution found:\", solution)\n", "    # print(\"Without LLM - Nodes searched:\", nodes_count)"]}, {"cell_type": "code", "execution_count": null, "id": "fe837f67-82a6-4a0f-bbb7-9a97315e27b7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pre_marshalling", "language": "python", "name": "pre_marshalling"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}