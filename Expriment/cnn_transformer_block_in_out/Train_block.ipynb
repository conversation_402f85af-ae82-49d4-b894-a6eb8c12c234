{"cells": [{"cell_type": "code", "execution_count": 3, "id": "31285500-1bfc-45ff-a1a4-5f6616ce2a75", "metadata": {}, "outputs": [], "source": ["import os\n", "from cnn_transformer_modify import train_model,evaluate_model,CNNTransformerClassifier"]}, {"cell_type": "code", "execution_count": 4, "id": "1812ac30-af6c-40ec-9dda-732301479885", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n", "Labels: min=2, max=21\n", "No existing model found at /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth, starting training from scratch\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/envs/pre_marshalling/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/500, Training Loss: 4.9818\n", "Epoch 1/500, Validation Loss: 4.5896\n", "Validation loss improved. Saving model state.\n", "Epoch 2/500, Training Loss: 4.0979\n", "Epoch 2/500, Validation Loss: 4.0005\n", "Validation loss improved. Saving model state.\n", "Epoch 3/500, Training Loss: 3.6446\n", "Epoch 3/500, Validation Loss: 3.9478\n", "Validation loss improved. Saving model state.\n", "Epoch 4/500, Training Loss: 3.4952\n", "Epoch 4/500, Validation Loss: 3.5419\n", "Validation loss improved. Saving model state.\n", "Epoch 5/500, Training Loss: 3.3465\n", "Epoch 5/500, Validation Loss: 3.3748\n", "Validation loss improved. Saving model state.\n", "Epoch 6/500, Training Loss: 3.2661\n", "Epoch 6/500, Validation Loss: 3.3414\n", "Validation loss improved. Saving model state.\n", "Epoch 7/500, Training Loss: 3.2209\n", "Epoch 7/500, Validation Loss: 3.2798\n", "Validation loss improved. Saving model state.\n", "Epoch 8/500, Training Loss: 3.1872\n", "Epoch 8/500, Validation Loss: 3.2256\n", "Validation loss improved. Saving model state.\n", "Epoch 9/500, Training Loss: 3.1594\n", "Epoch 9/500, Validation Loss: 3.2625\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 10/500, Training Loss: 3.1363\n", "Epoch 10/500, Validation Loss: 3.2781\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 11/500, Training Loss: 3.1141\n", "Epoch 11/500, Validation Loss: 3.1997\n", "Validation loss improved. Saving model state.\n", "Epoch 12/500, Training Loss: 3.0972\n", "Epoch 12/500, Validation Loss: 3.2575\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 13/500, Training Loss: 3.0734\n", "Epoch 13/500, Validation Loss: 3.2592\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 14/500, Training Loss: 3.0668\n", "Epoch 14/500, Validation Loss: 3.1929\n", "Validation loss improved. Saving model state.\n", "Epoch 15/500, Training Loss: 3.0347\n", "Epoch 15/500, Validation Loss: 3.1072\n", "Validation loss improved. Saving model state.\n", "Epoch 16/500, Training Loss: 2.9987\n", "Epoch 16/500, Validation Loss: 3.0730\n", "Validation loss improved. Saving model state.\n", "Epoch 17/500, Training Loss: 2.9462\n", "Epoch 17/500, Validation Loss: 3.0434\n", "Validation loss improved. Saving model state.\n", "Epoch 18/500, Training Loss: 2.9106\n", "Epoch 18/500, Validation Loss: 3.0345\n", "Validation loss improved. Saving model state.\n", "Epoch 19/500, Training Loss: 2.8823\n", "Epoch 19/500, Validation Loss: 3.0064\n", "Validation loss improved. Saving model state.\n", "Epoch 20/500, Training Loss: 2.8644\n", "Epoch 20/500, Validation Loss: 3.0030\n", "Validation loss improved. Saving model state.\n", "Epoch 21/500, Training Loss: 2.8445\n", "Epoch 21/500, Validation Loss: 3.0043\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 22/500, Training Loss: 2.8173\n", "Epoch 22/500, Validation Loss: 2.9013\n", "Validation loss improved. Saving model state.\n", "Epoch 23/500, Training Loss: 2.7819\n", "Epoch 23/500, Validation Loss: 2.8420\n", "Validation loss improved. Saving model state.\n", "Epoch 24/500, Training Loss: 2.7569\n", "Epoch 24/500, Validation Loss: 2.9053\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 25/500, Training Loss: 2.7391\n", "Epoch 25/500, Validation Loss: 2.8460\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 26/500, Training Loss: 2.7203\n", "Epoch 26/500, Validation Loss: 2.8226\n", "Validation loss improved. Saving model state.\n", "Epoch 27/500, Training Loss: 2.7050\n", "Epoch 27/500, Validation Loss: 2.8137\n", "Validation loss improved. Saving model state.\n", "Epoch 28/500, Training Loss: 2.6879\n", "Epoch 28/500, Validation Loss: 2.7964\n", "Validation loss improved. Saving model state.\n", "Epoch 29/500, Training Loss: 2.6816\n", "Epoch 29/500, Validation Loss: 2.8057\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 30/500, Training Loss: 2.6778\n", "Epoch 30/500, Validation Loss: 2.8100\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 31/500, Training Loss: 2.6647\n", "Epoch 31/500, Validation Loss: 2.7884\n", "Validation loss improved. Saving model state.\n", "Epoch 32/500, Training Loss: 2.6642\n", "Epoch 32/500, Validation Loss: 2.7321\n", "Validation loss improved. Saving model state.\n", "Epoch 33/500, Training Loss: 2.6622\n", "Epoch 33/500, Validation Loss: 2.7790\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 34/500, Training Loss: 2.6511\n", "Epoch 34/500, Validation Loss: 2.8040\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 35/500, Training Loss: 2.6502\n", "Epoch 35/500, Validation Loss: 2.8134\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 36/500, Training Loss: 2.6449\n", "Epoch 36/500, Validation Loss: 2.7592\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 37/500, Training Loss: 2.6384\n", "Epoch 37/500, Validation Loss: 2.7937\n", "Validation loss did not improve for 5 epoch(s).\n", "Epoch 38/500, Training Loss: 2.6262\n", "Epoch 38/500, Validation Loss: 2.7543\n", "Validation loss did not improve for 6 epoch(s).\n", "Epoch 39/500, Training Loss: 2.6291\n", "Epoch 39/500, Validation Loss: 2.7549\n", "Validation loss did not improve for 7 epoch(s).\n", "Epoch 40/500, Training Loss: 2.6264\n", "Epoch 40/500, Validation Loss: 2.7608\n", "Validation loss did not improve for 8 epoch(s).\n", "Epoch 41/500, Training Loss: 2.6198\n", "Epoch 41/500, Validation Loss: 2.7624\n", "Validation loss did not improve for 9 epoch(s).\n", "Epoch 42/500, Training Loss: 2.6147\n", "Epoch 42/500, Validation Loss: 2.7699\n", "Validation loss did not improve for 10 epoch(s).\n", "Epoch 43/500, Training Loss: 2.6096\n", "Epoch 43/500, Validation Loss: 2.7515\n", "Validation loss did not improve for 11 epoch(s).\n", "Epoch 44/500, Training Loss: 2.5765\n", "Epoch 44/500, Validation Loss: 2.7248\n", "Validation loss improved. Saving model state.\n", "Epoch 45/500, Training Loss: 2.5702\n", "Epoch 45/500, Validation Loss: 2.7133\n", "Validation loss improved. Saving model state.\n", "Epoch 46/500, Training Loss: 2.5685\n", "Epoch 46/500, Validation Loss: 2.7206\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 47/500, Training Loss: 2.5634\n", "Epoch 47/500, Validation Loss: 2.6892\n", "Validation loss improved. Saving model state.\n", "Epoch 48/500, Training Loss: 2.5615\n", "Epoch 48/500, Validation Loss: 2.7243\n", "Validation loss did not improve for 1 epoch(s).\n", "Epoch 49/500, Training Loss: 2.5573\n", "Epoch 49/500, Validation Loss: 2.7278\n", "Validation loss did not improve for 2 epoch(s).\n", "Epoch 50/500, Training Loss: 2.5579\n", "Epoch 50/500, Validation Loss: 2.7152\n", "Validation loss did not improve for 3 epoch(s).\n", "Epoch 51/500, Training Loss: 2.5533\n", "Epoch 51/500, Validation Loss: 2.7269\n", "Validation loss did not improve for 4 epoch(s).\n", "Epoch 52/500, Training Loss: 2.5510\n", "Epoch 52/500, Validation Loss: 2.6998\n", "Validation loss did not improve for 5 epoch(s).\n", "Epoch 53/500, Training Loss: 2.5535\n", "Epoch 53/500, Validation Loss: 2.7262\n", "Validation loss did not improve for 6 epoch(s).\n", "Epoch 54/500, Training Loss: 2.5446\n", "Epoch 54/500, Validation Loss: 2.7486\n", "Validation loss did not improve for 7 epoch(s).\n", "Epoch 55/500, Training Loss: 2.5414\n", "Epoch 55/500, Validation Loss: 2.6995\n", "Validation loss did not improve for 8 epoch(s).\n", "Epoch 56/500, Training Loss: 2.5408\n", "Epoch 56/500, Validation Loss: 2.7289\n", "Validation loss did not improve for 9 epoch(s).\n", "Epoch 57/500, Training Loss: 2.5399\n", "Epoch 57/500, Validation Loss: 2.7157\n", "Validation loss did not improve for 10 epoch(s).\n", "Epoch 58/500, Training Loss: 2.5354\n", "Epoch 58/500, Validation Loss: 2.7043\n", "Validation loss did not improve for 11 epoch(s).\n", "Epoch 59/500, Training Loss: 2.5195\n", "Epoch 59/500, Validation Loss: 2.7151\n", "Validation loss did not improve for 12 epoch(s).\n", "Epoch 60/500, Training Loss: 2.5167\n", "Epoch 60/500, Validation Loss: 2.7123\n", "Validation loss did not improve for 13 epoch(s).\n", "Epoch 61/500, Training Loss: 2.5088\n", "Epoch 61/500, Validation Loss: 2.7179\n", "Validation loss did not improve for 14 epoch(s).\n", "Epoch 62/500, Training Loss: 2.5039\n", "Epoch 62/500, Validation Loss: 2.7189\n", "Validation loss did not improve for 15 epoch(s).\n", "Epoch 63/500, Training Loss: 2.5032\n", "Epoch 63/500, Validation Loss: 2.7103\n", "Validation loss did not improve for 16 epoch(s).\n", "Epoch 64/500, Training Loss: 2.5046\n", "Epoch 64/500, Validation Loss: 2.7278\n", "Validation loss did not improve for 17 epoch(s).\n", "Epoch 65/500, Training Loss: 2.5033\n", "Epoch 65/500, Validation Loss: 2.7183\n", "Validation loss did not improve for 18 epoch(s).\n", "Epoch 66/500, Training Loss: 2.5032\n", "Epoch 66/500, Validation Loss: 2.7008\n", "Validation loss did not improve for 19 epoch(s).\n", "Epoch 67/500, Training Loss: 2.4996\n", "Epoch 67/500, Validation Loss: 2.7052\n", "Validation loss did not improve for 20 epoch(s).\n", "Early stopping triggered after 67 epochs.\n", "Model saved to /root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\n", "--- 正在评估训练集数据 ---\n", "Labels: min=2, max=21\n", "Best Action Accuracy: 0.6997 (21553/30802)\n", "Worst Action Accuracy: 0.7448 (22942/30802)\n", "\n", "--- 正在评估测试集数据 ---\n", "Labels: min=2, max=21\n", "Best Action Accuracy: 0.6559 (2933/4472)\n", "Worst Action Accuracy: 0.7290 (3260/4472)\n"]}], "source": ["\n", "\n", "# 训练数据路径\n", "train_matrix_file = \"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data_matrix.csv\"\n", "train_label_file = \"/root/Train/cnn_transformer_block_in_out/Data/Train_data/data_labels.csv\"\n", "\n", "# 测试数据路径\n", "test_matrix_file = \"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data_matrix.csv\"\n", "test_label_file = \"/root/Train/cnn_transformer_block_in_out/Data/Test_data/data_labels.csv\"\n", "\n", "\n", "# 基础形状\n", "base_shape=(22,21,25)\n", "\n", "model_path=\"/root/Train/Model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth\"\n", "\n", "# 实例化模型\n", "model = CNNTransformerClassifier(\n", "    n_layers=base_shape[0],          # 22 (基础图像通道数)\n", "    n_rows=base_shape[1],            # 21\n", "    n_cols=base_shape[2],            # 25\n", "    embed_dim=64,                    # Transformer嵌入维度\n", "    n_heads=4,                       # Transformer头数\n", "    n_hidden=256,                    # Transformer隐藏层维度\n", "    n_classes=20,                    # 分类任务的类别数 (例如动作数量)\n", "    num_transformer_layers=6,        # Transformer层数\n", "    classifier_dropout_rate=0.1\n", ")\n", "\n", "# 训练模型\n", "trained_model = train_model(\n", "    model,\n", "    train_matrix_file,\n", "    train_label_file,\n", "    epochs=500,\n", "    batch_size=32,\n", "    lr= 3e-4,\n", "    weight_decay=1e-4,\n", "    model_path=model_path,\n", "    orig_shape=base_shape,\n", "    new_shape=base_shape,  \n", "    val_split_ratio=0.2,\n", "    patience=20,\n", "    label_smoothing_epsilon=0.1,\n", ")\n", "\n", "print('--- 正在评估训练集数据 ---')\n", "evaluate_model(\n", "    trained_model,\n", "    train_matrix_file,\n", "    train_label_file,\n", "    orig_shape=base_shape,                 # 传递基础形状\n", "    new_shape=base_shape,           # 传递最终输入形状\n", "    batch_size=32,\n", ")\n", "\n", "print('\\n--- 正在评估测试集数据 ---')\n", "evaluate_model(\n", "    trained_model,\n", "    test_matrix_file,\n", "    test_label_file,\n", "    orig_shape=base_shape,                 # 传递基础形状\n", "    new_shape=base_shape,           # 传递最终输入形状\n", "    batch_size=32,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8fddfe6d-839e-448e-8eb0-e22afaba6b7d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}