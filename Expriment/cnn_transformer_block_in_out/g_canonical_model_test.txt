成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(2, 3), (1, 3), (1, 3), (1, 2), (3, 2), (3, 1), (2, 3), (2, 3), (2, 1)]
Nodes searched: 74
Path length: 9

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 11
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 9
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 2), (1, 3), (1, 3), (2, 1), (2, 1), (2, 1)]
Nodes searched: 9
Path length: 6

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(2, 1), (2, 3), (2, 3), (1, 2), (1, 2), (1, 3), (1, 3), (2, 1)]
Nodes searched: 10
Path length: 8

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (1, 2), (1, 3), (2, 3), (2, 1), (2, 1), (2, 1), (3, 1)]
Nodes searched: 13
Path length: 8

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 10
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 11
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 26
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(2, 3), (2, 3), (2, 3), (1, 2), (1, 2)]
Nodes searched: 6
Path length: 5

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 7
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 3)]
Nodes searched: 8
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 43
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(2, 3), (2, 3), (2, 3), (1, 2), (1, 2)]
Nodes searched: 21
Path length: 5

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(2, 3), (2, 3), (2, 3), (1, 2), (1, 2)]
Nodes searched: 16
Path length: 5

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 10
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 33
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (1, 3), (1, 3), (2, 1), (2, 1)]
Nodes searched: 7
Path length: 5

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (2, 3), (2, 3), (1, 2)]
Nodes searched: 7
Path length: 4

成功从 /home/<USER>/cem208/code/tz_experiment/Expriment/model/cnn_transformer_in_out_model_30803_bs32_lr3e-4_cdr0_1_emdim_64_hid_256_num_head_4_wd1e_4_transdrop_0_3.pth 加载并自适应调整了 CNN+Transformer 模型。
Running A* with CNN+Transformer model:
Solution found: [(1, 3), (1, 3), (1, 3), (2, 1), (2, 1)]
Nodes searched: 9
Path length: 5

