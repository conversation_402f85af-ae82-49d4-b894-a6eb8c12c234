--- CPMPDS 算法性能测试结果 ---


--------------------------------------------------------------------------------
测试配置: TotalS4_U60_D50
--------------------------------------------------------------------------------

  --- 实例: instance_1.txt ---
  初始状态: {'Stack1': [12, 10, 7, 5], 'Stack2': [6, 8, 9], 'Stack3': [4, 11, 3, 2, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 2815, 耗时: 0.5519 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 9.30
      搜索节点数: 2054.50
      耗时: 6.4648 秒
    10次运行最优值:
      最优路径长度: 8
      对应搜索节点数: 455

  --- 实例: instance_10.txt ---
  初始状态: {'Stack1': [7, 3, 10], 'Stack2': [9, 6, 1, 2, 8], 'Stack3': [4, 5, 11, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 6, 搜索节点数: 87, 耗时: 0.0088 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 9.60
      搜索节点数: 931.40
      耗时: 2.8817 秒
    10次运行最优值:
      最优路径长度: 6
      对应搜索节点数: 544

  --- 实例: instance_2.txt ---
  初始状态: {'Stack1': [4, 11, 6, 8], 'Stack2': [12, 7, 3], 'Stack3': [10, 2, 5, 9, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 762, 耗时: 0.1851 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 9.70
      搜索节点数: 1563.30
      耗时: 5.6331 秒
    10次运行最优值:
      最优路径长度: 8
      对应搜索节点数: 4710

  --- 实例: instance_3.txt ---
  初始状态: {'Stack1': [6, 7, 1], 'Stack2': [10, 9, 5, 3, 12], 'Stack3': [4, 11, 2, 8], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 253, 耗时: 0.0251 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 8.10
      搜索节点数: 204.60
      耗时: 0.7192 秒
    10次运行最优值:
      最优路径长度: 7
      对应搜索节点数: 150

  --- 实例: instance_4.txt ---
  初始状态: {'Stack1': [1, 3, 9, 2], 'Stack2': [10, 8, 12, 6, 4], 'Stack3': [11, 7, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 632, 耗时: 0.1668 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 9.90
      搜索节点数: 553.90
      耗时: 1.9119 秒
    10次运行最优值:
      最优路径长度: 7
      对应搜索节点数: 1218

  --- 实例: instance_5.txt ---
  初始状态: {'Stack1': [10, 5, 1, 8], 'Stack2': [11, 6, 12], 'Stack3': [7, 9, 4, 3, 2], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 8594, 耗时: 2.1685 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 11.00
      搜索节点数: 3478.60
      耗时: 11.0849 秒
    10次运行最优值:
      最优路径长度: 9
      对应搜索节点数: 675

  --- 实例: instance_6.txt ---
  初始状态: {'Stack1': [8, 9, 2, 11, 6], 'Stack2': [5, 7, 4], 'Stack3': [12, 10, 1, 3], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 9, 搜索节点数: 1987, 耗时: 0.3438 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 12.30
      搜索节点数: 2958.20
      耗时: 9.2252 秒
    10次运行最优值:
      最优路径长度: 9
      对应搜索节点数: 1781

  --- 实例: instance_7.txt ---
  初始状态: {'Stack1': [10, 8, 2, 11, 12], 'Stack2': [7, 3, 4, 9, 6], 'Stack3': [1, 5], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 71, 耗时: 0.0069 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 12.00
      搜索节点数: 859.00
      耗时: 2.6352 秒
    10次运行最优值:
      最优路径长度: 7
      对应搜索节点数: 91

  --- 实例: instance_8.txt ---
  初始状态: {'Stack1': [10, 9, 2, 5], 'Stack2': [12, 7, 8], 'Stack3': [3, 4, 11, 6, 1], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 8, 搜索节点数: 912, 耗时: 0.2045 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 10.10
      搜索节点数: 644.20
      耗时: 1.9965 秒
    10次运行最优值:
      最优路径长度: 8
      对应搜索节点数: 2037

  --- 实例: instance_9.txt ---
  初始状态: {'Stack1': [2, 8, 5, 7], 'Stack2': [11, 9, 4, 3], 'Stack3': [6, 10, 1, 12], 'Stack4': []}

  [纯 A* 算法]
    找到解决方案。步数: 7, 搜索节点数: 113, 耗时: 0.0105 秒

  [模型引导 A* 算法 (运行 10 次)]
    10次运行平均值:
      路径长度: 10.70
      搜索节点数: 467.80
      耗时: 1.4398 秒
    10次运行最优值:
      最优路径长度: 7
      对应搜索节点数: 255


--------------------------------------------------------------------------------
配置 TotalS4_U60_D50 下模型引导 A* 算法总结:
--------------------------------------------------------------------------------
  所有 10 个实例的平均路径长度的平均值: 10.27
  所有 10 个实例的平均搜索节点数的平均值: 1371.55
================================================================================

