import heapq
import time
from pre_marshalling_llm import LLMGuidance
from datetime import datetime
import torch
import torch.nn as nn
import numpy as np
from pre_marshalling import State
from cnn_transformer_rl import CNNTransformerClassifier
import os

from gymnasium import spaces
from ppo_guidance import PPOPolicyGuidance, CustomMaskableBCPolicy

# State class remains unchanged
class State:
    def __init__(self, config, g=0, h=0):
        self.config = config
        self.g = g
        self.h = h
        self.cost = g + h
    
    def __lt__(self, other):
        return self.cost < other.cost
    
    def __eq__(self, other):
        if not isinstance(other, State):
            return False
        return (tuple(self.config["on-block"].items()),
                tuple(self.config["on-table"].items()),
                tuple(self.config["clear"].items()),
                self.config["hand"]) == (
                tuple(other.config["on-block"].items()),
                tuple(other.config["on-table"].items()),
                tuple(other.config["clear"].items()),
                other.config["hand"])

    def __hash__(self):
        return hash((
            tuple(sorted(self.config["on-block"].items())),
            tuple(sorted(self.config["on-table"].items())),
            tuple(sorted(self.config["clear"].items())),
            self.config["hand"]
        ))

# Main planning class with integrated dynamic weights
class GraphPlanningBlocksWorld:
    def __init__(self, start_state, goal_state, log_file=None, model_path=None, ppo_model_path=None):
        self.state = State(start_state)
        self.goal = State(goal_state)
        self.mutex = self._initialize_mutex_constraints()
        self.log_file = log_file
        
        # Neural network related attributes
        self.blocks, self.tables = self._get_blocks_and_tables(start_state)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.nn_n_rows = len(self.blocks) + 3
        self.nn_n_cols = len(self.blocks) + len(self.tables)
        self.n_blocks = len(self.blocks)
        self.n_stacks = len(self.tables)
        self.n_layers = self.n_blocks + 2
        
        self.model = None
        if model_path:
            self.load_model(model_path)

        self.ppo_model = None
        if ppo_model_path:
            self.load_ppo_model(ppo_model_path)

        self._successors_cache = {}
        
    def _state_to_matrix(self, state_config):
        """将状态转换为矩阵形式，clear行包含栈底的onclear状态"""
        n_blocks = len(self.blocks)
        n_tables = len(self.tables)
        total_cols = n_blocks + n_tables
        matrix = np.zeros((n_blocks + 3, total_cols))  # on-block + on-table + clear + hand
        
        # 1. on-block: 前n_blocks行
        for block, target in state_config["on-block"].items():
            i = self.blocks.index(block)
            if target.startswith("TABLE"):
                j = n_blocks + self.tables.index(target)
            else:
                j = self.blocks.index(target)
            matrix[i, j] = 1
        
        # 2. on-table: 第n_blocks行
        for block, on_table in state_config["on-table"].items():
            if on_table:
                j = self.blocks.index(block)
                matrix[n_blocks, j] = 1
        
        # 3. clear: 第n_blocks+1行，直接使用state["clear"]中的块和栈底状态
        for item, is_clear in state_config["clear"].items():
            if item in self.blocks:
                j = self.blocks.index(item)
                matrix[n_blocks + 1, j] = 1 if is_clear else 0
            elif item in self.tables:
                j = n_blocks + self.tables.index(item)
                matrix[n_blocks + 1, j] = 1 if is_clear else 0
        
        # 4. hand: 第n_blocks+2行
        if state_config["hand"] is not None:
            j = self.blocks.index(state_config["hand"])
            matrix[n_blocks + 2, j] = 1
        
        return matrix    

    def _generate_n_layers_matrix(self):
        """生成多层矩阵，作为CNN输入和RL Obs的依据"""
        n_layers_matrix = np.zeros((self.n_layers, self.nn_n_rows, self.nn_n_cols), dtype=np.float32)
        n_layers_matrix[0] = self._state_to_matrix(state_config=self.goal.config)
        n_layers_matrix[1] = self._state_to_matrix(state_config=self.state.config)

        successors = self.get_successors()
        for i, (next_state, action) in enumerate(successors[:self.n_blocks]):
            n_layers_matrix[2+i] = self._state_to_matrix(next_state.config)        

        return n_layers_matrix

    def get_max_table(self, config):
        max_n = 0
        for target, _ in config["clear"].items():
            if target.startswith("TABLE"):
                n = int(target[5:])
                max_n = max(max_n, n)
        return max_n

    def load_model(self, model_path):
        try:
            self.model = CNNTransformerClassifier(n_layers=self.nn_n_rows-1,
                n_rows=self.nn_n_rows,
                n_cols=self.nn_n_cols,
                n_classes=self.nn_n_rows-3).to(self.device)
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()
            self.log(f"Loaded CNN+Transformer model from {model_path}")
        except Exception as e:
            self.log(f"Error loading model: {e}")
            self.model = None

    # def load_ppo_model(self, ppo_model_path):
    #     """加载 PPO 模型，不绑定环境"""
    #     try:
    #         self.ppo_model = MaskablePPO.load(
    #             ppo_model_path,
    #             env=None,
    #             device=self.device,
    #             custom_objects={"policy.optimizer": None}
    #         )
    #         self.log(f"Loaded PPO model from {ppo_model_path}")
    #     except Exception as e:
    #         self.log(f"Error loading PPO model: {e}")
    #         self.ppo_model = None
    
    def load_ppo_model(self, ppo_model_path):
        """加载 PPO 模型，不绑定环境"""
        # 定义空间和策略参数
        n_blocks = self.n_blocks
        obs_layers = 2 + n_blocks
        n_rows = self.nn_n_rows
        n_cols = self.nn_n_cols
        observation_space = spaces.Box(
            low=0, high=1,
            shape=(self.n_layers, self.nn_n_rows, self.nn_n_cols),
            dtype=np.float32
        )
        action_space = spaces.Discrete(n_blocks)
        # policy_kwargs = dict(net_arch=[128, 64]) # 确保与训练时一致

        try:
            self.ppo_model = PPOPolicyGuidance(
                ppo_model_path,
                observation_space,
                action_space,
                policy_class=CustomMaskableBCPolicy,
                # policy_kwargs=policy_kwargs,
                device=self.device
            )
            print(f"PPO 指导模型已准备就绪 (从 {ppo_model_path})。")
        except Exception as e:
            self.log(f"Error loading PPO model: {e}")
            self.ppo_model = None

    def _get_blocks_and_tables(self, state):
        """从状态中提取块和栈底列表"""
        blocks = sorted(set(state["on-table"].keys()))
        tables = set()
        for target in state["clear"].keys():
            if target.startswith("TABLE"):
                tables.add(target)
        return blocks, sorted(list(tables))
    
    

    def _initialize_mutex_constraints(self):
        mutex = set()
        for block in self.state.config["on-block"]:
            mutex.add(("on-table", block))
            for other in self.state.config["on-block"]:
                if block != other:
                    mutex.add((block, other))
        return mutex

    def is_mutex_violation(self, state):
        for constraint in self.mutex:
            if isinstance(constraint, tuple):
                key, value = constraint
                if key in state.config["on-table"] and state.config["on-table"][key] and value in state.config["on-block"]:
                    return True
        return False

    def on_block(self, x, y):
        return self.state.config["on-block"].get(x) == y

    def on_table(self, x):
        return self.state.config["on-table"].get(x) == True

    def clear(self, x):
        return self.state.config["clear"].get(x, False)

    def hand_empty(self):
        return self.state.config["hand"] is None

    def in_hand(self, x):
        return self.state.config["hand"] == x

    def pickUp_table(self, x):
        if self.on_table(x) and self.clear(x) and self.hand_empty():
            new_config = self.copy_state()
            new_config["hand"] = x
            new_config["on-table"][x] = False
            new_config["clear"][x] = False
            return State(new_config), f"PickUp_table({x})"
        return None, None

    def putDown_table(self, x):
        if self.in_hand(x):
            new_config = self.copy_state()
            new_config["on-table"][x] = True
            new_config["clear"][x] = True
            new_config["hand"] = None
            return State(new_config), f"PutDown_table({x})"
        return None, None

    def pickUp_block(self, x, y):
        if self.on_block(x, y) and self.clear(x) and self.hand_empty():
            new_config = self.copy_state()
            new_config["hand"] = x
            del new_config["on-block"][x]
            new_config["clear"][x] = False
            new_config["clear"][y] = True
            return State(new_config), f"pickUp_block({x}, {y})"
        return None, None

    def putDown_block(self, x, y):
        if self.in_hand(x) and self.clear(y) and (y in self.state.config["on-block"] or y.startswith("TABLE")):
            new_config = self.copy_state()
            new_config["on-block"][x] = y
            new_config["clear"][x] = True
            new_config["clear"][y] = False
            new_config["hand"] = None
            return State(new_config), f"putDown_block({x}, {y})"
        return None, None

    def copy_state(self):
        return {
            "on-block": self.state.config["on-block"].copy(),
            "on-table": self.state.config["on-table"].copy(),
            "clear": self.state.config["clear"].copy(),
            "hand": self.state.config["hand"]
        }

    def is_goal(self, state):
        for key in self.goal.config.keys():
            if state.config[key] != self.goal.config[key]:
                return False
        return True

    def get_successors(self):
        return self._get_successors()
        # successors = self._successors_cache.get(self.state, None)
        # if successors is None:
        #     successors = self._get_successors()
        #     self._successors_cache[self.state] = successors

        # return successors

    def _get_successors(self):
        self.mutex = self._initialize_mutex_constraints()
        successors = []
        for block in self.state.config["on-block"]:
            target = self.state.config["on-block"][block]
            new_state, action = self.pickUp_block(block, target)
            if new_state and not self.is_mutex_violation(new_state):
                successors.append((new_state, action))
        for block in self.state.config["on-table"]:
            if self.state.config["on-table"][block]:
                new_state, action = self.pickUp_table(block)
                if new_state and not self.is_mutex_violation(new_state):
                    successors.append((new_state, action))
        if self.state.config["hand"] is not None:
            block = self.state.config["hand"]
            new_state, action = self.putDown_table(block)
            if new_state and not self.is_mutex_violation(new_state):
                successors.append((new_state, action))
            possible_targets = [target for target in self.state.config["on-block"].keys() 
                                if self.state.config["clear"].get(target, False)]
            for i in range(1, len(self.tables) + 1):
                table = f"TABLE{i}"
                if self.state.config["clear"].get(table, True):
                    possible_targets.append(table)
            for target in possible_targets:
                new_state, action = self.putDown_block(block, target)
                if new_state and not self.is_mutex_violation(new_state):
                    successors.append((new_state, action))
        successors.sort()
        return successors

    def heuristic(self, state):
        cost = 0
        for block, target in self.goal.config["on-block"].items():
            current_pos = state.config["on-block"].get(block)
            if current_pos != target:
                cost += 1
        return cost

    def log(self, message):
        if self.log_file:
            print(message)
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + '\n')

    # def calculate_dynamic_weights(self, parent_h, child_h, child_successors):
    #     k_min = 0.33
    #     k_max = 3.0  # Reasonable upper limit
    #     delta = 0.01

    #     if child_h == 0:
    #         return 1.0, 1.0

    #     if parent_h is not None and child_h > 0:
    #         k_min_candidate = max(0, (parent_h - 1) / child_h)
    #         k_min = max(k_min, k_min_candidate)

    #     child_h_values = [self.heuristic(s[0]) for s in child_successors]
    #     if child_h_values:
    #         k_max_candidate = min([round((1 + h_child) / child_h, 3) for h_child in child_h_values])
    #         k_max = min(k_max, k_max_candidate)

    #     k_best = k_min + delta
    #     k_worst = k_max - delta

    #     return k_best, k_worst

    def calculate_dynamic_weights(self, parent_h, child_h, child_successors, best_confidence, worst_confidence):
        # 基础边界
        k_min_threshold = 0.2
        k_max_threshold = 5
        # 根据置信度调整 k_min 和 k_max
        confidence_threshold_high = 0.8 # 高置信度阈值
        confidence_threshold_low = 0.5   # 低置信度阈值

        delta = 0.01

        if child_h == 0:
            return 1.0, 1.0

        # 根据一致性要求计算 k_min 和 k_max
        if parent_h is not None and child_h > 0:
            k_min_candidate = max(0, (parent_h - 1) / child_h)

        child_h_values = [self.heuristic(s[0]) for s in child_successors]
        if child_h_values:
            k_max_candidate = min([round((1 + h_child) / child_h, 3) for h_child in child_h_values])

        # 最佳动作的 k_best
        if best_confidence > confidence_threshold_high:
            # 高置信度：允许更大的偏离
            k_best = k_min_threshold 
        elif best_confidence < confidence_threshold_low:
            # 低置信度：放弃指导
            k_best = 1
        else:
            # 中置信度：严格遵循一致性
            k_best = k_min_candidate + delta

        # 最差动作的 k_worst
        if worst_confidence > confidence_threshold_high:
            # 高置信度：允许更大的惩罚
            k_worst = k_max_threshold
        elif best_confidence < confidence_threshold_low:
            # 低置信度：放弃指导
            k_worst = 1
        else:
            # 中置信度：严格遵循一致性
            k_worst = k_max_candidate - delta

        return k_best, k_worst

    def a_star_search(self, llm=None, random_perturb_prob=0., max_expansions=1.5e6):
        start = time.time()

        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h
        
        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0

        try:
            open(self.log_file, 'a', encoding='utf-8').close()
        except:
            pass

        if llm is None and self.model is None and self.ppo_model is None:
            self.log("Running A* without guidance:")
        elif self.ppo_model is not None:
            self.log("Running A* with PPO model:")
        elif self.model is not None:
            self.log("Running A* with CNN+Transformer model:")
        else:
            self.log(f"Running A* with LLM {llm.model}:")

        while queue:
            cost_popped, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if count > max_expansions:
                self.log("No solution found\n")
                return None, -1

            if self.is_goal(current_state):
                self.log(f"Solution found: {path}")
                self.log(f"Nodes searched: {count}")
                self.log(f"Time consumption: {time.time() - start} seconds.\n")
                return path, count

            self.state = current_state
            # self.mutex = self.initialize_mutex_constraints()
            successors = self.get_successors()

            if self.model is not None or llm is not None or self.ppo_model is not None:
                if self.ppo_model is not None:
                    current_obs_matrix = self._generate_n_layers_matrix()
                    nn_result = self.ppo_model.evaluate_actions(current_obs_matrix)
                    best_action_idx = nn_result["best_action_idx"]
                    worst_action_idx = nn_result["worst_action_idx"]
                    best_confidence = nn_result["best_confidence"]
                    worst_confidence = nn_result["worst_confidence"]
                elif self.model is not None:
                    nn_result = self.model.evaluate_actions(self._generate_n_layers_matrix(),random_perturb_prob=random_perturb_prob,device=self.device)
                    best_action_idx = nn_result["best_action_idx"]
                    worst_action_idx = nn_result["worst_action_idx"]
                    best_confidence = nn_result["best_confidence"]
                    worst_confidence = nn_result["worst_confidence"]
                else:
                    try:
                        llm_result = llm.evaluate_actions(current_state, self.goal, self, successors)
                        best_action = llm_result["best_action"]
                        best_reason = llm_result["best_reason"]
                        worst_action = llm_result["worst_action"]
                        worst_reason = llm_result["worst_reason"]
                        best_action_idx = int(best_action) - 1 if best_action != "uncertain" else -1
                        worst_action_idx = int(worst_action) - 1 if worst_action != "uncertain" else -1
                    except:
                        best_action_idx = -1
                        worst_action_idx = -1

                self.log(f"\nNode {count}: Current State: {current_state.config}")
                self.log(f"Node {count}: Goal State: {self.goal.config}")

                for i, (next_state, action) in enumerate(successors):
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.cost = next_state.g + next_state.h

                    # Apply dynamic weights if best or worst action
                    if (best_action_idx != -1 and i == best_action_idx) or \
                       (worst_action_idx != -1 and i == worst_action_idx):
                        # Temporarily switch state to get child successors
                        original_state = self.state
                        self.state = next_state
                        next_successors = self.get_successors()
                        self.state = original_state

                        k_best, k_worst = self.calculate_dynamic_weights(
                            current_state.h, next_state.h, next_successors, 
                            best_confidence=nn_result["best_confidence"],
                            worst_confidence=nn_result["worst_confidence"]
                        )
                        
                        if best_action_idx != -1 and i == best_action_idx:
                            next_state.g -= 1  # Optional: keep this adjustment if desired
                            next_state.cost = next_state.g + next_state.h * k_best
                            # if i > 0 :
                            #     print(f"best action > 0 :{i}")
                            if llm is not None:
                                self.log(f"Node {count}: LLM suggests Best Action '{action}' with adjusted cost {next_state.cost} (k_best={k_best})")
                                self.log(f"Best Reason: {best_reason}")
                            else:
                                self.log(f"Node {count}: Model suggests Best Action '{action}' with adjusted cost {next_state.cost} (k_best={k_best})")
                        elif worst_action_idx != -1 and i == worst_action_idx:
                            next_state.cost = next_state.g + next_state.h * k_worst
                            if llm is not None:
                                self.log(f"Node {count}: LLM suggests Worst Action '{action}' with adjusted cost {next_state.cost} (k_worst={k_worst})")
                                self.log(f"Worst Reason: {worst_reason}")
                            else:
                                self.log(f"Node {count}: Model suggests Worst Action '{action}' with adjusted cost {next_state.cost} (k_worst={k_worst})")
                    # Else, cost remains as g + h (default)

            else:
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.cost = next_state.g + next_state.h

            for i, (next_state, action) in enumerate(successors):
                heapq.heappush(queue, (next_state.cost, count, next_state, path + [action]))

        self.log("No solution found\n")
        return None, -1


def test_a_star_guidance(start_state, goal_state, model_path, ppo_model_path, log_file_prefix="a_star_test"):
    results = []
    
    planner_ppo = GraphPlanningBlocksWorld(
        start_state=start_state,
        goal_state=goal_state,
        model_path=model_path,
        ppo_model_path=ppo_model_path,
        log_file=f"{log_file_prefix}_debug.txt"
    )
    
    # # 调试动作和状态一致性
    # successors = planner_ppo.get_successors()
    # env_successors = planner_ppo.ppo_env.planner.get_successors()
    # print(f"GraphPlanningBlocksWorld successors: {[s[1] for s in successors]}")
    # print(f"GymBlocksWorld successors: {[s[1] for s in env_successors]}")
    
    # obs = planner_ppo.ppo_env.get_observation()
    # matrix = planner_ppo._state_to_matrix(start_state)
    # print(f"PPO observation shape: {obs.shape}, sample: {obs.flatten()[:10]}")
    # print(f"Matrix shape: {matrix.shape}, sample: {matrix.flatten()[:10]}")
    
    # # 无指导
    # planner_none = GraphPlanningBlocksWorld(
    #     start_state=start_state,
    #     goal_state=goal_state,
    #     log_file=f"{log_file_prefix}_none.txt"
    # )
    # start_time = time.time()
    # path, nodes_searched = planner_none.a_star_search(max_expansions=3e6)
    # duration = time.time() - start_time
    # results.append({
    #     "mode": "No Guidance",
    #     "path_length": len(path) if path else None,
    #     "nodes_searched": nodes_searched,
    #     "duration": duration,
    #     "success": path is not None
    # })
    
    # # cnn_transformer 指导
    # planner_cnn = GraphPlanningBlocksWorld(
    #     start_state=start_state,
    #     goal_state=goal_state,
    #     log_file=f"{log_file_prefix}_cnn.txt",
    #     model_path=model_path
    # )
    # start_time = time.time()
    # path, nodes_searched = planner_cnn.a_star_search(max_expansions=3e6)
    # duration = time.time() - start_time
    # results.append({
    #     "mode": "CNN+Transformer",
    #     "path_length": len(path) if path else None,
    #     "nodes_searched": nodes_searched,
    #     "duration": duration,
    #     "success": path is not None
    # })
    
    # PPO 指导
    start_time = time.time()
    path, nodes_searched = planner_ppo.a_star_search(max_expansions=3e6)
    duration = time.time() - start_time
    results.append({
        "mode": "PPO",
        "path_length": len(path) if path else None,
        "nodes_searched": nodes_searched,
        "duration": duration,
        "success": path is not None
    })
    
    # 打印结果
    print("\nA* 指导效果比较：")
    for result in results:
        print(f"模式: {result['mode']}")
        print(f"  路径长度: {result['path_length']}")
        print(f"  节点扩展数: {result['nodes_searched']}")
        print(f"  运行时间: {result['duration']:.2f} 秒")
        print(f"  成功: {result['success']}")
        print()
    
    return results


if __name__ == "__main__":
    # start_state = {
    #     "on-block": {"A": "B", "B": "TABLE"},
    #     "on-table": {"A": False, "B": False, "C": True},
    #     "clear": {"A": True, "B": False, "C": True, "TABLE": False},
    #     "hand": None
    # }

    # goal_state = {
    #     "on-block": {"B": "A", "A": "C", "C": "TABLE"},
    #     "on-table": {"A": False, "B": False, "C": False},
    #     "clear": {"A": False, "B": True, "C": False, "TABLE": False},
    #     "hand": None
    # }

    # --- 复杂初始状态（10 个 Blocks）---
    start_state = {
        # "on-block": {"A": "B", "B": "D", "D": "E", "E": "F", "F": "TABLE1"},
        "on-block": {"A": "B", "B": "D", "D": "F", "F": "E", "E": "TABLE1"}, #微调状态，验证模型是死记硬背还是提取模式
        "on-table": {"A": False, "B": False, "C": True, "D": False, "E": False, "F": False, "G": True, "H": True, "I": True, "J": True},
        "clear": {"A": True, "B": False, "C": True, "D": False, "E": False, "F": False, "G": True, "H": True, "I": True, "J": True, "TABLE1": False},
        "hand": None
    }

    # --- 目标状态 ---
    goal_state = {
        "on-block": {"A": "B", "B": "C", "C": "D", "D": "E", "E": "F", "F": "G", "G": "H", "H": "I", "I": "J", "J": "TABLE1"},
        "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False},
        "clear": {"A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False, "TABLE1": False},
        "hand": None
    }

    # # 新算例，进一步测试模型泛化能力，考虑到训练样本的特征，仍保持10 bocks 单栈，且目标状态满栈
    # start_state = {
    #     "on-block": {"H": "I", "I": "G", "G": "J", "J": "C", "C": "TABLE1"},
    #     "on-table": {"A": True, "B": True, "C": False, "D": True, "E": True, "F": True, "G": False, "H": False, "I": False, "J": False},
    #     "clear": {"A": True, "B": True, "C": False, "D": True, "E": True, "F": True, "G": False, "H": True, "I": False, "J": False, "TABLE1": False},
    #     "hand": None
    # }

    # goal_state = {
    #     "on-block": {"J": "E", "E": "B", "B": "H", "H": "D", "D": "A", "A": "I", "I": "G", "G": "F", "F": "C", "C": "TABLE1"},
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False},
    #     "clear": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": True, "TABLE1": False},
    #     "hand": None
    # }    

    # # --- 复杂初始状态（另一个 10 个 Blocks）---
    # start_state = {
    #     "on-block": {"J": "I", "I": "H", "H": "G", "G": "F", "F": "E", "E": "D", "D": "C", "C": "B", "B": "TABLE1"},
    #     "on-table": {"A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False},
    #     "clear": {"A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": True, "TABLE1": False},
    #     "hand": None
    # }

    # goal_state = {
    #     "on-block": {"F": "E", "E": "D", "D": "C", "C": "B", "B": "A", "A": "J", "J": "I", "I": "H", "H": "TABLE1"},
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": True, "H": False, "I": False, "J": False},
    #     "clear": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": True, "G": True, "H": False, "I": False, "J": False, "TABLE1": False},
    #     "hand": None
    # }

    # # --- 15 Blocks 双栈---
    # start_state = {
    #     "on-block": {
    #         "J": "I", "I": "H", "H": "G", "G": "F", "F": "E", "E": "D", "D": "C", "C": "B", "B": "A", "A": "TABLE1",  # 第一个栈：J-I-H-G-F-E-D-C-B-A-TABLE1
    #         "O": "N", "N": "M", "M": "L", "L": "K", "K": "TABLE2"                                                  # 第二个栈：O-N-M-L-K-TABLE2
    #     },
    #     "on-table": {
    #         "A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False,
    #         "K": False, "L": False, "M": False, "N": False, "O": False
    #     },
    #     "clear": {
    #         "A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": True,  # J 是第一个栈的顶部
    #         "K": False, "L": False, "M": False, "N": False, "O": True,                                                      # O 是第二个栈的顶部
    #         "TABLE1": False, "TABLE2": False
    #     },
    #     "hand": None
    # }

    # goal_state = {
    #     "on-block": {
    #         "H": "G", "G": "F", "F": "E", "E": "D", "D": "C", "C": "B", "B": "A", "A": "TABLE1",  # 第一个栈：H-G-F-E-D-C-B-A-TABLE1
    #         "O": "N", "N": "M", "M": "L", "L": "K", "K": "J", "J": "I", "I": "TABLE2"          # 第二个栈：O-N-M-L-K-J-I-TABLE2
    #     },
    #     "on-table": {
    #         "A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False,
    #         "K": False, "L": False, "M": False, "N": False, "O": False
    #     },
    #     "clear": {
    #         "A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": True,  # H 是第一个栈的顶部
    #         "I": False, "J": False, "K": False, "L": False, "M": False, "N": False, "O": True,              # O 是第二个栈的顶部
    #         "TABLE1": False, "TABLE2": False
    #     },
    #     "hand": None
    # }

    # --- 复杂初始状态（10 个 Blocks, 多栈）---
    # start_state = {
    #     "on-block": {"A": "E", "E": "F", "F": "TABLE1", "C": "D", "D": "G", "G": "TABLE2"}, 
    #     "on-table": {"A": False, "B": True, "C": False, "D": False, "E": False, "F": False, "G": False, "H": True, "I": True, "J": True},
    #     "clear": {"A": True, "B": True, "C": True, "D": False, "E": False, "F": False, "G": False, "H": True, "I": True, "J": True, "TABLE1": False, "TABLE2": False}, 
    #     "hand": None 
    # } 
    
    # # --- 目标状态 --- 
    # goal_state = { 
    #     "on-block": {"B": "I", "I": "J", "J": "A", "A": "E", "E": "F", "F": "C", "C": "D", "D": "G", "G": "TABLE2"}, 
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": True, "I": False, "J": False}, 
    #     "clear": {"A": False, "B": True, "C": False, "D": False, "E": False, "F": False, "G": False, "H": True, "I": False, "J": False, "TABLE1": True, "TABLE2": False}, 
    #     "hand": None 
    # }

    # # --- 复杂初始状态（12 个 Blocks, 多栈）---
    # start_state = { 
    #     "on-block": {"A": "E", "E": "G", "G": "I", "I": "TABLE1", "C": "D", "D": "F", "F": "J", "J": "TABLE2"}, 
    #     "on-table": {"A": False, "B": True, "C": False, "D": False, "E": False, "F": False, "G": False, "H": True, "I": False, "J": False, "K": True, "L": True}, 
    #     "clear": {"A": True, "B": True, "C": True, "D": False, "E": False, "F": False, "G": False, "H": True, "I": False, "J": False, "K": True, "L": True, "TABLE1": False, "TABLE2": False}, 
    #     "hand": None 
    # } 
    
    # # --- 目标状态 --- 
    # goal_state = { 
    #     "on-block": {"B": "H", "H": "K", "K": "L", "L": "C", "C": "D", "D": "F", "F": "J", "J": "A", "A": "E", "E": "G", "G": "I", "I": "TABLE1"}, 
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False, "K": False, "L": False}, 
    #     "clear": {"A": False, "B": True, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, "I": False, "J": False, "K": False, "L": False, "TABLE1": False, "TABLE2": True}, 
    #     "hand": None 
    # }

    # --- 复杂初始状态（20 个 Blocks, 单栈）---
    # start_state = {
    #     "on-block": {"A": "B", "B": "C", "C": "D", "D": "E", "E": "F", "F": "G", "G": "H", "H": "I", "I": "TABLE1"},
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False,
    #                 "I": False, "J": True, "K": True, "L": True, "M": True, "N": True, "O": True, "P": True,
    #                 "Q": True, "R": True, "S": True, "T": True},
    #     "clear": {"A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False,
    #             "I": False, "J": True, "K": True, "L": True, "M": True, "N": True, "O": True, "P": True,
    #             "Q": True, "R": True, "S": True, "T": True, "TABLE1": False},
    #     "hand": None
    # }
    # goal_state = {
    #     "on-block": {"A": "B", "B": "C", "C": "D", "D": "E", "E": "F", "F": "G", "G": "H", "H": "I", "I": "J",
    #                 "J": "K", "K": "L", "L": "M", "M": "N", "N": "O", "O": "P", "P": "Q", "Q": "R", "R": "S", "S": "T",
    #                 "T": "TABLE1"},
    #     "on-table": {"A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False,
    #                 "I": False, "J": False, "K": False, "L": False, "M": False, "N": False, "O": False, "P": False,
    #                 "Q": False, "R": False, "S": False, "T": False},
    #     "clear": {"A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False,
    #             "I": False, "J": False, "K": False, "L": False, "M": False, "N": False, "O": False, "P": False,
    #             "Q": False, "R": False, "S": False, "T": False, "TABLE1": False},
    #     "hand": None
    # }


    # # 更大规模: 30 blocks, 单栈
    # start_state = {
    #     "on-block": {
    #         "A": "B", "B": "F", "F": "E", "E": "D", "D": "H", "H": "I", "I": "J", "J": "L", "L": "M",
    #         "M": "N", "N": "O", "O": "P", "P": "Q", "Q": "R", "R": "S", "S": "T", "T": "U", "U": "V",
    #         "V": "W", "W": "X", "X": "Y", "Y": "Z", "Z": "AA", "AA": "BB", "BB": "CC", "CC": "DD", "DD": "TABLE1"
    #     },
    #     "on-table": {
    #         "A": False, "B": False, "C": True, "D": False, "E": False, "F": False, "G": True, "H": False, 
    #         "I": False, "J": False, "K": True, "L": False, "M": False, "N": False, "O": False, "P": False, 
    #         "Q": False, "R": False, "S": False, "T": False, "U": False, "V": False, "W": False, "X": False, 
    #         "Y": False, "Z": False, "AA": False, "BB": False, "CC": False, "DD": False
    #     },
    #     "clear": {
    #         "A": True, "B": False, "C": True, "D": False, "E": False, "F": False, "G": True, "H": False, 
    #         "I": False, "J": False, "K": True, "L": False, "M": False, "N": False, "O": False, "P": False, 
    #         "Q": False, "R": False, "S": False, "T": False, "U": False, "V": False, "W": False, "X": False, 
    #         "Y": False, "Z": False, "AA": False, "BB": False, "CC": False, "DD": False, "TABLE1": False
    #     },
    #     "hand": None
    # }

    # goal_state = {
    #     "on-block": {
    #         "A": "B", "B": "C", "C": "D", "D": "E", "E": "F", "F": "G", "G": "H", "H": "I", "I": "J",
    #         "J": "K", "K": "L", "L": "M", "M": "N", "N": "O", "O": "P", "P": "Q", "Q": "R", "R": "S",
    #         "S": "T", "T": "U", "U": "V", "V": "W", "W": "X", "X": "Y", "Y": "Z", "Z": "AA", "AA": "BB",
    #         "BB": "CC", "CC": "DD", "DD": "TABLE1"
    #     },
    #     "on-table": {
    #         "A": False, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, 
    #         "I": False, "J": False, "K": False, "L": False, "M": False, "N": False, "O": False, "P": False, 
    #         "Q": False, "R": False, "S": False, "T": False, "U": False, "V": False, "W": False, "X": False, 
    #         "Y": False, "Z": False, "AA": False, "BB": False, "CC": False, "DD": False
    #     },
    #     "clear": {
    #         "A": True, "B": False, "C": False, "D": False, "E": False, "F": False, "G": False, "H": False, 
    #         "I": False, "J": False, "K": False, "L": False, "M": False, "N": False, "O": False, "P": False, 
    #         "Q": False, "R": False, "S": False, "T": False, "U": False, "V": False, "W": False, "X": False, 
    #         "Y": False, "Z": False, "AA": False, "BB": False, "CC": False, "DD": False, "TABLE1": False
    #     },
    #     "hand": None
    # }


    # 获取当前日期和时间
    current_time = datetime.now()

    # 格式化为 YYYY-MM-DD-HHMMSS 字符串
    time_string = current_time.strftime("%Y-%m-%d-%H%M%S")

    # # 无LLM指导
    # planner = GraphPlanningBlocksWorld(start_state, goal_state, 
    #                 log_file="log/astar_with_llm_"+time_string+".log")
    # solution, nodes_count = planner.a_star_search(llm=None)

    print()

    # # 有LLM指导
    # llm = LLMGuidance()
    # planner = GraphPlanningBlocksWorld(start_state, goal_state, log_file="astar_with_llm_"+time_string+".log")
    # solution, nodes_count = planner.a_star_search(llm=llm)


    # 有本地模型指导
    model_path = "model/cnn_transformer_model.pth"
    # model_path = "model/cnn_transformer_model_rl.pth"
    # ppo_model_path = "model/bc_policy.pth" # 或 "model/ppo_bc_trained.zip"
    ppo_model_path = "model/ppo_trained_model_10_3.zip"


    # # 在扩展学习中，blocks和TABLE数量会放有余量
    # for i in range(5):
    #     start_state["on-table"]["BLOCK_TEMP"+str(i+1)]=True
    #     start_state["clear"]["BLOCK_TEMP"+str(i+1)]=True    
    # start_state["clear"]["TABLE_TEMP1"]=True

    # for i in range(5):
    #     goal_state["on-table"]["BLOCK_TEMP"+str(i+1)]=True
    #     goal_state["clear"]["BLOCK_TEMP"+str(i+1)]=True    
    # goal_state["clear"]["TABLE_TEMP1"]=True 

    # planner = GraphPlanningBlocksWorld(start_state, goal_state, 
    #                 log_file="log/astar_with_model_"+time_string+".log",
    #                 model_path=model_path,ppo_model_path=None)    
    # solution, nodes_count = planner.a_star_search()


    results = test_a_star_guidance(start_state, goal_state, None, ppo_model_path)
