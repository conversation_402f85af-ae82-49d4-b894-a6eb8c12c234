import torch
import heapq
import time
from datetime import datetime
from cnn_transformer_modify import CNNTransformerClassifier
import heapq
import numpy as np
import time
from datetime import datetime
from pre_marshalling_llm import LLMGuidance,get_priority_task
class State:
    def __init__(self, stacks, current_fix_stack, g=0, h=0, parent=None, action=None):
        self.stacks = stacks  # 栈字典
        self.current_fix_stack = current_fix_stack  # 当前修复栈
        self.g = g  # 到当前状态的实际代价
        self.h = h  # 估计剩余代价
        self.cost = g + h  # 总代价
        self.parent = parent  # 父状态
        self.action = action  # 导致该状态的动作
        self.h_adjusted = None  # 调整后的启发式值

    def __lt__(self, other):
        return self.cost < other.cost

    def __eq__(self, other):
        if not isinstance(other, State):
            return False
        # 仅比较 stacks，不比较 current_fix_stack
        return tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())) == \
               tuple(sorted((k, tuple(v)) for k, v in other.stacks.items()))

    def __hash__(self):
        # 仅基于 stacks 计算哈希值
        return hash(tuple(sorted((k, tuple(v)) for k, v in self.stacks.items())))

    def __str__(self):
        return str(self.stacks)

class GraphPlanningBlocksWorld:
    def __init__(self, start_state, goal_state,fix_order,log_file=None, model_path=None, ppo_model_path=None):
        self.state = State(start_state, fix_order[0])  # 初始状态指定第一个修复栈
        self.goal = State(goal_state, None)  # 目标状态无需修复栈
        self.log_file = log_file
        self.check = []
        self.fix_order = fix_order  # 保存修复顺序

        # Neural network related attributes
        self.blocks, self.stacks = self._get_blocks_and_stacks(start_state)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.nn_n_rows = len(self.blocks) + 1  # 20 + 1 = 21 (on-block + clear)
        self.nn_n_cols = len(self.blocks) + len(self.stacks)  # 20 + 5 = 25
        self.n_blocks = len(self.blocks)  # 20
        self.n_stacks = len(self.stacks)  # 5
        self.n_layers = 22 # 1 (goal) + 1 (current) + 20 (successors)

        self.model = None
        if model_path:
            self.load_model(model_path)

        # self.ppo_model = None
        # if ppo_model_path:
        #     self.load_ppo_model(ppo_model_path)

        self._successors_cache = {}

    def state_to_matrix(self, state):
        """将状态转换为矩阵形式，仅包含on-block和clear信息"""
        n_blocks = len(self.blocks)  # e.g., 20
        n_stacks = len(self.stacks)  # e.g., 5
        matrix = np.zeros((n_blocks + 1, n_blocks + n_stacks))  # e.g., (21, 25)

        # 遍历每个栈
        for stack_name, stack in state.items():
            if not stack:  # 空栈，跳过
                continue
            stack_idx = self.stacks.index(stack_name) + n_blocks  # 栈的列索引（e.g., 20到24）

            # 底部块在栈上
            bottom_block = stack[0]
            bottom_idx = self.blocks.index(bottom_block)
            matrix[bottom_idx, stack_idx] = 1  # on-block

            # 其他块的堆叠关系
            for i in range(1, len(stack)):
                current_block = stack[i]
                below_block = stack[i - 1]
                current_idx = self.blocks.index(current_block)
                below_idx = self.blocks.index(below_block)
                matrix[current_idx, below_idx] = 1  # on-block

            # 顶部块是clear的
            top_block = stack[-1]
            top_idx = self.blocks.index(top_block)
            matrix[n_blocks, top_idx] = 1  # clear

        return matrix

    def _generate_n_layers_matrix(self):
        """生成多层矩阵，作为CNN输入和RL Obs的依据"""
        n_layers_matrix = np.zeros((self.n_layers, self.nn_n_rows, self.nn_n_cols), dtype=np.float32)
        n_layers_matrix[0] = self.state_to_matrix(self.goal.stacks)
        n_layers_matrix[1] = self.state_to_matrix(self.state.stacks)

        successors = self.get_successors()
        for i, (next_state, action) in enumerate(successors[:len(self.stacks)*(len(self.stacks)-1)]):  # 限制为最多len(self.stacks)*(len(self.stacks)-1)个后继状态
            n_layers_matrix[2 + i] = self.state_to_matrix(next_state.stacks)

        return n_layers_matrix

    def _get_blocks_and_stacks(self, state):
        """从状态中提取块和栈列表"""
        # 提取栈（状态字典的键）
        stacks = sorted(state.keys())  # e.g., ['Stack1', 'Stack2', 'Stack3', 'Stack4', 'Stack5']

        # 提取所有块（从每个栈的块列表中收集唯一块）
        blocks = set()
        for stack in state.values():
            blocks.update(stack)
        blocks = sorted(list(blocks))  # e.g., ['A', 'B', 'C', ..., 'T']

        return blocks, stacks
        
    def load_model(self, model_path, orig_n_rows=21, orig_n_cols=25):
        """
        加载并初始化CNN+Transformer模型，支持尺寸自适应插值。
    
        参数:
            model_path (str): 模型权重文件的路径。
            orig_n_rows (int): 模型原始训练时使用的 n_rows。
            orig_n_cols (int): 模型原始训练时使用的 n_cols。
        """
        try:
            # --- 1. 使用当前问题的维度实例化模型 ---
            print(f"为当前问题实例化模型。尺寸 (L,R,C,Classes): ({self.n_layers}, {self.nn_n_rows}, {self.nn_n_cols}, {len(self.blocks)})")
            self.model = CNNTransformerClassifier(
                n_layers=self.n_layers,              # 根据当前问题计算
                n_rows=self.nn_n_rows,              # 根据当前问题计算
                n_cols=self.nn_n_cols,              # 根据当前问题计算
                n_classes=len(self.blocks),          # 关键修改：动态计算分类数
                
                # --- 以下超参数应与您加载的模型训练时一致 ---
                embed_dim=64,
                n_heads=4,
                n_hidden=256,
                num_transformer_layers=6,
                classifier_dropout_rate=0.1
            ).to(self.device)
    
            # --- 2. 加载 checkpoint ---
            checkpoint = torch.load(model_path, map_location=self.device)
    
            # --- 3. 调用我们自定义的、带有插值功能的加载方法 ---
            print(f"加载预训练模型... 原始训练尺寸: R={orig_n_rows}, C={orig_n_cols}")
            self.model.load_state_dict(
                checkpoint,
                orig_n_rows=orig_n_rows, # 传入原始尺寸
                orig_n_cols=orig_n_cols  # 传入原始尺寸
            )
            
            self.model.eval()
            self.log(f"成功从 {model_path} 加载并自适应调整了 CNN+Transformer 模型。")
    
        except Exception as e:
            self.log(f"加载模型时发生错误: {e}")
            import traceback
            traceback.print_exc() # 打印详细的错误堆栈，方便调试
            self.model = None


    # def load_model(self, model_path):
    #     try:
    #         self.model = CNNTransformerClassifier(
    #             n_layers=self.n_layers,  # 22 (基础图像通道数)
    #             n_rows=self.nn_n_rows,  # 21
    #             n_cols=self.nn_n_cols,  # 25
    #             embed_dim=64,  # Transformer嵌入维度
    #             n_heads=4,  # Transformer头数
    #             n_hidden=256,  # Transformer隐藏层维度
    #             n_classes=20,  # 分类任务的类别数 (例如动作数量)
    #             num_transformer_layers=6,  # Transformer层数
    #             classifier_dropout_rate=0.1
    #         ).to(self.device)
    #         self.model.load_state_dict(torch.load(model_path, map_location=self.device))
    #         self.model.eval()
    #         self.log(f"Loaded CNN+Transformer model from {model_path}")
    #     except Exception as e:
    #         self.log(f"Error loading model: {e}")
    #         self.model = None

    def is_goal(self, state):
        return state.stacks == self.goal.stacks

    def get_successors(self):
        successors = []
        stack_names = list(self.state.stacks.keys())
        for i in range(len(stack_names)):
            for j in range(len(stack_names)):
                if i != j and self.state.stacks[stack_names[i]]:
                    new_stacks = {k: v.copy() for k, v in self.state.stacks.items()}
                    block = new_stacks[stack_names[i]].pop()
                    new_stacks[stack_names[j]].append(block)
                    # 更新 current_fix_stack
                    new_fix_stack = self.update_fix_stack(new_stacks)
                    new_state = State(new_stacks, new_fix_stack)
                    action = (i + 1, j + 1)
                    successors.append((new_state, action))
        return successors


    def update_fix_stack(self, stacks):
        """根据当前栈状态更新 current_fix_stack"""
        for stack_name in self.fix_order:
            if not self.is_stack_fixed(stack_name, stacks, self.goal.stacks):
                return stack_name
        return None  # 所有栈都修复完成

    def is_stack_fixed(self, stack_name, current_stacks, goal_stacks):
        current_stack = current_stacks.get(stack_name, [])
        goal_stack = goal_stacks.get(stack_name, [])
        # 检查当前栈是否至少包含目标栈的全部块，且顺序从底部开始匹配
        if len(current_stack) < len(goal_stack):
            return False
        for i in range(len(goal_stack)):
            if current_stack[i] != goal_stack[i]:
                return False
        return True

    def heuristic(self, state):
        cost = 0
        goal_on_block = {}
        for stack_name, blocks in self.goal.stacks.items():
            for i in range(len(blocks)):
                if i == 0:
                    goal_on_block[blocks[i]] = stack_name
                else:
                    goal_on_block[blocks[i]] = blocks[i-1]
        current_on_block = {}
        for stack_name, blocks in state.stacks.items():
            for i in range(len(blocks)):
                if i == 0:
                    current_on_block[blocks[i]] = stack_name
                else:
                    current_on_block[blocks[i]] = blocks[i-1]
        for block, target in goal_on_block.items():
            current_pos = current_on_block.get(block)
            if current_pos != target:
                cost += 1
        return cost

    def log(self, message):
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')

    def calculate_dynamic_weights(self, parent_h, child_h, child_successors, best_confidence, worst_confidence):
        # 基础边界
        k_min_threshold = 0.2
        k_max_threshold = 5
        # 根据置信度调整 k_min 和 k_max
        confidence_threshold_high = 0.8 # 高置信度阈值
        confidence_threshold_low = 0.5   # 低置信度阈值

        delta = 0.01

        if child_h == 0:
            return 1.0, 1.0

        # 根据一致性要求计算 k_min 和 k_max
        if parent_h is not None and child_h > 0:
            k_min_candidate = max(0, (parent_h - 1) / child_h)

        child_h_values = [self.heuristic(s[0]) for s in child_successors]
        if child_h_values:
            k_max_candidate = min([round((1 + h_child) / child_h, 3) for h_child in child_h_values])

        # 最佳动作的 k_best
        if best_confidence > confidence_threshold_high:
            # 高置信度：允许更大的偏离
            k_best = k_min_threshold 
        elif best_confidence < confidence_threshold_low:
            # 低置信度：放弃指导
            k_best = 1
        else:
            # 中置信度：严格遵循一致性
            k_best = k_min_candidate + delta

        # 最差动作的 k_worst
        if worst_confidence > confidence_threshold_high:
            # 高置信度：允许更大的惩罚
            k_worst = k_max_threshold
        elif best_confidence < confidence_threshold_low:
            # 低置信度：放弃指导
            k_worst = 1
        else:
            # 中置信度：严格遵循一致性
            k_worst = k_max_candidate - delta

        return k_best, k_worst
    # def calculate_dynamic_weights(self, parent_h_adjusted, child_h_original):
    #     """
    #     在严格遵守一致性原则的前提下，计算启发值的动态调整系数 k。
    #     - k_best: 为“最佳动作”计算的缩减系数，尽可能小但绝不违反一致性。
    #     - k_worst: 为“最差动作”设定的系数，固定为1.0，以保证最优性。

    #     Args:
    #         parent_h_adjusted (float): 当前节点(父节点)的、已调整过的启发值。
    #         child_h_original (float): 后继节点(子节点)的、原始启发值。

    #     Returns:
    #         tuple[float, float]: 返回一个元组 (k_best, k_worst)。
    #     """

    #     # --- 边缘情况处理 ---
    #     # 如果子节点的原始启发值为0，任何缩放都无意义或会导致除零。
    #     if child_h_original == 0:
    #         return 1.0, 1.0  # (k_best, k_worst)

    #     # =================================================================
    #     #  1. 计算 k_best (用于奖励最佳动作)
    #     # =================================================================

    #     # a. 设定一个我们期望的“奖励”折扣，这个值可以根据实验调整
    #     scale_down_factor = 0.7
    #     h_proposed = child_h_original * scale_down_factor

    #     # b. 计算一致性原则 h(p) <= c(p,c) + h(c) 要求的数学下限
    #     #    假设 c(p, c) = 1, 则 h_adj(c) >= h_adj(p) - 1
    #     h_lower_bound = parent_h_adjusted - 1

    #     # c. 最终安全的、调整后的h值，是期望值和下限中的较大者
    #     final_h_best = max(h_proposed, h_lower_bound)

    #     # d. 将最终安全的h值，转换回缩放系数 k
    #     k_best = final_h_best / child_h_original

    #     # =================================================================
    #     #  2. 计算 k_worst (用于处理最差动作)
    #     # =================================================================

    #     # 遵照您的要求，为了保持代码结构一致性，并100%保证最优解，
    #     # 我们将最差动作的调整系数固定为 1.0。
    #     # 这意味着它的启发值将保持不变: h_adjusted = h_original * 1.0
    #     k_worst = 1.0

    #     return k_best, k_worst

    def a_star_search(self, llm=None,max_iterations=100000,is_consistency=False):
        self.state.g = 0
        self.state.h = self.heuristic(self.state)
        self.state.cost = self.state.g + self.state.h

        queue = [(self.state.cost, 0, self.state, [])]
        visited = set()
        count = 0
        self.check = []

        open(self.log_file, 'a', encoding='utf-8').close()

        if llm is None and self.model is None:
            self.log("Running A* without guidance:")
        elif self.model is not None:
            self.log("Running A* with CNN+Transformer model:")
        else:
            self.log(f"Running A* with LLM {llm.model}:")

        while queue:

            if count >= max_iterations:
                self.log(f"搜索达到最大迭代次数 {max_iterations}，未找到解决方案。")
                self.log(f"已搜索节点数：{count}")  # count here is the number of nodes expanded
                return None, count  # Return None for path, and the count of expanded nodes

            _, _, current_state, path = heapq.heappop(queue)

            if current_state in visited:
                continue
            visited.add(current_state)
            count += 1

            if len(self.check) < count:
                self.check.append(True)


            if self.is_goal(current_state):
                self.log(f"Solution found: {path}")
                self.log(f"Nodes searched: {count}")
                self.log(f"Path length: {len(path)}\n")
                return path, count

            self.state = current_state
            successors = self.get_successors()

            h_n_adjusted = current_state.h_adjusted if current_state.h_adjusted is not None else current_state.h


            if self.model is not None or llm is not None:

                if self.model is not None:

                    fix_stack_curr = current_state.stacks[current_state.current_fix_stack]
                    fix_stack_goal = self.goal.stacks.get(current_state.current_fix_stack, [])

                    priority=get_priority_task(current_state.current_fix_stack,fix_stack_curr,fix_stack_goal)

                    nn_result = self.model.evaluate_actions(current_state,self.goal,self,successors,current_state.current_fix_stack
                                                            ,priority)
                    best_action_idx = nn_result["best_action_idx"]
                    worst_action_idx = nn_result["worst_action_idx"]
                    best_confidence = nn_result["best_confidence"]
                    worst_confidence = nn_result["worst_confidence"]
                else:
                    llm_result, actions, current_issue, priority = llm.evaluate_actions(current_state, self.goal,
                                                                                        self,successors)
                    best_action = llm_result["best_action"]
                    best_reason = llm_result["best_reason"]
                    worst_action = llm_result["worst_action"]
                    worst_reason = llm_result["worst_reason"]
                    best_action_idx = int(best_action) - 1 if best_action != "uncertain" else -1
                    worst_action_idx = int(worst_action) - 1 if worst_action != "uncertain" else -1

                    self.log(
                        f"\nNode {count}: Current fix stack: **{current_state.current_fix_stack}** Current issue: {current_issue} Priority task: {priority}")
                    actions = [action for _, action in successors]

                # self.log(f"Node {count}: Current state: {current_state.stacks}")
                # self.log(f"Node {count}: Goal state: {self.goal.stacks}")
                for i, (next_state, action) in enumerate(successors):
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    h_original = next_state.h

                    #获取后继状态的后继状态——一致性计算
                    original_state = self.state
                    self.state = next_state
                    next_successors = self.get_successors()
                    self.state = original_state

                    if is_consistency:
                        # k_best, k_worst = self.calculate_dynamic_weights(
                        #     h_n_adjusted, next_state.h
                        # )
                        k_best, k_worst = self.calculate_dynamic_weights(
                            h_n_adjusted, next_state.h, next_successors, 
                            best_confidence=nn_result["best_confidence"],
                            worst_confidence=nn_result["worst_confidence"]
                        )
                    else:
                        # k_best, k_worst=0.667,1.5
                        k_best, k_worst=0.667,1


                    if best_action_idx != -1 and i == best_action_idx:
                        next_state.g -= 1  # Optional: keep this adjustment if desired
                        next_state.h_adjusted = h_original * k_best
                        next_state.cost = next_state.g + next_state.h_adjusted
                        # if llm is not None:
                        #     self.log(
                        #         f"Node {count}: LLM suggests Best Action '{action}'")
                        #     self.log(f"Best Reason: {best_reason}")
                        # else:
                        #     self.log(
                        #         f"Node {count}: LLM suggests Best Action '{action}'")
                    elif worst_action_idx != -1 and i == worst_action_idx:
                        next_state.h_adjusted = h_original * k_worst
                        next_state.cost = next_state.g + next_state.h_adjusted
                        # if llm is not None:
                        #     self.log(
                        #         f"Node {count}: LLM suggests Worst Action '{action}'")
                        #     self.log(f"Worst Reason: {worst_reason}")
                        # else:
                        #     self.log(
                        #         f"Node {count}: LLM suggests Worst Action '{action}'")
                    else:
                        next_state.h_adjusted = h_original
                        next_state.cost = next_state.g + next_state.h_adjusted

                # action_cost_list = [
                #     f"'{action}': cost={next_state.cost}, h_orig={next_state.h}, h_adj={next_state.h_adjusted}"
                #     for next_state, action in successors
                # ]
                # formatted_list = f"[{', '.join(action_cost_list)}]"
                # self.log(f"节点 {count}：调整后的动作代价：{formatted_list}")
            else:
                for next_state, action in successors:
                    next_state.g = current_state.g + 1
                    next_state.h = self.heuristic(next_state)
                    next_state.h_adjusted = next_state.h
                    next_state.cost = next_state.g + next_state.h

            # for next_state, action in successors:
            #     h_m_adjusted = next_state.h_adjusted
            #     if h_n_adjusted > 1 + h_m_adjusted:
            #         self.check[count - 1] = False
            #         self.log(f"节点 {count} 检测到不一致："
            #                  f"动作={action}==={h_n_adjusted} > 1 + {h_m_adjusted}")

            for next_state, action in successors:
                next_state.parent = current_state
                next_state.action = action
                heapq.heappush(queue, (next_state.cost, count, next_state, path + [action]))

        self.log("未找到解决方案\n")
        return None, -1