import torch
import torch.nn as nn
import pandas as pd
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import random
from sklearn.model_selection import KFold


class BlocksWorldDataset(Dataset):
    def __init__(self, matrix_file, label_file, orig_shape, new_shape):
        #  (n_samples, 22, 21, 25)
        matrix = pd.read_csv(matrix_file).values.reshape(-1, orig_shape[0], orig_shape[1], orig_shape[2])
        matrix = prepare_data(matrix, orig_shape=orig_shape, new_shape=new_shape)
        assert not np.isnan(matrix).any(), "Matrix contains NaN values"
        assert not np.isinf(matrix).any(), "Matrix contains Inf values"
        self.matrix = matrix

        # 加载标签并验证范围
        labels = pd.read_csv(label_file).values
        print(f"Labels: min={labels.min()}, max={labels.max()}")

        self.labels = labels

    def __len__(self):
        return len(self.matrix)

    def __getitem__(self, idx):
        matrix = torch.tensor(self.matrix[idx], dtype=torch.float32)
        labels = torch.tensor(self.labels[idx], dtype=torch.long)
        return matrix, labels

class CNNTransformerClassifier(nn.Module):
    def __init__(self, n_layers=22, n_rows=21, n_cols=25, embed_dim=128, n_heads=8, n_hidden=512, n_classes=20,
                 num_transformer_layers=8):
        super().__init__()
        self.n_layers = n_layers
        self.n_rows = n_rows
        self.n_cols = n_cols
        self.embed_dim = embed_dim

        self.max_actions_layers =n_layers-2

        self.cnn = nn.Sequential(
            nn.Conv2d(n_layers, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Dropout(p=0.2),
            nn.MaxPool2d(2)
            # Output shape: (batch_size, 64, 10, 12) calculated from (21,25) input
        )

        # Calculate sequence length after CNN
        with torch.no_grad():
            dummy_input = torch.zeros(1, n_layers, n_rows, n_cols)
            cnn_output_shape = self.cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]  # 64
            self.cnn_out_h = cnn_output_shape[2]  # (n_rows + 1) // 2 = 11 for n_rows=21
            self.cnn_out_w = cnn_output_shape[3]  # (n_cols + 1) // 2 = 13 for n_cols=25
            self.sequence_length = self.cnn_out_h * self.cnn_out_w  # 11 * 13 = 143

        # Linear projection from CNN channel dim to Transformer embed_dim (if needed)
        # If cnn_out_channels == embed_dim, this could potentially be an Identity layer
        self.input_proj = nn.Linear(self.cnn_out_channels, embed_dim)

        # Positional embedding for the sequence
        # Shape: (1, sequence_length, embed_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.sequence_length, embed_dim))

        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim, nhead=n_heads, dim_feedforward=n_hidden, batch_first=True,
            dropout=0.3, #提升泛化性
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # Classification heads (using the aggregated output)
        self.fc_best = nn.Linear(embed_dim, n_classes)
        self.fc_worst = nn.Linear(embed_dim, n_classes)

        self._initialize_weights()

    def _initialize_weights(self):
        # Initialize positional embedding
        nn.init.normal_(self.pos_embed, std=.02) # Common initialization for pos embed

        for m in self.modules():
            if isinstance(m, nn.Linear):
                 # Slightly different init for Linear layers is common
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            # Note: Initialization within TransformerEncoderLayer might be handled internally
            # or you can customize it further if needed, but PyTorch defaults are often okay.

    def forward(self, x):
        batch_size = x.size(0)

        # 1. Pass through CNN
        x = self.cnn(x)  # Shape: (batch_size, 64, 11, 13)

        # 2. Reshape for Transformer
        x = x.flatten(2)  # Shape: (batch_size, 64, 143)  # 11 * 13 = 143
        x = x.transpose(1, 2)  # Shape: (batch_size, 143, 64)

        # 3. Project to embed_dim
        x = self.input_proj(x)  # Shape: (batch_size, 143, 64)  # embed_dim = 64

        # 4. Add positional embedding
        x = x + self.pos_embed  # Shape: (batch_size, 143, 64)  # pos_embed: (1, 143, 64)

        # 5. Pass through Transformer Encoder
        x = self.transformer(x)  # Shape: (batch_size, 143, 64)

        # 6. Mean pooling
        x = x.mean(dim=1)  # Shape: (batch_size, 64)  # Mean over sequence_length (143)

        # 7. Classification heads
        best_logits = self.fc_best(x)  # Shape: (batch_size, 20)  # n_classes = 20
        worst_logits = self.fc_worst(x)  # Shape: (batch_size, 20)  # n_classes = 20

        return best_logits, worst_logits

    def freeze_cnn_layers(self, freeze_first=True, freeze_second=False):
        for name, param in self.cnn.named_parameters():
            if freeze_first and ("0.weight" in name or "0.bias" in name):
                param.requires_grad = False
            if freeze_second and ("4.weight" in name or "4.bias" in name):  # Updated index for second Conv2d
                param.requires_grad = False

    # --- New evaluate_actions method ---
    # @torch.no_grad() # Ensure no gradients are computed during evaluation
    # def evaluate_actions(self, current_state, goal_state, planner, successors):
    #     """
    #     使用神经网络评估给定状态下的后继动作。

    #     Args:
    #         current_state (State): 当前状态对象。
    #         goal_state (State): 目标状态对象。
    #         planner (GraphPlanningBlocksWorld): 规划器实例，用于访问 state_to_matrix,
    #                                         blocks, tables, n_rows, n_cols 等。
    #         successors (list): 后继状态和动作的列表 [(State, action_str), ...]。

    #     Returns:
    #         dict: 包含预测的最优/最劣动作索引及其置信度的字典。
    #             {
    #                 'best_action_idx': int, 
    #                 'worst_action_idx': int,
    #                 'best_confidence': float, 
    #                 'worst_confidence': float,
    #                 'best_probs': np.ndarray,  # 可选：所有动作的最佳概率
    #                 'worst_probs': np.ndarray  # 可选：所有动作的最差概率
    #             }
    #             索引对应于 successors 列表。如果无法预测则返回 -1 和 0.0。
    #     """
    #     self.eval()  # Ensure model is in evaluation mode

    #     # 1. Prepare the input tensor using the planner's context
    #     # Input shape expected by model's forward: (1, n_layers, n_rows, n_cols)
    #     n_layers = 2 + self.max_actions_layers
    #     n_rows, n_cols = planner.nn_n_rows, planner.nn_n_cols
        
    #     # Check if planner has necessary attributes
    #     if not all(hasattr(planner, attr) for attr in ['state_to_matrix', 'nn_n_rows', 'nn_n_cols', 'device']):
    #         print("Error: Planner object missing required attributes for NN input preparation.")
    #         return {
    #             "best_action_idx": -1, 
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }
            
    #     n_input_layers = 2 + self.max_actions_layers
    #     input_tensor_np = np.zeros((1, n_input_layers, planner.nn_n_rows, planner.nn_n_cols), dtype=np.float32)

    #     try:
    #         # Layer 0: Goal State
    #         input_tensor_np[0, 0] = planner.state_to_matrix(goal_state.config)
    #         # Layer 1: Current State
    #         input_tensor_np[0, 1] = planner.state_to_matrix(current_state.config)
    #         # Layer 2 onwards: Successor States (up to max_actions_layers)
    #         num_successors_to_encode = min(len(successors), self.max_actions_layers)
    #         for i in range(num_successors_to_encode):
    #             next_state, _ = successors[i]
    #             input_tensor_np[0, 2 + i] = planner.state_to_matrix(next_state.config)
    #     except Exception as e:
    #         print(f"Error preparing state matrices for NN input: {e}")
    #         return {
    #             "best_action_idx": -1, 
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }

    #     # Convert to torch tensor and move to the correct device
    #     nn_input = torch.from_numpy(input_tensor_np).to(planner.device)  # Use planner's device

    #     # 2. Perform inference using the model's forward method
    #     try:
    #         best_logits, worst_logits = self.forward(nn_input)  # Call the model itself
    #     except Exception as e:
    #         print(f"Error during model forward pass in evaluate_actions: {e}")
    #         return {
    #             "best_action_idx": -1, 
    #             "worst_action_idx": -1,
    #             "best_confidence": 0.0,
    #             "worst_confidence": 0.0
    #         }

    #     # 3. Process logits to get indices and probabilities
    #     predicted_best_idx = -1
    #     predicted_worst_idx = -1
    #     best_confidence = 0.0
    #     worst_confidence = 0.0
    #     num_valid_successors = len(successors)  # Actual number of successors generated

    #     # Consider predictions only up to the number of actual successors OR max_actions_layers,
    #     # whichever is smaller, as logits correspond to the first max_actions_layers slots.
    #     num_predictions_possible = min(num_valid_successors, self.max_actions_layers)

    #     if num_predictions_possible > 0:
    #         # Slice logits to match the number of possible predictions
    #         valid_best_logits = best_logits[0, :num_predictions_possible]
    #         valid_worst_logits = worst_logits[0, :num_predictions_possible]

    #         # Compute probabilities using softmax
    #         best_probs = torch.softmax(valid_best_logits, dim=-1)
    #         worst_probs = torch.softmax(valid_worst_logits, dim=-1)

    #         # Get indices
    #         predicted_best_idx = torch.argmax(best_probs).item() # Highest probability is best action
    #         predicted_worst_idx = torch.argmax(worst_probs).item()  # Highest probability is worst action

    #         # Get confidences
    #         best_confidence = best_probs[predicted_best_idx].item()
    #         worst_confidence = worst_probs[predicted_worst_idx].item()

    #     return {
    #         "best_action_idx": predicted_best_idx,
    #         "worst_action_idx": predicted_worst_idx,
    #         "best_confidence": best_confidence,
    #         "worst_confidence": worst_confidence,
    #         "best_probs": best_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([]),  # Optional
    #         "worst_probs": worst_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([])  # Optional
    #     }

    @torch.no_grad()
    def evaluate_actions(self, current_state, goal_state, planner, successors, random_perturb_prob=0.):
        """
        使用神经网络评估给定状态下的后继动作，支持随机扰动以测试框架健壮性。

        Args:
            current_state (State): 当前状态对象。
            goal_state (State): 目标状态对象。
            planner (GraphPlanningBlocksWorld): 规划器实例，用于访问 state_to_matrix,
                                            blocks, tables, n_rows, n_cols 等。
            successors (list): 后继状态和动作的列表 [(State, action_str), ...]。
            random_perturb_prob (float): 随机扰动的概率（0.0 到 1.0），默认 0.1（10%）。

        Returns:
            dict: 包含预测的最优/最劣动作索引及其置信度的字典。
                {
                    'best_action_idx': int, 
                    'worst_action_idx': int,
                    'best_confidence': float, 
                    'worst_confidence': float,
                    'best_probs': np.ndarray,  # 可选：所有动作的最佳概率
                    'worst_probs': np.ndarray  # 可选：所有动作的最差概率
                }
                索引对应于 successors 列表。如果无法预测则返回 -1 和 0.0。
        """
        self.eval()  # Ensure model is in evaluation mode

        # 1. Prepare the input tensor using the planner's context
        n_layers = 2 + self.max_actions_layers
        n_rows, n_cols = planner.nn_n_rows, planner.nn_n_cols
        
        if not all(hasattr(planner, attr) for attr in ['state_to_matrix', 'nn_n_rows', 'nn_n_cols', 'device']):
            print("Error: Planner object missing required attributes for NN input preparation.")
            return {
                "best_action_idx": -1, 
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }
            
        n_input_layers = 2 + self.max_actions_layers
        input_tensor_np = np.zeros((1, n_input_layers, planner.nn_n_rows, planner.nn_n_cols), dtype=np.float32)

        try:
            input_tensor_np[0, 0] = planner.state_to_matrix(goal_state.stacks)
            input_tensor_np[0, 1] = planner.state_to_matrix(current_state.stacks)
            num_successors_to_encode = min(len(successors), self.max_actions_layers)
            for i in range(num_successors_to_encode):
                next_state, _ = successors[i]
                input_tensor_np[0, 2 + i] = planner.state_to_matrix(next_state.stacks)
        except Exception as e:
            print(f"Error preparing state matrices for NN input: {e}")
            return {
                "best_action_idx": -1, 
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }

        nn_input = torch.from_numpy(input_tensor_np).to(planner.device)

        # 2. Perform inference
        try:
            best_logits, worst_logits = self.forward(nn_input)
        except Exception as e:
            print(f"Error during model forward pass in evaluate_actions: {e}")
            return {
                "best_action_idx": -1, 
                "worst_action_idx": -1,
                "best_confidence": 0.0,
                "worst_confidence": 0.0
            }

        # 3. Process logits to get indices and probabilities
        predicted_best_idx = -1
        predicted_worst_idx = -1
        best_confidence = 0.0
        worst_confidence = 0.0
        num_valid_successors = len(successors)
        num_predictions_possible = min(num_valid_successors, self.max_actions_layers)

        if num_predictions_possible > 0:
            # Slice logits to match the number of possible predictions
            valid_best_logits = best_logits[0, :num_predictions_possible]
            valid_worst_logits = worst_logits[0, :num_predictions_possible]

            # Compute probabilities using softmax
            best_probs = torch.softmax(valid_best_logits, dim=-1)
            worst_probs = torch.softmax(valid_worst_logits, dim=-1)

            # Get model-predicted indices
            model_best_idx = torch.argmax(best_probs).item()  # Highest probability is best action
            model_worst_idx = torch.argmax(worst_probs).item()  # Highest probability is worst action

            # Get confidences based on model predictions
            best_confidence = best_probs[model_best_idx].item()
            worst_confidence = worst_probs[model_worst_idx].item()

            # Apply random perturbation with specified probability
            if random.random() < random_perturb_prob:
                # Randomly select best action index
                predicted_best_idx = random.randint(0, num_predictions_possible - 1)
                # Ensure worst action is different from best action (optional, to avoid conflicts)
                available_worst_indices = [i for i in range(num_predictions_possible) if i != predicted_best_idx]
                if available_worst_indices:
                    predicted_worst_idx = random.choice(available_worst_indices)
                else:
                    predicted_worst_idx = predicted_best_idx  # Fallback, though unlikely
                print(f"Random perturbation applied: best_idx={predicted_best_idx}, worst_idx={predicted_worst_idx}")
            else:
                # Use model predictions
                predicted_best_idx = model_best_idx
                predicted_worst_idx = model_worst_idx

        return {
            "best_action_idx": predicted_best_idx,
            "worst_action_idx": predicted_worst_idx,
            "best_confidence": best_confidence,
            "worst_confidence": worst_confidence,
            "best_probs": best_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([]),
            "worst_probs": worst_probs.cpu().numpy() if num_predictions_possible > 0 else np.array([])
        }

def train_model(matrix_file, label_file, epochs=10, batch_size=32, lr=1e-3, 
                model_path="cnn_transformer_model.pth", orig_shape=(22, 21, 25), new_shape=(22, 21, 25)):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    dataset = BlocksWorldDataset(matrix_file, label_file, orig_shape=orig_shape,new_shape=new_shape)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    # 初始化模型
    # n_classes是输出分类数目，应为矩阵行数减1（clear）占据的行
    model = CNNTransformerClassifier(n_layers=new_shape[0],
                                     n_rows=new_shape[1],
                                     n_cols=new_shape[2],
                                     n_classes=new_shape[1]-1).to(device)
    
    # 检查是否存在已有模型文件，若存在则加载
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"Loaded existing model from {model_path} for incremental training")
    else:
        print(f"No existing model found at {model_path}, starting training from scratch")

    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)  # L2正则化

    for epoch in range(epochs):
        model.train()
        total_loss = 0
        total_batches = 0
        for batch_matrix, batch_labels in dataloader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)
            
            optimizer.zero_grad()
            best_logits, worst_logits = model(batch_matrix)
            
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1
            
            loss = 0
            if best_mask.any():
                loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                loss += loss_best
            if worst_mask.any():
                loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                loss += loss_worst
            
            if loss != 0:
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
                total_batches += 1
        
        avg_loss = total_loss / total_batches if total_batches > 0 else 0
        print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}")

    # 保存更新后的模型
    torch.save(model.state_dict(), model_path)
    print(f"Model saved to {model_path}")
    return model

def evaluate_model(model, matrix_file, label_file, orig_shape=(22, 21, 25), new_shape=(22, 21, 25), batch_size=32):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.eval()
    
    dataset = BlocksWorldDataset(matrix_file, label_file,orig_shape=orig_shape,new_shape=new_shape)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0
    
    with torch.no_grad():
        for batch_matrix, batch_labels in dataloader:
            batch_matrix = batch_matrix.to(device)
            batch_labels = batch_labels.to(device)
            
            best_logits, worst_logits = model(batch_matrix)
            best_preds = torch.argmax(best_logits, dim=1)
            worst_preds = torch.argmax(worst_logits, dim=1)
            
            best_mask = batch_labels[:, 0] != -1
            worst_mask = batch_labels[:, 1] != -1
            
            if best_mask.any():
                true_best = batch_labels[best_mask, 0] - 2
                correct_best += (best_preds[best_mask] == true_best).sum().item()
                total_best += best_mask.sum().item()
            
            if worst_mask.any():
                true_worst = batch_labels[worst_mask, 1] - 2
                correct_worst += (worst_preds[worst_mask] == true_worst).sum().item()
                total_worst += worst_mask.sum().item()
    
    acc_best = correct_best / total_best if total_best > 0 else 0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0
    print(f"Best Action Accuracy: {acc_best:.4f} ({correct_best}/{total_best})")
    print(f"Worst Action Accuracy: {acc_worst:.4f} ({correct_worst}/{total_worst})")

def train_model_with_kfold(matrix_file, label_file, shape=(22, 21, 25), n_folds=5, epochs=500,
                           batch_size=32, lr=1e-3, patience=10, model_path="cnn_transformer_model.pth"):
    """
    使用n折交叉验证和早停机制训练模型。
    
    Args:
        matrix_file (str): 输入矩阵文件路径。
        label_file (str): 标签文件路径。
        shape (tuple): 输入矩阵的形状 (n_layers, n_rows, n_cols)。
        n_folds (int): 交叉验证的折数。
        epochs (int): 最大训练轮数。
        batch_size (int): 批大小。
        lr (float): 学习率。
        patience (int): 早停的耐心值（验证损失不再下降的轮数）。
        model_path (str): 模型保存路径。
    
    Returns:
        list: 每折的最佳模型和其验证准确率。
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 加载完整数据集
    dataset = BlocksWorldDataset(matrix_file, label_file, shape,shape)
    
    # 初始化 KFold
    kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)
    fold_results = []

    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        print(f"\nFold {fold + 1}/{n_folds}")
        
        # 创建训练和验证子集
        train_subset = torch.utils.data.Subset(dataset, train_idx)
        val_subset = torch.utils.data.Subset(dataset, val_idx)
        
        train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)

        # 初始化模型
        model = CNNTransformerClassifier(
            n_layers=shape[0],
            n_rows=shape[1],
            n_cols=shape[2],
            n_classes=shape[1]-1
        ).to(device)
        
        # 检查是否加载已有模型
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            print(f"Loaded existing model from {model_path} for fold {fold + 1}")
        else:
            print(f"Starting training from scratch for fold {fold + 1}")

        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        
        # 早停参数
        best_val_loss = float('inf')
        epochs_no_improve = 0
        best_model_state = None

        for epoch in range(epochs):
            # 训练阶段
            model.train()
            total_train_loss = 0
            total_batches = 0
            
            for batch_matrix, batch_labels in train_loader:
                batch_matrix = batch_matrix.to(device)
                batch_labels = batch_labels.to(device)
                
                optimizer.zero_grad()
                best_logits, worst_logits = model(batch_matrix)
                
                best_mask = batch_labels[:, 0] != -1
                worst_mask = batch_labels[:, 1] != -1
                
                loss = 0
                if best_mask.any():
                    loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                    loss += loss_best
                if worst_mask.any():
                    loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                    loss += loss_worst
                
                if loss != 0:
                    loss.backward()
                    optimizer.step()
                    total_train_loss += loss.item()
                    total_batches += 1
            
            avg_train_loss = total_train_loss / total_batches if total_batches > 0 else 0

            # 验证阶段
            model.eval()
            total_val_loss = 0
            total_val_batches = 0
            correct_best = 0
            correct_worst = 0
            total_best = 0
            total_worst = 0
            
            with torch.no_grad():
                for batch_matrix, batch_labels in val_loader:
                    batch_matrix = batch_matrix.to(device)
                    batch_labels = batch_labels.to(device)
                    
                    best_logits, worst_logits = model(batch_matrix)
                    
                    best_mask = batch_labels[:, 0] != -1
                    worst_mask = batch_labels[:, 1] != -1
                    
                    val_loss = 0
                    if best_mask.any():
                        val_loss_best = criterion(best_logits[best_mask], batch_labels[best_mask, 0] - 2)
                        val_loss += val_loss_best
                        true_best = batch_labels[best_mask, 0] - 2
                        best_preds = torch.argmax(best_logits[best_mask], dim=1)
                        correct_best += (best_preds == true_best).sum().item()
                        total_best += best_mask.sum().item()
                    if worst_mask.any():
                        val_loss_worst = criterion(worst_logits[worst_mask], batch_labels[worst_mask, 1] - 2)
                        val_loss += val_loss_worst
                        true_worst = batch_labels[worst_mask, 1] - 2
                        worst_preds = torch.argmax(worst_logits[worst_mask], dim=1)
                        correct_worst += (worst_preds == true_worst).sum().item()
                        total_worst += worst_mask.sum().item()
                    
                    if val_loss != 0:
                        total_val_loss += val_loss.item()
                        total_val_batches += 1
            
            avg_val_loss = total_val_loss / total_val_batches if total_val_batches > 0 else 0
            acc_best = correct_best / total_best if total_best > 0 else 0
            acc_worst = correct_worst / total_worst if total_worst > 0 else 0
            
            print(f"Epoch {epoch + 1}/{epochs}, Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"Best Acc: {acc_best:.4f}, Worst Acc: {acc_worst:.4f}")

            # 早停检查
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                best_model_state = model.state_dict()
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
            
            if epochs_no_improve >= patience:
                print(f"Early stopping triggered after {epoch + 1} epochs")
                break

        # 保存该折的最佳模型
        fold_model_path = f"{model_path}_fold_{fold + 1}.pth"
        torch.save(best_model_state, fold_model_path)
        print(f"Best model for fold {fold + 1} saved to {fold_model_path}")
        
        # 记录该折的结果
        fold_results.append({
            'fold': fold + 1,
            'model_path': fold_model_path,
            'val_loss': best_val_loss,
            'best_acc': acc_best,
            'worst_acc': acc_worst
        })

    # 打印交叉验证结果
    print("\nCross-Validation Results:")
    for result in fold_results:
        print(f"Fold {result['fold']}: Val Loss = {result['val_loss']:.4f}, "
              f"Best Acc = {result['best_acc']:.4f}, Worst Acc = {result['worst_acc']:.4f}")

    # 返回所有折的结果
    return fold_results

def prepare_data(data, orig_shape,new_shape):
    batch_size = data.shape[0]
    new_data = np.zeros((batch_size, new_shape[0], new_shape[1], new_shape[2]))
    new_data[:, :orig_shape[0], :orig_shape[1], :orig_shape[2]] = data  # data: (batch_size, 22, 21, 25)
    return new_data

# if __name__ == "__main__":
    # # 生成样本
    # with open("astar_with_llm_2025-04-01-195134.log", "r", encoding="utf-8") as f:
    #     log_content = f.read()
    # process_log_to_matrix(log_content)
    
    # # 训练和评估
    # matrix_file = "data/samples0417_sorted_matrix.csv"
    # label_file = "data/samples0417_sorted_labels.csv"
    # shape = (17,18,17)
    # model_path="model/cnn_transformer_model_dual_stack.pth"
    # lr = 1e-4 if os.path.exists(model_path) else 3e-4
    # model = train_model(matrix_file, label_file, epochs=100, batch_size=32, lr=lr,
    #                     model_path=model_path, shape=shape)
    # evaluate_model(model, matrix_file, label_file, shape=shape, batch_size=32)

    # # 进行5折交叉验证训练
    # matrix_file = "data/samples0422_matrix.csv"
    # label_file = "data/samples0422_labels.csv"
    # shape = (17,18,17)
    # model_path="cnn_transformer_model_dual_staccks.pth"
    # lr = 1e-4 if os.path.exists(model_path) else 3e-4
    #
    # fold_results = train_model_with_kfold(
    #     matrix_file=matrix_file,
    #     label_file=label_file,
    #     shape=shape,
    #     n_folds=5,
    #     epochs=100,
    #     batch_size=32,
    #     lr=lr,
    #     patience=10,
    #     model_path=model_path
    # )
    #
    # # 评估每个折的模型
    # device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # for result in fold_results:
    #     print(f"\nEvaluating model from fold {result['fold']}")
    #     model = CNNTransformerClassifier(
    #         n_layers=shape[0],
    #         n_rows=shape[1],
    #         n_cols=shape[2],
    #         n_classes=shape[1]-3
    #     ).to(device)
    #     model.load_state_dict(torch.load(result['model_path'], map_location=device))
    #     evaluate_model(model, matrix_file, label_file, orig_shape=shape, new_shape=shape, batch_size=32)

    # # 扩展学习设计
    # # 训练和评估
    # matrix_file = "data/samples0422_sorted_matrix.csv"
    # label_file = "data/samples0422_sorted_labels.csv"
    # # orig_shape = (12,13,11)
    # orig_shape = (17,18,17)
    # new_shape = (17,18,17)
    # model_path="model/cnn_transformer_model_15_blocks_dual_staccks.pth"
    # lr = 1e-4 if os.path.exists(model_path) else 3e-4
    # model = train_model(matrix_file, label_file, epochs=100, batch_size=32, lr=lr,
    #                     model_path=model_path, orig_shape=orig_shape, new_shape=new_shape)
    # evaluate_model(model, matrix_file, label_file, orig_shape=orig_shape, new_shape=new_shape, batch_size=32)
