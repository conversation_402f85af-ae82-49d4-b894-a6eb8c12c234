import copy

# ===================================================================
# 1. 辅助函数 (Helper Functions)
# ===================================================================

def apply_move(state: dict, move: tuple) -> dict:
    """应用一个移动操作到当前状态。"""
    from_stack_key = f'Stack{move[0]}'
    if not state.get(from_stack_key) or not state[from_stack_key]:
        raise ValueError(f"错误：优化算法引入了非法移动，尝试从一个空的堆栈 {from_stack_key} 移动。Move: {move}, State: {state}")
    new_state = copy.deepcopy(state)
    to_stack_key = f'Stack{move[1]}'
    block = new_state[from_stack_key].pop()
    new_state[to_stack_key].append(block)
    return new_state

def get_state_hash(state: dict) -> str:
    """为状态生成一个可哈希的字符串表示。"""
    sorted_keys = sorted(state.keys())
    # 为了让哈希值更稳定，将block转换为字符串
    return "".join(f"{key}:{','.join(map(str, state[key]))};" for key in sorted_keys)

# ### MODIFICATION ###: 新增的验证函数，用于CPMP问题
def is_valid_cpmp_solution(state: dict) -> bool:
    """
    检查一个状态是否是合法的CPMP问题解。
    规则：所有非空的栈都必须是内部有序的（大数在下）。
    """
    for stack in state.values():
        if len(stack) <= 1:
            continue
        # 假设箱子是数字，越大越应该在下面
        for i in range(len(stack) - 1):
            if stack[i] < stack[i+1]:
                # 发现一个逆序，说明不是解
                return False
    # 所有非空栈都检查通过
    return True

# ===================================================================
# 2. 各优化阶段的实现 (保持不变)
# ===================================================================

def optimize_phase1_iterative(path: list) -> list:
    # ... 此函数代码保持不变 ...
    # 为了简洁，此处省略
    pass

def optimize_phase2_iterative(path: list, initial_state: dict) -> list:
    # ... 此函数代码保持不变 ...
    # 为了简洁，此处省略
    pass

def optimize_phase3_iterative(path: list, initial_state: dict) -> list:
    # ... 此函数代码保持不变 ...
    # 为了简洁，此处省略
    pass

# ===================================================================
# 3. 主流程编排 (保持不变)
# ===================================================================

def optimize_path(original_path, initial_state):
    # ... 此函数代码保持不变 ...
    # 为了简洁，此处省略
    pass

# ### MODIFICATION ###: 为了方便演示，将被省略的函数代码粘贴回来
def optimize_phase1_iterative(path: list) -> list:
    while True:
        optimized_in_pass = False
        new_path_work_in_progress = []
        i = 0
        original_path_for_pass = list(path) # Create a copy for iteration
        while i < len(original_path_for_pass):
            if i + 1 < len(original_path_for_pass) and \
                    original_path_for_pass[i][0] == original_path_for_pass[i + 1][1] and \
                    original_path_for_pass[i][1] == original_path_for_pass[i + 1][0]:
                print(f"  - [Phase 1] 消除连续往返: {original_path_for_pass[i]} 和 {original_path_for_pass[i + 1]}")
                path = new_path_work_in_progress + original_path_for_pass[i + 2:]
                optimized_in_pass = True
                break
            if i + 1 < len(original_path_for_pass) and \
                    original_path_for_pass[i][1] == original_path_for_pass[i + 1][0]:
                A, B = original_path_for_pass[i]
                _, C = original_path_for_pass[i + 1]
                shortcut_move = (A, C)
                print(f"  - [Phase 1] 压缩连续中介: {original_path_for_pass[i]}, {original_path_for_pass[i + 1]}  —>  {shortcut_move}")
                path = new_path_work_in_progress + [shortcut_move] + original_path_for_pass[i + 2:]
                optimized_in_pass = True
                break
            new_path_work_in_progress.append(original_path_for_pass[i])
            i += 1
        if not optimized_in_pass:
            break
    return path

def optimize_phase2_iterative(path: list, initial_state: dict) -> list:
    while True:
        optimized_in_pass = False
        augmented_path = []
        current_state = initial_state
        for move in path:
            from_key = f'Stack{move[0]}'
            block = current_state[from_key][-1] if current_state.get(from_key) and current_state[from_key] else None
            augmented_path.append({'move': move, 'block': block})
            current_state = apply_move(current_state, move)
        i = 0
        while i < len(augmented_path) - 1 and not optimized_in_pass:
            block_to_track = augmented_path[i]['block']
            if block_to_track is None:
                i += 1
                continue
            for j in range(i + 1, len(augmented_path)):
                if augmented_path[j]['block'] == block_to_track:
                    move_i = augmented_path[i]['move']
                    move_j = augmented_path[j]['move']
                    S, I = move_i
                    I_prime, E = move_j
                    if I != I_prime: continue
                    inter_moves = [item['move'] for item in augmented_path[i + 1:j]]
                    is_independent = all(inter_move[0] not in (I, E) and inter_move[1] not in (I, E) for inter_move in inter_moves)
                    if is_independent:
                        shortcut_move = (S, E)
                        print(f"  - [Phase 2] 优化非连续中介: {move_i} ... {move_j} (中间有{len(inter_moves)}步)  —>  {shortcut_move}")
                        path_prefix = [item['move'] for item in augmented_path[:i]]
                        path_suffix = [item['move'] for item in augmented_path[j + 1:]]
                        path = path_prefix + [shortcut_move] + inter_moves + path_suffix
                        optimized_in_pass = True
                        break
            i += 1
        if not optimized_in_pass:
            break
    return path

def optimize_phase3_iterative(path: list, initial_state: dict) -> list:
    while True:
        optimized_in_pass = False
        state_sequence = [initial_state]
        state_map = {get_state_hash(initial_state): 0}
        for i, move in enumerate(path):
            current_state = state_sequence[-1]
            new_state = apply_move(current_state, move)
            new_state_hash = get_state_hash(new_state)
            if new_state_hash in state_map:
                j = state_map[new_state_hash]
                sub_path_to_replace = path[j: i + 1]
                print(f"  - [Phase 3] 消除全局状态循环: 移除子路径 {sub_path_to_replace}")
                path = path[:j] + path[i + 1:]
                optimized_in_pass = True
                break
            state_map[new_state_hash] = i + 1
            state_sequence.append(new_state)
        if not optimized_in_pass:
            break
    return path

def optimize_path(original_path, initial_state):
    # print("========= 开始路径优化流程 =========")
    # print(f"初始路径 (长度 {len(original_path)}): {original_path}")
    current_path = copy.deepcopy(original_path)
    # print("\n--- 执行 Phase 1: 基础模式匹配 ---")
    len_before = len(current_path)
    current_path = optimize_phase1_iterative(current_path)
    # print(f"--- Phase 1 结束: 路径长度从 {len_before} 变为 {len(current_path)} ---")
    # print("\n--- 执行 Phase 2: 上下文关联优化 ---")
    len_before = len(current_path)
    current_path = optimize_phase2_iterative(current_path, initial_state)
    # print(f"--- Phase 2 结束: 路径长度从 {len_before} 变为 {len(current_path)} ---")
    # print("\n--- 执行 Phase 3: 全局路径重构 ---")
    len_before = len(current_path)
    current_path = optimize_phase3_iterative(current_path, initial_state)
    # print(f"--- Phase 3 结束: 路径长度从 {len_before} 变为 {len(current_path)} ---")
    final_path = current_path
    print("\n========= 优化流程结束 =========")
    reduction_percent = (len(original_path) - len(final_path)) / len(original_path) * 100 if original_path else 0
    print(f"最终路径长度从 {len(original_path)} 减少到 {len(final_path)}，缩短了 {reduction_percent:.2f}%。")
    return final_path

# ===================================================================
# 4. 示例用法 (已修改)
# ===================================================================

if __name__ == '__main__':
    # 我们使用一个之前讨论过的、有解的无序状态作为例子
    start_state = {
        'Stack1': [1, 5, 2],
        'Stack2': [4, 3],
        'Stack3': []
    }
    
    # 这是一个由某个规划器（比如A*）找到的、未经优化的、冗长的路径
    # (这个路径是我手动构造的，它能达到一个有序状态，但包含大量冗余)
    path_to_optimize = [
        (1, 3), # S1 -> S3, [5,2] [4,3] [1]
        (1, 3), # S1 -> S3, [2] [4,3] [1,5]
        (2, 1), # S2 -> S1, [2,3] [4] [1,5]
        (2, 1), # S2 -> S1, [2,3,4] [] [1,5]
        (1, 2), # S1 -> S2, [2,3] [4] [1,5]  <- 无效往返开始
        (2, 1), # S2 -> S1, [2,3,4] [] [1,5]  <- 无效往返结束
        (3, 2), # S3 -> S2, [2,3,4] [5] [1]
        (3, 2), # S3 -> S2, [2,3,4] [5,1] []
        (1, 3), # S1 -> S3, [2,3] [5,1] [4]
        (1, 3), # S1 -> S3, [2] [5,1] [4,3]
        (1, 3), # S1 -> S3, [] [5,1] [4,3,2]  <- 最终状态 {S2:[5,1], S3:[4,3,2]} 是一个合法的解
    ]
    
    # 执行优化
    optimized_path = optimize_path(path_to_optimize, start_state)

    # 验证优化后的路径是否依然能达到一个合法的CPMP解
    final_state_from_optimized_path = start_state
    for move in optimized_path:
        final_state_from_optimized_path = apply_move(final_state_from_optimized_path, move)

    print("\n--- 验证结果 ---")
    print(f"优化路径达到的最终状态: {final_state_from_optimized_path}")

    # ### MODIFICATION ###: 使用新的验证函数
    if is_valid_cpmp_solution(final_state_from_optimized_path):
        print("✅ 验证成功：优化后的路径能够正确达到一个合法的有序状态！")
    else:
        print("❌ 验证失败：优化后的路径未能达到一个合法的有序状态！")