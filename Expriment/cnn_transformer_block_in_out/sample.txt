--- Sample 1 ---
stack_order=['Stack1', 'Stack4', 'Stack2', 'Stack3']
start_state={'Stack1': ['N', 'M', 'F', 'R', 'L'], 'Stack2': ['Q', 'I', 'T', 'K', 'D'], 'Stack3': ['S', 'H', 'P', 'E', 'G'], 'Stack4': ['C', 'J', 'A', 'B', 'O'], 'Stack5': []}
goal_state={'Stack1': ['Q', 'J', 'S', 'B', 'M'], 'Stack2': ['G', 'F', 'R', 'I', 'N'], 'Stack3': ['L', 'P', 'K', 'O', 'E'], 'Stack4': ['A', 'T', 'C', 'D', 'H'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2699.1004741191864 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 1), (4, 2), (4, 5), (4, 5), (4, 1), (3, 5), (3, 2), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 2), (5, 3), (5, 1), (5, 2), (5, 3), (5, 3), (5, 4), (5, 4), (5, 1), (4, 2), (4, 5), (4, 5), (3, 5), (3, 5), (3, 4), (5, 4), (5, 1), (5, 4), (5, 4), (3, 5), (3, 4), (2, 3), (2, 5), (2, 5), (2, 3), (2, 4), (5, 2), (5, 3), (5, 4), (5, 2), (5, 2), (3, 2), (3, 5), (3, 2), (5, 1), (5, 3), (4, 3), (1, 5), (1, 3), (4, 3), (5, 3)]
Nodes searched: 67
Path.Length: 62
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 19.790143489837646 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 1), (5, 2), (5, 3), (5, 3), (4, 3), (5, 3), (5, 3), (4, 3), (5, 3), (5, 3), (5, 3), (4, 1), (1, 5), (4, 1), (2, 4), (3, 5), (3, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (5, 1), (4, 5), (4, 5), (5, 1), (5, 1), (5, 1), (5, 4), (3, 2), (5, 3), (1, 5), (2, 5), (5, 1), (5, 3), (1, 5), (1, 3), (2, 3), (2, 1), (2, 5), (5, 1), (2, 5), (2, 4), (1, 5), (1, 5), (1, 4), (5, 4), (5, 1), (5, 1), (5, 4), (2, 5), (2, 5), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 2), (1, 3), (1, 5), (1, 2), (1, 5), (1, 5), (3, 5), (3, 1), (3, 5), (3, 2), (5, 2), (1, 2), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (1, 3), (1, 3)]
Nodes searched: 5285
Path.Length:102

--- Sample 2 ---
stack_order=['Stack4', 'Stack1', 'Stack2', 'Stack3']
start_state={'Stack1': ['M', 'G', 'T', 'B', 'K'], 'Stack2': ['J', 'E', 'H', 'S', 'P'], 'Stack3': ['L', 'C', 'F', 'I', 'N'], 'Stack4': ['D', 'O', 'Q', 'A', 'R'], 'Stack5': []}
goal_state={'Stack1': ['L', 'M', 'Q', 'T', 'R'], 'Stack2': ['J', 'A', 'K', 'H', 'O'], 'Stack3': ['C', 'D', 'P', 'G', 'F'], 'Stack4': ['S', 'E', 'I', 'B', 'N'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1920.1401343345642 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 2), (4, 5), (2, 5), (2, 5), (2, 4), (2, 5), (2, 4), (3, 2), (3, 4), (1, 5), (1, 4), (2, 4), (1, 5), (1, 5), (1, 2), (3, 5), (3, 5), (3, 1), (2, 1), (5, 2), (5, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 3), (5, 2), (5, 3), (5, 1), (2, 5), (2, 5), (2, 3), (2, 1), (5, 4), (5, 2), (5, 2), (5, 1), (2, 5), (2, 5), (2, 1), (2, 5), (5, 1), (5, 1), (5, 2), (3, 2), (4, 2), (1, 2), (3, 5), (3, 1), (3, 5), (1, 2), (1, 3), (5, 4), (5, 3), (2, 3), (1, 3), (4, 3)]
Nodes searched: 66
Path.Length: 60
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 0.94561767578125 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (2, 1), (2, 4), (3, 1), (3, 4), (1, 5), (3, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (5, 3), (5, 1), (5, 2), (5, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (3, 1), (5, 3), (5, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (5, 1), (5, 2), (5, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 3), (1, 3), (1, 5), (1, 3), (5, 3)]
Nodes searched: 263
Path.Length: 62

--- Sample 3 ---
stack_order=['Stack4', 'Stack3', 'Stack2', 'Stack1']
start_state={'Stack1': ['I', 'J', 'O', 'P', 'K'], 'Stack2': ['L', 'S', 'D', 'C', 'E'], 'Stack3': ['G', 'B', 'Q', 'T', 'H'], 'Stack4': ['R', 'A', 'N', 'F', 'M'], 'Stack5': []}
goal_state={'Stack1': ['S', 'O', 'T', 'H', 'B'], 'Stack2': ['K', 'N', 'A', 'G', 'M'], 'Stack3': ['D', 'E', 'Q', 'J', 'P'], 'Stack4': ['I', 'C', 'R', 'L', 'F'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2010.591914176941 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (2, 1), (2, 4), (5, 1), (5, 1), (5, 3), (5, 1), (5, 4), (2, 5), (2, 5), (2, 4), (5, 2), (5, 1), (5, 2), (5, 2), (5, 4), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (3, 4), (1, 5), (1, 3), (1, 5), (1, 4), (1, 5), (1, 3), (5, 1), (5, 4), (5, 1), (5, 3), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 3), (2, 5), (2, 5), (2, 3), (4, 2), (5, 1), (5, 2), (1, 2), (4, 5), (4, 2), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 2), (3, 1), (5, 2), (5, 3), (5, 1), (2, 1), (3, 1), (2, 1)]
Nodes searched: 70
Path.Length: 69
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 0.7095298767089844 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (2, 1), (2, 4), (5, 1), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (2, 5), (2, 5), (2, 4), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (5, 1), (5, 1), (5, 1), (5, 3), (5, 1), (5, 1), (5, 2), (1, 2), (5, 2), (5, 1), (5, 2), (1, 3), (1, 5), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (2, 1), (2, 1), (3, 2), (2, 5), (2, 5), (2, 1), (5, 1), (5, 1)]
Nodes searched: 133
Path.Length: 92

--- Sample 4 ---
stack_order=['Stack4', 'Stack3', 'Stack1', 'Stack2']
start_state={'Stack1': ['H', 'E', 'B', 'O', 'L'], 'Stack2': ['F', 'Q', 'K', 'C', 'T'], 'Stack3': ['I', 'S', 'P', 'D', 'R'], 'Stack4': ['M', 'J', 'N', 'A', 'G'], 'Stack5': []}
goal_state={'Stack1': ['J', 'F', 'H', 'B', 'I'], 'Stack2': ['D', 'A', 'K', 'S', 'T'], 'Stack3': ['Q', 'N', 'E', 'P', 'R'], 'Stack4': ['O', 'M', 'G', 'C', 'L'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2128.0103516578674 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 2), (1, 4), (5, 4), (5, 2), (5, 3), (5, 1), (5, 4), (2, 5), (2, 5), (2, 5), (2, 4), (5, 2), (5, 4), (3, 5), (3, 5), (3, 4), (3, 1), (3, 5), (3, 5), (2, 5), (2, 1), (2, 3), (2, 1), (4, 2), (5, 2), (5, 1), (5, 4), (5, 4), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (1, 5), (1, 3), (5, 1), (5, 3), (4, 3), (1, 3), (1, 5), (5, 4), (5, 2), (5, 3), (5, 4), (5, 1), (3, 1), (4, 3), (4, 1), (3, 5), (3, 1), (5, 1), (2, 5), (2, 3), (2, 5), (3, 2), (5, 1), (5, 2), (4, 2), (1, 2)]
Nodes searched: 67
Path.Length: 65
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 3.452843189239502 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 4), (5, 1), (5, 4), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (2, 5), (2, 4), (5, 1), (2, 5), (1, 5), (1, 2), (1, 3), (1, 4), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (2, 5), (2, 3), (5, 3), (1, 4), (1, 3), (5, 1), (5, 1), (5, 3), (5, 1), (5, 3), (2, 5), (1, 2), (1, 5), (1, 5), (1, 5), (5, 2), (5, 2), (5, 4), (5, 2), (5, 1), (2, 1), (5, 2), (4, 5), (2, 5), (2, 4), (2, 1), (4, 5), (4, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 2), (1, 2)]
Nodes searched: 942
Path.Length: 64

--- Sample 5 ---
stack_order=['Stack1', 'Stack4', 'Stack3', 'Stack2']
start_state={'Stack1': ['H', 'E', 'I', 'A', 'C'], 'Stack2': ['D', 'G', 'J', 'N', 'R'], 'Stack3': ['T', 'P', 'S', 'O', 'F'], 'Stack4': ['Q', 'K', 'B', 'M', 'L'], 'Stack5': []}
goal_state={'Stack1': ['O', 'P', 'E', 'Q', 'N'], 'Stack2': ['A', 'J', 'C', 'L', 'M'], 'Stack3': ['H', 'D', 'T', 'I', 'F'], 'Stack4': ['B', 'G', 'S', 'R', 'K'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1606.8346457481384 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 1), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 1), (4, 5), (4, 5), (4, 5), (4, 2), (4, 1), (2, 4), (2, 4), (2, 1), (4, 1), (4, 5), (5, 2), (5, 4), (2, 5), (2, 5), (2, 4), (3, 5), (3, 5), (3, 4), (1, 4), (5, 3), (5, 2), (5, 1), (5, 4), (3, 5), (3, 4), (2, 3), (2, 3), (4, 3), (5, 2), (5, 2), (5, 1), (5, 3), (2, 4), (2, 3), (5, 2), (1, 3), (1, 2), (5, 2), (3, 2), (4, 2)]
Nodes searched: 54
Path.Length: 53
运行 A* 搜索（使用 CNN+Transformer）：
搜索达到最大迭代次数 50000，未找到解决方案。
已搜索节点数：50000
Time consumption: 172.29346323013306 seconds
Solution found: None
Nodes searched: 50000

--- Sample 6 ---
stack_order=['Stack2', 'Stack3', 'Stack4', 'Stack1']
start_state={'Stack1': ['S', 'N', 'I', 'H', 'E'], 'Stack2': ['J', 'Q', 'C', 'P', 'L'], 'Stack3': ['G', 'R', 'K', 'D', 'A'], 'Stack4': ['T', 'M', 'B', 'O', 'F'], 'Stack5': []}
goal_state={'Stack1': ['O', 'L', 'I', 'E', 'M'], 'Stack2': ['H', 'Q', 'C', 'S', 'P'], 'Stack3': ['T', 'J', 'G', 'A', 'R'], 'Stack4': ['D', 'K', 'F', 'N', 'B'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2947.377541780472 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 3), (2, 3), (1, 5), (1, 2), (3, 5), (3, 2), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (5, 3), (5, 1), (5, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 5), (4, 5), (4, 5), (4, 1), (4, 3), (5, 4), (5, 1), (5, 4), (5, 1), (5, 4), (5, 1), (5, 4), (5, 1), (5, 3), (1, 5), (1, 5), (1, 3), (5, 4), (5, 3), (4, 5), (4, 5), (4, 3), (4, 2), (4, 2), (5, 4), (5, 4), (2, 5), (2, 4), (1, 3), (1, 2), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (3, 1), (5, 3), (5, 4), (5, 1), (3, 1), (4, 1), (2, 1)]
Nodes searched: 87
Path.Length: 73
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 64.83308482170105 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 2), (3, 5), (3, 5), (5, 1), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (5, 2), (1, 5), (1, 5), (1, 5), (4, 1), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (5, 1), (5, 1), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (4, 5), (4, 5), (4, 5), (4, 3), (1, 3), (5, 4), (5, 1), (5, 1), (5, 3), (1, 2), (5, 2), (1, 5), (1, 5), (1, 3), (5, 2), (4, 2), (5, 4), (1, 5), (4, 2), (5, 4), (1, 5), (4, 5), (1, 4), (1, 3), (5, 1), (5, 4), (1, 4), (1, 5), (1, 4), (2, 4), (5, 4), (2, 5), (5, 4), (2, 5), (2, 5), (2, 1), (5, 1), (4, 5), (4, 1), (5, 3), (5, 1), (3, 1)]
Nodes searched: 18503
Path.Length: 81

--- Sample 7 ---
stack_order=['Stack2', 'Stack4', 'Stack1', 'Stack3']
start_state={'Stack1': ['J', 'F', 'H', 'R', 'D'], 'Stack2': ['M', 'E', 'T', 'Q', 'O'], 'Stack3': ['I', 'S', 'B', 'K', 'N'], 'Stack4': ['A', 'C', 'P', 'G', 'L'], 'Stack5': []}
goal_state={'Stack1': ['Q', 'E', 'M', 'H', 'B'], 'Stack2': ['F', 'P', 'N', 'O', 'S'], 'Stack3': ['T', 'D', 'K', 'J', 'C'], 'Stack4': ['I', 'A', 'R', 'G', 'L'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2332.3204209804535 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 5), (1, 5), (1, 2), (4, 5), (4, 5), (4, 2), (3, 2), (5, 4), (5, 4), (5, 1), (5, 1), (5, 3), (5, 3), (5, 1), (5, 1), (5, 1), (5, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 2), (4, 5), (4, 5), (4, 3), (4, 5), (3, 5), (3, 4), (5, 3), (5, 4), (1, 2), (1, 5), (1, 2), (1, 4), (5, 1), (5, 4), (5, 4), (1, 5), (1, 5), (1, 3), (2, 3), (2, 1), (3, 1), (5, 2), (5, 4), (5, 2), (5, 3), (5, 4), (5, 1), (2, 5), (2, 1), (5, 1), (3, 5), (3, 5), (3, 5), (4, 1), (4, 3), (1, 3), (5, 4), (5, 1), (5, 3), (1, 3), (4, 3)]
Nodes searched: 74
Path.Length: 69
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 18.874289989471436 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (1, 5), (2, 5), (2, 5), (1, 5), (4, 5), (1, 5), (1, 2), (5, 1), (5, 1), (4, 5), (4, 2), (3, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (3, 5), (3, 5), (3, 2), (4, 5), (4, 5), (3, 4), (5, 4), (1, 2), (1, 3), (1, 3), (1, 4), (1, 4), (1, 4), (1, 3), (1, 5), (5, 2), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (3, 5), (3, 5), (3, 1), (5, 1), (5, 1), (2, 3), (2, 3), (2, 1), (2, 4), (2, 3), (3, 5), (3, 5), (2, 3), (5, 3), (5, 3), (4, 3)]
Nodes searched: 5819
Path.Length: 62

--- Sample 8 ---
stack_order=['Stack4', 'Stack1', 'Stack2', 'Stack3']
start_state={'Stack1': ['K', 'T', 'R', 'P', 'H'], 'Stack2': ['E', 'B', 'A', 'O', 'C'], 'Stack3': ['N', 'Q', 'S', 'L', 'D'], 'Stack4': ['M', 'I', 'F', 'J', 'G'], 'Stack5': []}
goal_state={'Stack1': ['S', 'O', 'P', 'Q', 'T'], 'Stack2': ['E', 'I', 'C', 'F', 'B'], 'Stack3': ['D', 'L', 'A', 'K', 'G'], 'Stack4': ['R', 'M', 'N', 'J', 'H'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2116.7900733947754 seconds
Solution found: [(4, 5), (4, 5), (4, 2), (4, 5), (4, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 2), (5, 4), (3, 5), (3, 5), (3, 5), (3, 1), (3, 4), (2, 3), (5, 1), (5, 1), (5, 3), (5, 2), (5, 4), (3, 5), (3, 4), (1, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 3), (4, 1), (2, 5), (2, 5), (2, 3), (2, 1), (5, 2), (5, 4), (5, 3), (5, 1), (5, 1), (3, 1), (2, 5), (2, 5), (2, 5), (4, 2), (3, 2), (5, 1), (5, 3), (5, 2), (1, 2), (3, 1), (3, 4), (3, 5), (5, 4), (5, 3), (4, 3), (1, 3), (4, 3), (5, 3)]
Nodes searched: 69
Path.Length: 59
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 0.3372197151184082 seconds
Solution found: [(4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (1, 5), (1, 5), (1, 4), (3, 5), (3, 5), (5, 1), (5, 1), (3, 5), (5, 1), (5, 1), (3, 5), (5, 1), (5, 1), (5, 4), (3, 4), (5, 3), (5, 1), (5, 4), (1, 5), (1, 4), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 1), (2, 1), (5, 1), (5, 1), (5, 2), (4, 5), (5, 2), (4, 5), (4, 1), (2, 5), (2, 5), (2, 5), (2, 5), (3, 5), (3, 2), (5, 2), (5, 3), (5, 1), (5, 2), (3, 2), (5, 3), (5, 3), (1, 3), (4, 3), (5, 3)]
Nodes searched: 95
Path.Length: 62

--- Sample 9 ---
stack_order=['Stack1', 'Stack2', 'Stack3', 'Stack4']
start_state={'Stack1': ['O', 'J', 'H', 'A', 'R'], 'Stack2': ['T', 'M', 'Q', 'N', 'C'], 'Stack3': ['B', 'G', 'L', 'D', 'S'], 'Stack4': ['F', 'P', 'I', 'K', 'E'], 'Stack5': []}
goal_state={'Stack1': ['R', 'N', 'M', 'Q', 'S'], 'Stack2': ['C', 'G', 'E', 'B', 'O'], 'Stack3': ['K', 'I', 'A', 'H', 'P'], 'Stack4': ['J', 'F', 'T', 'L', 'D'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1790.3026463985443 seconds
Solution found: [(1, 5), (1, 3), (1, 5), (1, 5), (1, 3), (5, 2), (5, 2), (5, 1), (2, 5), (2, 5), (2, 3), (2, 1), (2, 5), (2, 1), (5, 1), (3, 2), (3, 2), (3, 5), (3, 1), (2, 5), (2, 5), (2, 3), (5, 2), (3, 5), (3, 5), (3, 5), (3, 2), (4, 2), (3, 2), (4, 3), (5, 4), (5, 4), (5, 1), (5, 2), (4, 5), (4, 1), (4, 3), (5, 4), (5, 3), (5, 1), (5, 3), (4, 5), (4, 3), (4, 3), (1, 4), (3, 4), (1, 2), (1, 4), (2, 4), (5, 4)]
Nodes searched: 55
Path.Length: 50
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 0.5708959102630615 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 1), (2, 5), (2, 1), (2, 5), (2, 1), (5, 1), (3, 1), (2, 5), (5, 1), (5, 2), (3, 5), (3, 5), (3, 2), (5, 1), (5, 3), (1, 5), (1, 5), (3, 5), (4, 5), (4, 5), (5, 1), (4, 5), (5, 1), (5, 1), (4, 5), (4, 2), (3, 2), (5, 2), (4, 3), (4, 3), (1, 3), (1, 2), (1, 3), (4, 3), (4, 5), (2, 4), (5, 4), (5, 1), (5, 4), (5, 4), (1, 4)]
Nodes searched: 173
Path.Length: 50

--- Sample 10 ---
stack_order=['Stack1', 'Stack4', 'Stack3', 'Stack2']
start_state={'Stack1': ['Q', 'O', 'J', 'S', 'I'], 'Stack2': ['F', 'B', 'E', 'N', 'K'], 'Stack3': ['G', 'T', 'A', 'R', 'M'], 'Stack4': ['P', 'H', 'D', 'L', 'C'], 'Stack5': []}
goal_state={'Stack1': ['A', 'E', 'K', 'N', 'T'], 'Stack2': ['Q', 'D', 'J', 'L', 'M'], 'Stack3': ['B', 'H', 'P', 'G', 'S'], 'Stack4': ['O', 'C', 'I', 'F', 'R'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2002.1022763252258 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 4), (3, 1), (2, 5), (2, 5), (2, 1), (5, 4), (5, 1), (4, 1), (3, 1), (4, 5), (4, 5), (4, 1), (4, 5), (4, 2), (4, 5), (5, 3), (5, 2), (5, 3), (5, 1), (5, 2), (5, 3), (5, 4), (3, 5), (3, 4), (5, 1), (5, 3), (5, 3), (5, 4), (2, 5), (2, 5), (2, 5), (2, 5), (2, 4), (1, 2), (1, 4), (3, 4), (3, 5), (3, 2), (3, 2), (5, 2), (5, 3), (5, 3), (2, 5), (2, 5), (2, 3), (5, 3), (4, 3), (5, 1), (5, 2), (1, 2), (1, 2), (5, 2)]
Nodes searched: 63
Path.Length: 58
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 63.52468729019165 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 5), (4, 5), (4, 5), (4, 5), (4, 5), (3, 5), (3, 1), (3, 1), (1, 2), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 2), (5, 1), (2, 1), (2, 1), (4, 1), (5, 4), (4, 2), (5, 4), (3, 1), (5, 1), (5, 1), (3, 5), (3, 1), (3, 4), (5, 1), (5, 4), (1, 4), (3, 1), (3, 2), (1, 5), (1, 5), (3, 4), (4, 5), (3, 4), (3, 5), (1, 5), (1, 2), (1, 3), (1, 2), (5, 1), (5, 1), (5, 3), (2, 3), (1, 3), (1, 3), (5, 1), (5, 2), (1, 2)]
Nodes searched: 18158
Path.Length: 66

--- Sample 11 ---
stack_order=['Stack3', 'Stack1', 'Stack4', 'Stack2']
start_state={'Stack1': ['I', 'T', 'A', 'P', 'G'], 'Stack2': ['C', 'O', 'Q', 'N', 'M'], 'Stack3': ['R', 'K', 'F', 'J', 'H'], 'Stack4': ['B', 'L', 'D', 'S', 'E'], 'Stack5': []}
goal_state={'Stack1': ['L', 'Q', 'N', 'R', 'M'], 'Stack2': ['E', 'S', 'T', 'A', 'I'], 'Stack3': ['D', 'O', 'C', 'J', 'P'], 'Stack4': ['K', 'G', 'F', 'H', 'B'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2048.7954235076904 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 5), (4, 5), (4, 3), (2, 5), (2, 4), (2, 5), (2, 3), (2, 3), (4, 5), (5, 2), (5, 1), (5, 1), (5, 2), (5, 2), (5, 1), (5, 1), (5, 4), (5, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (1, 5), (4, 5), (4, 1), (5, 4), (5, 2), (5, 3), (5, 2), (5, 4), (5, 1), (2, 3), (2, 4), (2, 5), (2, 5), (2, 1), (4, 3), (5, 2), (5, 2), (5, 3), (5, 1), (3, 1), (4, 5), (4, 1), (4, 2), (5, 1), (5, 4), (1, 4), (1, 4), (5, 4), (2, 4), (2, 5), (2, 1), (5, 2), (1, 2), (3, 5), (3, 5), (3, 2), (5, 2), (5, 2)]
Nodes searched: 70
Path.Length: 69
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 16.21055817604065 seconds
Solution found: [(3, 5), (3, 5), (3, 1), (3, 5), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (4, 5), (5, 1), (4, 3), (3, 5), (4, 3), (2, 5), (5, 1), (2, 5), (5, 1), (2, 5), (2, 3), (2, 3), (5, 2), (1, 2), (1, 5), (1, 5), (1, 5), (1, 3), (4, 3), (3, 5), (5, 1), (5, 1), (5, 1), (5, 4), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (1, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 1), (2, 5), (2, 1), (5, 2), (2, 1), (5, 3), (5, 2), (4, 5), (4, 1), (4, 3), (5, 2), (4, 2), (3, 5), (5, 2), (3, 5), (5, 2), (4, 1), (4, 5), (1, 5), (4, 5), (4, 1), (2, 4), (4, 5), (4, 5), (2, 5), (2, 5), (2, 4), (5, 4), (5, 4), (5, 1), (5, 4), (1, 4), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 2), (1, 2)]
Nodes searched: 4958
Path.Length: 90

--- Sample 12 ---
stack_order=['Stack1', 'Stack2', 'Stack3', 'Stack4']
start_state={'Stack1': ['G', 'L', 'O', 'J', 'K'], 'Stack2': ['B', 'R', 'A', 'I', 'H'], 'Stack3': ['N', 'D', 'P', 'M', 'Q'], 'Stack4': ['C', 'S', 'E', 'T', 'F'], 'Stack5': []}
goal_state={'Stack1': ['E', 'O', 'K', 'Q', 'P'], 'Stack2': ['B', 'D', 'I', 'S', 'T'], 'Stack3': ['L', 'F', 'A', 'N', 'G'], 'Stack4': ['R', 'H', 'J', 'M', 'C'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2356.723390340805 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (4, 5), (4, 5), (4, 2), (4, 1), (5, 3), (5, 4), (5, 3), (5, 1), (5, 4), (5, 1), (2, 1), (1, 5), (3, 5), (3, 5), (3, 1), (3, 5), (3, 1), (2, 5), (2, 1), (2, 5), (2, 5), (3, 2), (1, 2), (4, 5), (4, 5), (4, 2), (5, 1), (5, 3), (5, 3), (5, 3), (5, 4), (5, 3), (5, 3), (5, 3), (5, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (3, 4), (3, 5), (1, 2), (1, 3), (5, 1), (5, 4), (5, 1), (5, 3), (4, 3), (1, 5), (1, 3), (5, 1), (5, 3), (4, 5), (4, 3), (4, 5), (2, 4), (3, 4), (5, 1), (5, 4), (1, 5), (1, 4), (5, 4)]
Nodes searched: 76
Path.Length: 68
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 2.1575124263763428 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (4, 5), (4, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 1), (5, 3), (5, 1), (3, 2), (3, 4), (3, 5), (4, 5), (3, 5), (3, 4), (3, 1), (5, 2), (5, 2), (3, 5), (3, 1), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (3, 2), (5, 1), (5, 1), (5, 2), (4, 5), (4, 2), (5, 2), (3, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 3), (5, 1), (4, 2), (1, 2), (5, 4), (1, 5), (1, 5), (1, 5), (1, 3), (5, 3), (4, 3), (1, 4), (5, 4), (5, 4), (2, 4), (2, 4)]
Nodes searched: 683
Path.Length: 62

--- Sample 13 ---
stack_order=['Stack3', 'Stack2', 'Stack4', 'Stack1']
start_state={'Stack1': ['G', 'T', 'J', 'D', 'H'], 'Stack2': ['S', 'M', 'P', 'L', 'K'], 'Stack3': ['R', 'B', 'E', 'N', 'I'], 'Stack4': ['C', 'A', 'O', 'F', 'Q'], 'Stack5': []}
goal_state={'Stack1': ['D', 'T', 'N', 'M', 'F'], 'Stack2': ['I', 'J', 'E', 'O', 'B'], 'Stack3': ['L', 'S', 'K', 'A', 'R'], 'Stack4': ['G', 'H', 'C', 'Q', 'P'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2553.4194824695587 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (2, 5), (2, 3), (2, 4), (2, 1), (2, 3), (5, 3), (4, 2), (4, 5), (4, 5), (4, 5), (4, 3), (5, 2), (5, 1), (5, 4), (5, 3), (2, 5), (2, 4), (5, 3), (5, 1), (5, 4), (5, 1), (5, 2), (1, 5), (4, 1), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (1, 2), (5, 3), (5, 1), (5, 1), (5, 3), (5, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 3), (5, 1), (5, 2), (4, 5), (4, 5), (4, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 4), (2, 4), (5, 4), (5, 4), (1, 5), (1, 5), (1, 5), (3, 1), (5, 1), (5, 2), (5, 3), (5, 1), (2, 1), (3, 1)]
Nodes searched: 74
Path.Length: 73
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 13.936685562133789 seconds
Solution found: [(2, 3), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (2, 3), (2, 5), (2, 5), (2, 3), (5, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (4, 5), (5, 1), (4, 5), (5, 1), (2, 4), (5, 1), (4, 5), (1, 2), (1, 5), (1, 2), (1, 5), (1, 5), (4, 5), (4, 3), (1, 5), (1, 3), (2, 5), (1, 5), (1, 2), (1, 3), (2, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 2), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 5), (3, 1), (5, 1), (5, 1), (5, 3), (5, 1), (3, 1)]
Nodes searched: 3984
Path.Length: 70

--- Sample 14 ---
stack_order=['Stack2', 'Stack1', 'Stack3', 'Stack4']
start_state={'Stack1': ['R', 'A', 'T', 'B', 'C'], 'Stack2': ['M', 'O', 'G', 'H', 'J'], 'Stack3': ['Q', 'I', 'S', 'L', 'E'], 'Stack4': ['N', 'K', 'D', 'P', 'F'], 'Stack5': []}
goal_state={'Stack1': ['R', 'I', 'M', 'F', 'C'], 'Stack2': ['B', 'P', 'E', 'Q', 'L'], 'Stack3': ['A', 'K', 'T', 'S', 'J'], 'Stack4': ['H', 'O', 'G', 'D', 'N'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1388.0170996189117 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 2), (4, 5), (4, 2), (3, 2), (3, 5), (3, 5), (3, 1), (3, 2), (5, 3), (5, 2), (1, 3), (1, 5), (1, 5), (3, 1), (5, 3), (5, 2), (5, 4), (5, 3), (5, 1), (4, 1), (3, 1), (3, 5), (3, 4), (5, 3), (4, 5), (4, 5), (4, 3), (2, 3), (5, 1), (5, 3), (5, 2), (5, 1), (5, 1), (5, 3), (4, 5), (1, 4), (2, 4), (1, 4), (1, 4), (5, 4)]
Nodes searched: 47
Path.Length: 46
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 1.290156364440918 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (1, 5), (1, 2), (5, 1), (4, 5), (4, 2), (3, 2), (3, 5), (3, 5), (3, 5), (3, 2), (5, 3), (5, 1), (5, 2), (1, 5), (1, 5), (1, 5), (1, 5), (3, 1), (5, 3), (5, 2), (5, 2), (5, 2), (5, 2), (5, 1), (2, 1), (2, 3), (2, 1), (3, 5), (5, 1), (5, 1), (5, 1), (4, 1), (4, 3), (2, 3), (1, 2), (4, 2), (5, 4), (5, 2), (1, 5), (1, 5), (1, 3), (2, 3), (5, 4), (5, 4), (2, 5), (2, 4), (5, 4)]
Nodes searched: 396
Path.Length: 52

--- Sample 15 ---
stack_order=['Stack1', 'Stack3', 'Stack4', 'Stack2']
start_state={'Stack1': ['J', 'B', 'I', 'K', 'H'], 'Stack2': ['O', 'A', 'C', 'G', 'M'], 'Stack3': ['P', 'T', 'F', 'Q', 'D'], 'Stack4': ['N', 'S', 'R', 'L', 'E'], 'Stack5': []}
goal_state={'Stack1': ['O', 'A', 'I', 'M', 'C'], 'Stack2': ['R', 'H', 'E', 'L', 'K'], 'Stack3': ['J', 'T', 'N', 'B', 'D'], 'Stack4': ['P', 'S', 'G', 'F', 'Q'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2780.7987747192383 seconds
Solution found: [(1, 5), (4, 5), (1, 2), (1, 5), (1, 5), (1, 5), (2, 3), (2, 5), (2, 5), (2, 5), (2, 5), (2, 1), (5, 1), (5, 2), (5, 2), (5, 3), (5, 2), (5, 3), (5, 1), (3, 5), (3, 1), (2, 5), (2, 5), (2, 1), (3, 2), (3, 5), (3, 4), (3, 1), (3, 5), (3, 1), (5, 4), (5, 2), (5, 1), (5, 3), (4, 3), (4, 5), (4, 5), (4, 1), (4, 1), (4, 3), (5, 4), (5, 4), (5, 3), (2, 3), (4, 3), (4, 5), (1, 2), (1, 3), (1, 5), (1, 4), (2, 4), (5, 4), (1, 4), (3, 5), (3, 4), (2, 3), (5, 2), (5, 1), (5, 1), (5, 2), (1, 2), (1, 2), (3, 2)]
Nodes searched: 93
Path.Length: 63
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 13.913392066955566 seconds
Solution found: [(1, 5), (4, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (2, 5), (2, 5), (2, 5), (2, 1), (5, 1), (5, 2), (5, 2), (5, 2), (5, 3), (5, 2), (5, 1), (2, 1), (1, 5), (2, 1), (2, 5), (2, 1), (3, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (2, 3), (5, 2), (5, 3), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 4), (1, 3), (4, 5), (4, 5), (2, 4), (5, 4), (5, 4), (1, 2), (1, 4), (2, 4), (1, 2), (2, 5), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 2), (5, 2)]
Nodes searched: 3912
Path.Length: 65

--- Sample 16 ---
stack_order=['Stack1', 'Stack4', 'Stack3', 'Stack2']
start_state={'Stack1': ['I', 'H', 'F', 'G', 'J'], 'Stack2': ['M', 'K', 'L', 'N', 'A'], 'Stack3': ['O', 'S', 'T', 'E', 'P'], 'Stack4': ['B', 'R', 'C', 'Q', 'D'], 'Stack5': []}
goal_state={'Stack1': ['O', 'J', 'B', 'H', 'L'], 'Stack2': ['R', 'C', 'S', 'D', 'I'], 'Stack3': ['E', 'Q', 'A', 'M', 'T'], 'Stack4': ['K', 'N', 'F', 'P', 'G'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2367.1901285648346 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 2), (5, 3), (5, 4), (5, 4), (5, 1), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (4, 5), (4, 5), (4, 1), (1, 5), (4, 5), (4, 1), (3, 1), (2, 4), (2, 1), (4, 1), (2, 4), (1, 4), (5, 1), (5, 2), (5, 3), (5, 1), (5, 2), (5, 2), (5, 4), (3, 5), (3, 4), (5, 3), (5, 4), (3, 5), (3, 5), (3, 4), (3, 1), (5, 3), (5, 3), (1, 5), (1, 3), (2, 5), (2, 5), (2, 5), (2, 3), (4, 3), (1, 2), (5, 2), (5, 3), (5, 1), (5, 2), (1, 2), (3, 2)]
Nodes searched: 80
Path.Length: 67
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 41.73511362075806 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (3, 5), (3, 5), (3, 5), (3, 5), (3, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 4), (5, 3), (5, 3), (5, 3), (5, 1), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (4, 1), (5, 4), (5, 4), (5, 2), (5, 2), (3, 5), (5, 2), (3, 5), (3, 1), (3, 5), (2, 5), (3, 5), (3, 5), (3, 4), (2, 4), (5, 3), (2, 5), (2, 5), (2, 3), (2, 1), (4, 5), (4, 5), (4, 5), (4, 5), (2, 4), (3, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (3, 5), (1, 3), (1, 3), (1, 3), (2, 3), (5, 3), (1, 2), (1, 5), (2, 5), (1, 2), (2, 3), (1, 2), (3, 2), (5, 1), (5, 2), (1, 2), (5, 2)]
Nodes searched: 12070
Path.Length: 79

--- Sample 17 ---
stack_order=['Stack3', 'Stack4', 'Stack1', 'Stack2']
start_state={'Stack1': ['E', 'T', 'I', 'Q', 'F'], 'Stack2': ['L', 'B', 'R', 'K', 'D'], 'Stack3': ['H', 'M', 'O', 'S', 'N'], 'Stack4': ['A', 'G', 'C', 'J', 'P'], 'Stack5': []}
goal_state={'Stack1': ['H', 'G', 'N', 'E', 'B'], 'Stack2': ['M', 'Q', 'D', 'L', 'I'], 'Stack3': ['C', 'J', 'R', 'F', 'T'], 'Stack4': ['S', 'P', 'O', 'K', 'A'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1826.782220363617 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 1), (4, 1), (4, 3), (1, 3), (2, 5), (2, 5), (2, 3), (1, 5), (1, 3), (1, 5), (1, 5), (1, 3), (4, 1), (4, 5), (5, 1), (5, 2), (5, 2), (5, 3), (5, 2), (5, 2), (5, 1), (5, 3), (5, 1), (5, 4), (3, 5), (3, 4), (1, 4), (2, 5), (2, 4), (1, 5), (1, 4), (1, 3), (1, 4), (5, 1), (3, 1), (5, 3), (5, 2), (5, 1), (4, 1), (2, 5), (2, 5), (2, 5), (2, 1), (2, 4), (3, 1), (5, 3), (5, 1), (5, 2), (1, 2), (1, 2), (4, 2), (3, 2)]
Nodes searched: 58
Path.Length: 57
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 9.268842697143555 seconds
Solution found: [(3, 5), (3, 5), (4, 5), (3, 5), (3, 5), (3, 5), (4, 5), (4, 3), (5, 3), (2, 5), (5, 1), (5, 1), (5, 1), (2, 5), (2, 3), (1, 5), (1, 5), (1, 5), (1, 3), (1, 5), (1, 5), (1, 3), (2, 1), (4, 5), (4, 5), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (1, 4), (1, 4), (1, 4), (1, 5), (2, 3), (5, 2), (1, 3), (1, 5), (1, 2), (5, 2), (1, 5), (1, 3), (1, 4), (1, 5), (1, 5), (3, 5), (3, 1), (5, 1), (5, 3), (5, 3), (5, 3), (5, 1), (3, 5), (3, 5), (3, 1), (5, 1), (3, 2), (5, 2)]
Nodes searched: 2615
Path.Length: 64

--- Sample 18 ---
stack_order=['Stack1', 'Stack4', 'Stack3', 'Stack2']
start_state={'Stack1': ['K', 'E', 'Q', 'N', 'D'], 'Stack2': ['B', 'P', 'A', 'L', 'T'], 'Stack3': ['J', 'C', 'O', 'I', 'F'], 'Stack4': ['R', 'M', 'G', 'H', 'S'], 'Stack5': []}
goal_state={'Stack1': ['A', 'K', 'O', 'T', 'E'], 'Stack2': ['D', 'P', 'S', 'M', 'I'], 'Stack3': ['Q', 'B', 'N', 'C', 'G'], 'Stack4': ['F', 'H', 'R', 'J', 'L'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1861.7645437717438 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 3), (2, 3), (2, 4), (2, 1), (3, 2), (3, 1), (3, 2), (3, 2), (3, 1), (2, 5), (2, 5), (2, 1), (5, 2), (5, 2), (5, 1), (4, 5), (4, 1), (4, 5), (4, 3), (4, 5), (4, 5), (2, 5), (2, 4), (5, 2), (5, 3), (5, 2), (5, 4), (3, 4), (3, 5), (3, 5), (3, 4), (5, 3), (5, 2), (5, 4), (3, 4), (5, 3), (2, 5), (2, 5), (2, 5), (2, 4), (2, 3), (5, 2), (5, 2), (5, 4), (5, 3), (4, 5), (4, 1), (4, 3), (5, 3), (2, 3), (2, 1), (5, 2), (1, 5), (1, 2), (1, 2), (3, 2), (5, 2)]
Nodes searched: 62
Path.Length: 61
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 4.732829809188843 seconds
Solution found: [(1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (4, 5), (3, 5), (3, 5), (3, 5), (4, 5), (4, 5), (3, 5), (3, 5), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (2, 4), (2, 4), (2, 3), (3, 1), (5, 3), (5, 3), (5, 3), (5, 1), (5, 4), (5, 2), (5, 2), (3, 5), (5, 2), (3, 5), (5, 2), (4, 5), (5, 2), (4, 5), (4, 5), (4, 1), (5, 1), (2, 1), (4, 2), (4, 5), (4, 5), (4, 5), (4, 5), (4, 5), (2, 5), (2, 4), (5, 4), (5, 4), (5, 1), (5, 4), (5, 1), (5, 1), (5, 4), (2, 1), (2, 3), (3, 5), (3, 5), (2, 3), (2, 5), (2, 3), (5, 2), (5, 1), (5, 3), (1, 5), (1, 2), (1, 5), (1, 3), (5, 3), (2, 5), (2, 5), (5, 1), (5, 1), (5, 1), (5, 2), (1, 5), (1, 5), (1, 2), (5, 2), (1, 2), (5, 2)]
Nodes searched: 1469
Path.Length: 82

--- Sample 19 ---
stack_order=['Stack3', 'Stack1', 'Stack4', 'Stack2']
start_state={'Stack1': ['R', 'A', 'C', 'F', 'M'], 'Stack2': ['B', 'L', 'J', 'K', 'S'], 'Stack3': ['P', 'Q', 'H', 'G', 'E'], 'Stack4': ['D', 'I', 'O', 'N', 'T'], 'Stack5': []}
goal_state={'Stack1': ['B', 'R', 'E', 'S', 'D'], 'Stack2': ['A', 'C', 'L', 'H', 'M'], 'Stack3': ['P', 'G', 'O', 'Q', 'J'], 'Stack4': ['K', 'N', 'I', 'T', 'F'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 3454.8638038635254 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 2), (5, 3), (4, 5), (4, 5), (4, 3), (1, 3), (4, 5), (2, 5), (2, 1), (2, 5), (2, 3), (1, 5), (4, 5), (1, 4), (1, 5), (1, 4), (1, 5), (1, 5), (2, 5), (2, 1), (5, 2), (5, 1), (5, 2), (5, 3), (4, 2), (5, 2), (5, 4), (5, 2), (5, 2), (5, 3), (5, 3), (5, 3), (5, 1), (4, 1), (2, 5), (2, 4), (2, 1), (4, 5), (4, 2), (5, 4), (3, 5), (3, 4), (3, 4), (5, 4), (3, 4), (2, 3), (2, 4), (2, 1), (2, 5), (1, 2), (4, 2), (5, 2), (5, 2), (3, 2)]
Nodes searched: 99
Path.Length: 59
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 9.000902891159058 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (5, 1), (5, 1), (5, 3), (1, 5), (1, 2), (1, 5), (4, 1), (1, 5), (1, 5), (4, 1), (4, 3), (2, 3), (4, 1), (2, 3), (3, 5), (4, 5), (2, 4), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (2, 1), (5, 1), (1, 2), (5, 1), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (2, 3), (5, 2), (5, 3), (5, 3), (5, 3), (5, 1), (3, 5), (3, 5), (3, 5), (5, 2), (3, 5), (3, 1), (3, 1), (3, 5), (3, 4), (5, 4), (2, 4), (2, 4), (5, 1), (5, 1), (5, 1), (3, 5), (3, 2), (5, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 2)]
Nodes searched: 2526
Path.Length: 68

--- Sample 20 ---
stack_order=['Stack2', 'Stack4', 'Stack1', 'Stack3']
start_state={'Stack1': ['J', 'K', 'E', 'R', 'L'], 'Stack2': ['O', 'G', 'N', 'P', 'A'], 'Stack3': ['B', 'M', 'S', 'H', 'F'], 'Stack4': ['D', 'I', 'C', 'T', 'Q'], 'Stack5': []}
goal_state={'Stack1': ['D', 'L', 'G', 'H', 'M'], 'Stack2': ['C', 'O', 'F', 'I', 'Q'], 'Stack3': ['R', 'S', 'N', 'T', 'K'], 'Stack4': ['E', 'P', 'A', 'J', 'B'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 1864.5486946105957 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (4, 5), (4, 5), (4, 2), (5, 1), (5, 1), (5, 2), (3, 2), (4, 2), (1, 2), (4, 5), (1, 5), (1, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 2), (5, 1), (5, 2), (5, 3), (5, 4), (5, 4), (1, 5), (1, 5), (1, 5), (1, 3), (1, 4), (3, 1), (3, 5), (3, 5), (3, 5), (3, 5), (3, 4), (1, 3), (5, 4), (5, 3), (5, 3), (5, 3), (5, 2), (5, 2), (5, 1), (2, 1), (2, 5), (2, 1), (3, 5), (3, 1), (4, 1), (3, 1), (3, 5), (5, 4), (5, 1), (5, 3), (1, 5), (1, 3), (5, 3), (2, 3), (4, 3)]
Nodes searched: 63
Path.Length: 62
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 24.740440130233765 seconds
Solution found: [(2, 5), (2, 5), (2, 5), (2, 5), (2, 5), (4, 5), (4, 5), (4, 2), (4, 3), (5, 1), (5, 1), (5, 2), (3, 5), (3, 2), (5, 2), (1, 2), (1, 5), (1, 5), (4, 5), (1, 5), (1, 4), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 4), (5, 4), (1, 2), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 5), (1, 4), (2, 1), (3, 5), (3, 5), (3, 5), (3, 4), (1, 3), (5, 4), (5, 4), (5, 4), (5, 4), (5, 4), (5, 1), (5, 1), (5, 3), (5, 1), (3, 5), (4, 5), (5, 2), (4, 5), (4, 1), (4, 2), (4, 1), (3, 5), (2, 5), (2, 3), (5, 3), (5, 3), (5, 1), (5, 3), (1, 3)]
Nodes searched: 7612
Path.Length: 67

--- Sample 21 ---
stack_order=['Stack3', 'Stack4', 'Stack2', 'Stack1']
start_state={'Stack1': ['C', 'J', 'P', 'E', 'R'], 'Stack2': ['D', 'I', 'F', 'Q', 'N'], 'Stack3': ['G', 'B', 'O', 'H', 'S'], 'Stack4': ['K', 'L', 'M', 'T', 'A'], 'Stack5': []}
goal_state={'Stack1': ['T', 'G', 'C', 'K', 'H'], 'Stack2': ['P', 'F', 'J', 'O', 'R'], 'Stack3': ['L', 'S', 'Q', 'E', 'I'], 'Stack4': ['B', 'D', 'A', 'N', 'M'], 'Stack5': []}
运行 A* 搜索（使用 LLM qwen/qwq-32b:free）：
Time consumption: 2111.865910768509 seconds
Solution found: [(3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (4, 5), (4, 5), (4, 5), (4, 3), (5, 4), (5, 4), (5, 1), (5, 1), (5, 1), (5, 2), (5, 1), (5, 3), (2, 5), (2, 5), (2, 3), (1, 5), (1, 5), (1, 5), (1, 5), (1, 2), (1, 3), (2, 4), (2, 5), (2, 3), (4, 5), (4, 5), (4, 5), (4, 5), (5, 3), (5, 2), (5, 1), (5, 2), (5, 3), (5, 3), (5, 1), (5, 4), (2, 1), (2, 5), (2, 4), (3, 4), (5, 2), (5, 1), (5, 4), (2, 4), (1, 4), (1, 5), (1, 5), (1, 5), (1, 2), (3, 2), (1, 2), (5, 1), (5, 1), (5, 1), (5, 2), (1, 2), (1, 5), (1, 2), (1, 5), (2, 1), (5, 2), (5, 1), (2, 1), (3, 1), (4, 1)]
Nodes searched: 71
Path.Length: 70
运行 A* 搜索（使用 CNN+Transformer）：
Time consumption: 92.930593252182 seconds
Solution found: [(4, 2), (3, 5), (3, 5), (3, 5), (3, 5), (3, 5), (1, 5), (1, 5), (1, 5), (1, 5), (2, 5), (1, 5), (4, 1), (4, 2), (4, 3), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 1), (5, 3), (1, 4), (1, 5), (1, 5), (1, 5), (1, 4), (1, 5), (1, 5), (2, 5), (2, 5), (2, 3), (5, 1), (5, 1), (5, 1), (5, 3), (1, 5), (2, 5), (2, 3), (2, 1), (4, 2), (4, 5), (4, 5), (5, 3), (5, 3), (5, 3), (5, 3), (5, 3), (1, 5), (1, 5), (1, 5), (5, 3), (5, 3), (5, 3), (5, 4), (3, 4), (2, 5), (1, 2), (1, 4), (3, 5), (3, 4), (5, 4), (2, 5), (5, 1), (5, 1), (5, 1), (3, 5), (3, 2), (3, 2), (1, 5), (1, 5), (1, 2), (5, 1), (5, 2), (1, 2), (1, 5), (5, 3), (5, 1), (3, 1), (3, 5), (3, 1), (5, 1)]
Nodes searched: 29536
Path.Length: 86



