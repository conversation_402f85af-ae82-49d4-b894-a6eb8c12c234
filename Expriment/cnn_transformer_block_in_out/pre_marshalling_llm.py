import json
import re
import google.generativeai as genai
import os
import re
import ast
from openai import OpenAI

# API_KEYS = [ #GOOGLE
#     'AIzaSyA5h5n9qsVS_u0wRGZ4ZYJOKfhQXA5Jiww',
#     "AIzaSyDy6cHyjOuEkAct6CfvTlzKqx71VeiYWJM",
#     'AIzaSyBxlwUd5foUdzJXGQkiO_3I-GeMgcZR7og',
#     'AIzaSyAzqetrg2YRGq6LanqSrJxsrE7wgiQJRbk',
#     'AIzaSyCZmhC9FpryncZg53gsiXhwWmf76ZUvSow',
#     'AIzaSyCKAMDtFZTZxcby8gdR4a2a1mtzeqo0LCA',
#     'AIzaSyDjaJ9UBgtQrRu6yXjcmp1cRu0XdHU5PR0',
#     'AIzaSyAnlIz8W8ZOjgT4vh6mrFmYWciSGr-7Xtw',
#     'AIzaSyBfETd-9YQdOBVbVZt7FXCxt0coMy_dgOE',
#     'AIzaSyCq5FLHqzteZCrU18flsEGkk9OaMSdaTUs',
#     'AIzaSyBm3ceZ0kTRmLc3tqmA_cv9Sf6akNqTzDs',
#     'AIzaSyB7kVJSdFRl8jiDHmuxdPymKskjcEVWwcw',
#     'AIzaSyAvzADSqT3gWXh9HIgNq9XTttvWw2-jnTc',
#     'AIzaSyB0gCvlQEi0eTkiPAqqFUjHC27xw6YEpDk',
# ]

API_KEYS = [ #OPEN_ROUNTER
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    # '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
    '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',
]

# os.environ['http_proxy'] = "http://10.100.102.103:10811"
# os.environ['https_proxy'] = "http://10.100.102.103:10811"

BASE_URL = "https://openrouter.ai/api/v1"
# BASE_URL = "http://10.100.102.208:11434/v1" # ollama的部署地址和端口（4090服务器）
# BASE_URL = "http://10.100.102.208:8076/v1" # vllm的部署地址和端口（4090服务器）

# MODEL_NAME = "deepseek/deepseek-chat-v3-0324:free"
MODEL_NAME = "qwen/qwq-32b:free"

# MODEL_NAME = "qwen/qwen3-235b-a22b:free"
# MODEL_NAME = "google/gemini-2.0-pro-exp-02-05:free"
# MODEL_NAME = "google/gemini-2.0-flash-thinking-exp:free"
# MODEL_NAME = "google/gemini-2.5-pro-exp-03-25:free"

# MODEL_NAME = "qwq:32b-q4_K_M"  #ollama中部署的模型名称
# MODEL_NAME = "deepseek-r1:32b"  #ollama中部署的模型名称

# MODEL_NAME = "/data/base_models/Qwen/QwQ-32B-AWQ" # vllm

# MODEL_NAME = "models/gemini-2.5-pro-exp-03-25" #google

USEFUL_API_KEY_INDEX = 0

def chat_with_ai(input_text, api_keys=API_KEYS):
    global USEFUL_API_KEY_INDEX
    for api_key_index in range(len(api_keys)):
        index = (api_key_index + USEFUL_API_KEY_INDEX) % len(api_keys)
        current_api_key = api_keys[index]
        try:
            if not current_api_key.startswith('AIzaSy') or BASE_URL.startswith('http://'): # local ollama or vllm
                client = OpenAI(
                    base_url=BASE_URL,
                    api_key=current_api_key,
                    timeout=300.0,
                )

                completion = client.chat.completions.create(
                    model=MODEL_NAME,
                    messages=[
                        {
                            "role": "user",
                            "content": input_text
                        }
                    ]
                )

                response = completion.choices[0].message.content
            else:
                genai.configure(api_key=current_api_key)
                model = genai.GenerativeModel(MODEL_NAME)
                prompt_parts = [input_text]
                response = model.generate_content(prompt_parts).text

            USEFUL_API_KEY_INDEX = index
            return response

        except Exception as e:
            print(f"API Key {api_key_index + 1} 尝试失败: {e}")
            print(f"尝试下一个 API Key...")
            continue # 当前 API Key 失败，尝试下一个

    # 所有 API Key 失败，抛出异常
    raise Exception("所有 API Key 尝试失败，请检查 API Key 或网络连接。")

def get_priority_task(stack, current_blocks, goal_blocks):
    """动态生成当前修复栈的优先任务"""
    # 检查目标状态是否为空
    if not goal_blocks:
        return f"{stack}目标为空，无需进一步操作"

    # 情况 1：栈为空，优先移入目标栈底块
    if not current_blocks:
        return f"将{goal_blocks[0]}移入{stack}"

    # 情况 2：栈底错误，优先移走顶部错误块
    if current_blocks[0] != goal_blocks[0]:
        return f"移走{stack}顶部错误块{current_blocks[-1]}"

    # 情况 3：栈底正确，检查顶部是否匹配
    for i in range(min(len(current_blocks), len(goal_blocks))):
        if current_blocks[i] != goal_blocks[i]:
            return f"移走{stack}顶部错误块{current_blocks[-1]}"

    # 情况 4：当前栈块全部正确，移入下一块（如果有）
    if len(current_blocks) < len(goal_blocks):
        return f"移入目标状态的下一块{goal_blocks[len(current_blocks)]}"

    # 情况 5：栈已完全匹配目标状态
    return f"{stack}已匹配目标状态，无需操作"


class LLMGuidance:
    def __init__(self):
        self.model = MODEL_NAME

    def evaluate_actions(self, current_state, goal_state, planner, successors):
        current_fix_stack = current_state.current_fix_stack
        if current_fix_stack is None:
            return {"best_action": "uncertain", "best_reason": "all stacks fixed",
                    "worst_action": "uncertain", "worst_reason": "all stacks fixed"}, [], current_fix_stack

        # # 生成栈描述函数
        # def get_stack_order(stacks):
        #     stacks_desc = []
        #     for stack_name, blocks in stacks.items():
        #         if blocks:
        #             stack = blocks[::-1] + [stack_name]
        #             stacks_desc.append("-".join(stack))
        #         else:
        #             stacks_desc.append(f"{stack_name} (empty)")
        #     return "None" if not stacks_desc else "; ".join(stacks_desc)
        #
        # def generate_state_description(state):
        #     stacks = state.stacks
        #     stack_order = get_stack_order(stacks)
        #     return f"- Stack order (top to bottom): {stack_order}"
        def get_stack_order(stacks):
            stacks_desc = []
            for stack_name, blocks in stacks.items():
                if blocks:
                    stack_desc = f"{stack_name}: [{', '.join(blocks)}]（底→顶）"
                    stacks_desc.append(stack_desc)
                else:
                    stacks_desc.append(f"{stack_name}: []（空栈）")
            return "; ".join(stacks_desc)

        def get_stack_issue(stack, current_blocks, goal_blocks):
            """动态生成当前修复栈的问题描述"""
            if not goal_blocks:
                return f"{stack}目标为空，无需修复"
            if not current_blocks:
                return f"{stack}为空，需移入目标栈底块{goal_blocks[0]}"
            if current_blocks[0] != goal_blocks[0]:
                return f"{stack}栈底应为{goal_blocks[0]}，当前为{current_blocks[0]}"
            for i in range(1, min(len(current_blocks), len(goal_blocks))):
                if current_blocks[i] != goal_blocks[i]:
                    return f"{stack}栈底正确，顶部块{current_blocks[i:]}需调整为{goal_blocks[i:]}"
            if len(current_blocks) < len(goal_blocks):
                return f"{stack}栈底正确，需添加{goal_blocks[len(current_blocks):]}"
            if len(current_blocks) > len(goal_blocks):
                return f"{stack}栈底正确，需移除多余块{current_blocks[len(goal_blocks):]}"
            return f"{stack}已匹配目标状态"





        def generate_state_description(state):
            stacks = state.stacks
            stack_order = get_stack_order(stacks)
            return f"- {stack_order}"

        current_desc = generate_state_description(current_state)
        goal_desc = generate_state_description(goal_state)

        fix_stack_curr = current_state.stacks[current_fix_stack]
        fix_stack_goal = goal_state.stacks.get(current_fix_stack, [])

        # 生成当前修复栈和目标修复栈的描述
        current_fix_desc = get_stack_order({current_fix_stack: fix_stack_curr})
        goal_fix_desc = get_stack_order({current_fix_stack: fix_stack_goal})

        # 生成动作描述
        stack_names = list(current_state.stacks.keys())
        actions_desc = []
        actions = []
        for i, (next_state, action) in enumerate(successors):
            result_desc = generate_state_description(next_state)
            i_stack, j_stack = action
            source_stack = stack_names[i_stack - 1]
            target_stack = stack_names[j_stack - 1]
            i_block = current_state.stacks[source_stack][-1]
            if current_state.stacks[target_stack]:
                j_block = current_state.stacks[target_stack][-1]
                desc = (f"Action {i + 1}: [{i_stack},{j_stack}] \n"
                        f"- 将{i_stack}的栈顶块{i_block}移动到{j_stack}的栈顶\n"
                        f"Result:\n{result_desc}")
            else:
                desc = (f"Action {i + 1}: [{i_stack},{j_stack}] \n"
                        f"- 将{i_stack}的栈顶块{i_block}移动到{j_stack}的栈顶\n"
                        f"Result:\n{result_desc}")
            actions.append(action)
            actions_desc.append(desc)
        actions_text = "\n".join(actions_desc)

        # 提示词
        # prompt = (
        #     "你是一个Blocks World问题的专家。你的任务是评估一组可行动作，选出最优和最劣动作，帮助机器人手臂从当前状态接近目标状态。\n"
        #     "\n"
        #     "**当前修复栈**：\n"
        #     f"- **{current_fix_stack}**：当前状态为{current_fix_desc}，目标状态为{goal_fix_desc}。\n"
        #     "\n"
        #     "**问题背景**：\n"
        #     "Blocks World是一个积木堆叠问题，包含多个独立的垂直栈。每个栈由列表表示，**列表的第一个元素是栈底，最后一个元素是栈顶**。例如，[A, B, C]表示A在栈底，C在栈顶。机器人手臂一次只能执行一个动作：拿起某个栈顶的块并放到另一个栈顶。\n"
        #     "- 在构建目标状态时，**必须从栈底开始，逐层向上构建**。例如，对于目标状态[C, D, E]（底→顶），必须先放置C，再D，最后E。\n"
        #     "\n"
        #     "**核心规则**：\n"
        #     "1. 所有积木块都在栈内，每个栈是一个独立的垂直栈。\n"
        #     "2. 手臂只能拿起栈顶的块（即列表的最后一个元素），并将其放到另一个栈顶（即在另一个栈的列表末尾添加该块）。\n"
        #     "3. 栈具有先入后出（LIFO）特性：要访问栈中的某个块，必须先移走其上的所有块。\n"
        #     "4. 动作表示为 [i, j]，即将第 i 个栈的栈顶块移动到第 j 个栈的栈顶。\n"
        #     "\n"
        #     "**任务**：\n"
        #     "根据当前状态、目标状态和可行动作列表，选出：\n"
        #     f"- **最优动作**：优先选择能直接减少当前修复栈（{current_fix_stack}）与目标状态差异的动作，特别是：\n"
        #     "  - 将目标栈底的块移动到当前修复栈（如果栈底不正确）。\n"
        #     "  - 移走阻碍目标块放置的顶部块。\n"
        #     "  - 将目标块移动到正确位置（在栈底正确的前提下）。\n"
        #     "- **最劣动作**：避免选择增加当前修复栈与目标状态差异或无助于减少差异的动作。\n"
        #     "- 如果当前所有动作对全局进展影响相近，返回‘不确定’。\n"
        #     "\n"
        #     "**当前状态**：\n"
        #     f"{current_desc}\n"
        #     "\n"
        #     "**目标状态**：\n"
        #     f"{goal_desc}\n"
        #     "\n"
        #     "**可行动作及其后果**：\n"
        #     f"{actions_text}\n"
        #     "（在每个动作后，简要说明对当前修复栈的影响）\n"
        #     "\n"
        #     "**要求**：\n"
        #     "1. **动作质量**：\n"
        #     "   - **进展性**：动作应帮助当前修复栈的块内容和顺序完全匹配目标状态。\n"
        #     "   - **简洁性**：避免显著增加不必要移动。\n"
        #     "2. **置信度**：仅在动作有明确优势或劣势时给出确定结果，否则返回‘不确定’。\n"
        #     "3. 输出格式：\n"
        #     "```json\n"
        #     "{\n"
        #     "  \"best_action\": <动作编号,比如1> 或 \"uncertain\",\n"
        #     "  \"best_reason\": \"<简洁理由，不超50字符>\",\n"
        #     "  \"worst_action\": <动作编号，比如1> 或 \"uncertain\",\n"
        #     "  \"worst_reason\": \"<简洁理由，不超50字符>\"\n"
        #     "}\n"
        #     "```\n"
        #     "\n"
        #     "**评估原则（优先级顺序）**：\n"
        #     "1. **当前修复栈的栈底调整**：\n"
        #     "   - **若当前修复栈为空或栈底与目标不符，必须优先将目标栈底的块移动到该栈**。这是构建目标状态的基础。\n"
        #     "   - **只有栈底正确后**，才考虑调整上方块的顺序以完全匹配目标。\n"
        #     "2. **对其他栈的影响**：\n"
        #     "   - 避免将块移动到其他栈，导致其他栈的修复难度显著增加，除非必要。\n"
        #     "3. **逐步构建**：\n"
        #     "   - 优先选择动作，使当前修复栈的块内容和顺序向目标状态靠近。\n"
        #     "4. **避免冗余复杂性**：\n"
        #     "   - 避免将块移动到不必要的栈上，增加后续修复难度。\n"
        #     "\n"
        #     "**特别注意**：\n"
        #     f"- 优先考虑当前修复栈（{current_fix_stack}）的块内容和顺序完全匹配目标状态。\n"
        #     "- 其他栈可以作为临时‘桌面’使用，但应尽量减少对其结构的干扰。\n"
        #     "- 平衡对其他栈的影响，避免过度阻碍后续栈的修复。\n"
        #     "\n"
        # )
        prompt = (
            "你是一个Blocks World问题的专家。你的任务是评估一组可行动作，选出最优和最劣动作，帮助机器人手臂从当前状态接近目标状态。\n"
            "\n"
            "**问题背景**：\n"
            "Blocks World是一个积木堆叠问题，包含多个独立的垂直栈。每个栈由列表表示，**列表的第一个元素是栈底，最后一个元素是栈顶**。例如，[A, B, C]表示A在栈底，C在栈顶。机器人手臂一次只能执行一个动作：拿起某个栈顶的块并放到另一个栈顶。\n"
            "- 在构建目标状态时，**必须从栈底开始，逐层向上构建**。例如，对于目标状态[C, D, E]（底→顶），必须先放置C，再D，最后E。\n"
            "\n"
            "**核心规则**：\n"
            "1. 所有积木块都在栈内，每个栈是一个独立的垂直栈。\n"
            "2. 手臂只能拿起栈顶的块（即列表的最后一个元素），并将其放到另一个栈顶（即在另一个栈的列表末尾添加该块）。\n"
            "3. 栈具有先入后出（LIFO）特性：要访问栈中的某个块，必须先移走其上的所有块。\n"
            "4. 动作表示为 [i, j]，即将第 i 个栈的栈顶块移动到第 j 个栈的栈顶。\n"
            "5. 在移动块时，不仅要考虑当前栈的改进，还要评估该动作是否会阻碍未来将目标块移入当前修复栈或访问其他关键块。避免将块移动到未来需要访问的块之上。\n"
            "\n"
            "**当前修复栈**：\n"
            f"- **{current_fix_stack}**：当前状态为{current_fix_desc}，目标状态为{goal_fix_desc}。\n"
            f"- 当前问题：{get_stack_issue(current_fix_stack, fix_stack_curr, fix_stack_goal)}。\n"
            f"- 优先任务：{get_priority_task(current_fix_stack, fix_stack_curr, fix_stack_goal)}。\n"
            f"- **特别注意**：\n"
            f"- 在修复StackX时，首要任务是确保StackX的栈底是目标状态中的栈底块。在栈底放置正确之前，不应将任何其他块放入StackX。\n"
            f"- 在选择动作时，不仅要关注当前修复栈的局部改进，还要考虑该动作是否会阻碍后续将正确块移入当前修复栈。\n"
            
            "\n"
            "**任务**：\n"
            "根据当前状态、目标状态和可行动作列表，选出：\n"
            f"- **最优动作(优先级)**：优先选择能直接减少当前修复栈（{current_fix_stack}）与目标状态差异的动作。\n"
            "1. 移走当前修复栈顶部的错误块，优先移至空栈或无关栈。\n"
            "2. 将目标栈底的正确块移入当前修复栈。\n"
            "3. 若目标块被其他块阻挡，优先将阻挡块移动到其他栈（非当前修复栈）。\n"
            f"- **最劣动作（优先级）**：选择增加当前修复栈与目标状态差异或无助于减少差异的动作。\n"
            "1. 移走当前修复栈中已正确的块。\n"
            "2. 将不属于目标状态的块移入当前修复栈。\n"
            "3. 将块移动到未来需要访问的块之上。\n"
            "- 即使动作影响相近，也要根据次要标准（如对其他栈影响最小，或移动距离最短）选出最优和最劣动作。\n"
            "\n"
            "**当前状态**：\n"
            f"{current_desc}\n"
            "\n"
            "**目标状态**：\n"
            f"{goal_desc}\n"
            "\n"
            "**可行动作及其后果**：\n"
            f"{actions_text}\n"
            "**要求**：\n"
            "1. **动作质量**：\n"
            "   - **进展性**：动作应增加当前修复栈与目标状态的匹配块数，或减少栈顶错误块数。\n"
            "   - **简洁性**：避免显著增加不必要移动。\n"
            "2. **置信度**：根据量化指标（如匹配块数、栈顶错误块数）给出明确结果。\n"
            "3. 输出格式：\n"
            "```json\n"
            "{\n"
            "  \"best_action\": <动作编号,比如1>,\n"
            "  \"best_reason\": \"<简洁理由，不超50字符>\",\n"
            "  \"worst_action\": <动作编号，比如1>,\n"
            "  \"worst_reason\": \"<简洁理由，不超50字符>\"\n"
            "}\n"
            "```\n"
            "\n"
            "**评估原则（优先级顺序）**：\n"
            "1. **当前修复栈的栈底调整**：\n"
            "   - 若当前修复栈为空或栈底与目标不符，优先将目标栈底的块移动到该栈，或移走顶部错误块。\n"
            "   - 若栈底正确，优先移入目标状态的下一块。\n"
            "2. **移除阻挡块**：\n"
            "   - 若目标块被其他块阻挡，优先将阻挡块移动到其他栈（非当前修复栈），以便后续将目标块移动到正确位置。\n"
            "3. **对其他栈的影响**：\n"
            "   - 避免将块移动到其他栈，导致其他栈的修复难度显著增加，除非必要。\n"
            "4. **避免冗余复杂性**：\n"
            "   - 避免将块移动到不必要的栈上，增加后续修复难度。\n"
            "5. **避免阻碍未来移动**：\n"
            "   - 移走阻挡块时，优先选择空栈或不包含未来需要移动块的栈，避免将高层块放在低层块之上，除非必要。\n"
            "\n"
            "**量化指标**：\n"
            "- **栈底正确性**：动作后当前修复栈的栈底是否与目标状态的栈底相同。\n"
            "- **匹配块数**：动作后当前修复栈与目标状态的匹配块数量（从栈底开始）。\n"
            "- **栈顶错误块数**：动作后当前修复栈顶部不属于目标状态的块数量。\n"
            "- **未来移动阻碍程度**：动作后，移动的块被放置在未来需要移动的目标块之上，若有，视为次优动作。\n"
            "- 优先级顺序：1. 栈底正确性；2. 匹配块数增加；3. 栈顶错误块数减少。避免使栈底错误的动作。\n"
            "\n"
            "**特别注意**：\n"
            f"- 优先考虑当前修复栈（{current_fix_stack}）的块内容和顺序完全匹配目标状态。\n"
            "- 其他栈可作为临时‘桌面’使用，但应尽量减少对其结构的干扰。\n"
            "- 平衡对其他栈的影响，避免过度阻碍后续栈的修复。\n"
        )
        # print(prompt)
        try:
            response = chat_with_ai(prompt)
            matches = re.findall(r'```json\s*([\s\S]*?)\s*```', response, re.MULTILINE)
            if not matches:
                return {"best_action": "uncertain", "best_reason": "unknown", "worst_action": "uncertain",
                        "worst_reason": "unknown"}, actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                             fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
            try:
                return json.loads(matches[0]), actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                        fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
            except json.JSONDecodeError:
                return {"best_action": "uncertain", "best_reason": "unknown", "worst_action": "uncertain",
                        "worst_reason": "unknown"}, actions, get_stack_issue(current_fix_stack, fix_stack_curr,
                                                                             fix_stack_goal), get_priority_task(
                    current_fix_stack, fix_stack_curr, fix_stack_goal)
        except Exception as e:
            # 捕获 chat_with_ai 的异常并终止探索
            raise Exception(f"AI交互失败: {str(e)}")