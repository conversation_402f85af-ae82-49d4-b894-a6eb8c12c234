import random
import os
import math

def _calculate_actual_dirty_ratio(state):
    """
    内部辅助函数：精确计算给定状态的真实脏箱比例，并正确处理“级联效应”。
    """
    dirty_containers_count = 0
    total_containers = 0
    
    for stack in state.values():
        if not stack:
            continue
        total_containers += len(stack)
        is_dirty_below = False 
        for i in range(len(stack)):
            current_container = stack[i]
            is_self_dirty = False
            for j in range(i):
                lower_container = stack[j]
                if current_container > lower_container:
                    is_self_dirty = True
                    break
            
            if is_self_dirty or is_dirty_below:
                dirty_containers_count += 1
                is_dirty_below = True 
    
    if total_containers == 0:
        return 0.0
        
    return dirty_containers_count / total_containers


def generate_cpmpds_instance_v4(num_total_stacks, stack_height_limit, usage_rate, dirty_ratio, tolerance=0.05):
    """
    【V4 - 带自动修正】
    此版本在V3的基础上，增加了对“配置不可行”情况的自动修正能力。
    """
    # --- 1. 计算箱子数，并在超出容量时自动修正 ---
    num_active_stacks = num_total_stacks - 1
    if num_active_stacks < 1:
        raise ValueError("总堆位数必须至少为2")
        
    total_slots = num_total_stacks * stack_height_limit
    num_containers = int(total_slots * usage_rate)

    active_stack_capacity = num_active_stacks * stack_height_limit
    
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    # !!!                  *** 核心改动点 *** !!!
    # !!!  不再抛出错误，而是打印提示并自动修正箱子数量。       !!!
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    if num_containers > active_stack_capacity:
        print(f"  提示: 计算箱子数({num_containers})超出活动堆垛容量({active_stack_capacity})。将自动修正为 {active_stack_capacity}。")
        num_containers = active_stack_capacity

    if num_containers == 0:
        start_state = {f'Stack{i + 1}': [] for i in range(num_total_stacks)}
        return start_state, {}, []

    # --- 2. 创建一个“随机但干净”的初始布局 ---
    all_blocks = list(range(1, num_containers + 1))
    random.shuffle(all_blocks) 

    start_state = {f'Stack{i + 1}': [] for i in range(num_total_stacks)}
    empty_stack_name = f'Stack{num_total_stacks}'
    active_stack_names = [f'Stack{i + 1}' for i in range(num_active_stacks)]

    for block_id in all_blocks:
        eligible_stacks = [s for s in active_stack_names if len(start_state[s]) < stack_height_limit]
        if not eligible_stacks:
            print(f"Warning: No space left to place container {block_id}. Stopping placement.")
            break
        chosen_stack = random.choice(eligible_stacks)
        start_state[chosen_stack].append(block_id)
    
    for stack_name in active_stack_names:
        start_state[stack_name].sort(reverse=True)
    
    # --- 3. 采用混合策略，“迭代逼近”制造脏箱 ---
    target_lower_bound = dirty_ratio - tolerance
    current_ratio = _calculate_actual_dirty_ratio(start_state)
    attempts = 0
    max_attempts = num_containers * 20 

    while current_ratio < target_lower_bound and attempts < max_attempts:
        attempts += 1
        
        if random.random() < 0.7:
            source_stack_name = random.choice([s for s in active_stack_names if start_state[s]] or active_stack_names)
            if not source_stack_name or not start_state[source_stack_name]: continue
                
            source_stack = start_state[source_stack_name]
            block_to_move = source_stack.pop(random.randrange(len(source_stack)))
            
            eligible_targets = [s for s in active_stack_names if len(start_state[s]) < stack_height_limit]
            if not eligible_targets: 
                start_state[source_stack_name].append(block_to_move) #放不下了，放回去
                continue
            target_stack_name = random.choice(eligible_targets)
            start_state[target_stack_name].append(block_to_move)

        else:
            eligible_stacks = [s for s in active_stack_names if len(start_state[s]) >= 2]
            if not eligible_stacks: continue
            
            stack_to_mess_up_name = random.choice(eligible_stacks)
            stack_to_mess_up = start_state[stack_to_mess_up_name]
            idx = random.randint(0, len(stack_to_mess_up) - 2)
            stack_to_mess_up[idx], stack_to_mess_up[idx+1] = stack_to_mess_up[idx+1], stack_to_mess_up[idx]
        
        current_ratio = _calculate_actual_dirty_ratio(start_state)
        
    if attempts >= max_attempts and current_ratio < target_lower_bound:
        print(f"Warning: Reached max attempts. Final dirty ratio is {current_ratio:.2%}, target range was [{target_lower_bound:.2%}, {dirty_ratio+tolerance:.2%}]")

    # --- 4. 自动生成 G_canonical 和 fix_order ---
    target_stack_name = 'Stack1'
    g_canonical = {stack_name: [] for stack_name in start_state.keys()}
    all_placed_blocks = [item for sublist in start_state.values() if sublist for item in sublist]
    g_canonical[target_stack_name] = sorted(all_placed_blocks, reverse=True)
    stack_names_list = list(start_state.keys())
    fix_order = [target_stack_name] + [s for s in stack_names_list if s != target_stack_name and s != empty_stack_name] + [empty_stack_name]

    return start_state, g_canonical, fix_order

# ==============================================================================
# 文件保存和主循环逻辑
# ==============================================================================
def save_instance_to_file(instance_data, file_path):
    with open(file_path, 'w') as f:
        f.write(f"Start State: {instance_data['start_state']}\n")
        f.write(f"G_canonical: {instance_data['g_canonical']}\n")
        f.write(f"Fix Order: {instance_data['fix_order']}\n")
        f.write("-" * 30 + "\n")

# --- 配置参数 ---
num_total_stacks_options = [4, 5, 6]
stack_height_limit = 5
usage_rate_options = [0.60, 0.70, 0.80] # 增加一些更容易触发修正的配置
dirty_ratio_options = [0.50, 0.60, 0.70]
instances_per_config = 30

output_dir = "cpmpds_datasets"
os.makedirs(output_dir, exist_ok=True)

print("--- 开始生成【 - 带自动修正】CPMPDS 数据集 ---")
total_instances_generated = 0

for nts in num_total_stacks_options:
    for ur in usage_rate_options:
        for dr in dirty_ratio_options:
            config_name = f"TotalS{nts}_U{int(ur*100)}_D{int(dr*100)}"
            config_output_dir = os.path.join(output_dir, config_name)
            os.makedirs(config_output_dir, exist_ok=True)
            print(f"\n生成配置: 总堆位数={nts}, 使用率={ur*100}%, 目标脏箱比例={dr*100}% (容忍度 +/-5%)")

            # **修正点**: 移除了 try...except 块，因为函数内部已处理该情况
            for i in range(instances_per_config):
                start_state, g_canonical, fix_order = generate_cpmpds_instance_v4(
                    num_total_stacks=nts,
                    stack_height_limit=stack_height_limit,
                    usage_rate=ur,
                    dirty_ratio=dr
                )

                instance_data = {
                    'start_state': start_state,
                    'g_canonical': g_canonical,
                    'fix_order': fix_order
                }
                file_name = f"instance_{i+1}.txt"
                file_path = os.path.join(config_output_dir, file_name)
                save_instance_to_file(instance_data, file_path)
            
            print(f"  完成该配置下 {instances_per_config} 个实例的生成。")
            total_instances_generated += instances_per_config

print(f"\n--- 数据集生成完成！共生成 {total_instances_generated} 个实例。 ---")
print(f"所有实例保存在 '{output_dir}' 目录下。")